const { Schema, model } = require("mongoose");

const waazVenueSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
    },
    status: {
      type: String,
      required: true,
      trim: true,
      enum: ["active", "inactive", "cancel"],
      default: "active",
    },
    approvalStatus: {
      type: String,
      required: true,
      trim: true,
      enum: ["under-review", "approved"],
      default: "under-review",
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: false,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: false,
    },
    mawaidVenues: [{
      type: Schema.Types.ObjectId,
      ref: "MawaidVenue",
      required: false,
    }],
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: true,
    },
    waazVenueTypeID: {
      type: Schema.Types.ObjectId,
      ref: "WaazVenueType",
      required: true,
    },
    waazVenueSuitabilityID: {
      type: Schema.Types.ObjectId,
      ref: "WaazVenueSuitability",
      required: true,
    },
    waazVenuePriorityID: {
      type: Schema.Types.ObjectId,
      ref: "WaazVenuePriority",
      required: true,
    },
    waazVenuPolylineArea: {
      type: Number,
      required: true,
    },
    waazSeatingCapacity: {
      type: Number,
      required: false,
    },
    finalizedWaazSeatingCapacity: {
      type: Number,
      required: false,
    },
    remarks: {
      type: String,
      required: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const WaazVenue = model("WaazVenue", waazVenueSchema);

module.exports = { WaazVenue };
