const { Schema, model } = require("mongoose");

const kgGroupSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unqiue: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    isSystem: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const KGGroup = model("KGGroup", kgGroupSchema);

module.exports = { KGGroup };
