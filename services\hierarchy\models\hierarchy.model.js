const { Schema, model } = require("mongoose");

const usersSchema = new Schema(
  {
    KGType: {
      type: String,
    },
    KGTypeID: {
      type: Schema.Types.ObjectId,
      ref: "KGType",
    },
    KGGroup: {
      type: String,
    },
    KGGroupID: {
      type: Schema.Types.ObjectId,
      ref: "KGGroup",
    },
    name: {
      type: String,
      required: false,
      trim: true,
    },
    LDName: {
      type: String,
      required: false,
      trim: true,
    },
    ITSID: {
      type: String,
      required: false,
      trim: true,
    },
    email: {
      type: String,
      required: false,
      trim: true,
    },
    phone: {
      type: String,
      required: false,
      trim: true,
    },
    whatsapp: {
      type: String,
      required: false,
      trim: true,
    },
    age: {
      type: String,
      required: false,
      trim: true,
    },
    gender: {
      type: String,
      required: false,
      trim: true,
    },
    KGID: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
    },
    departmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
    },
    hierarchyPositionID: {
      type: Schema.Types.ObjectId,
      ref: "HiearchyPosition",
    },
    isInternationalPlugin: {
      type: Boolean,
    },
  },
  { _id: false }
);

const listViewSchema = new Schema(
  {
    isOpen: {
      type: Boolean,
      default: false,
    },
    position: {
      type: String,
      required: true,
    },
    positionID: {
      type: Schema.Types.ObjectId,
      ref: "HierarchyPosition",
      required: true,
    },
    positionDetails: {
      originalName: {
        type: String,
      },
      originalAlias: {
        type: String,
      },
      parentType: {
        type: String,
      },
      isZonal: {
        type: Boolean,
      },
      isDepartmental: {
        type: Boolean,
      },
      isVisibleForArazCityUser: {
        type: Boolean,
      },
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
    },
    arazCityZoneLDName: {
      type: String
    },
    arazCityZoneName: {
      type: String
    },
    departmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
    },
    departmentLDName: {
      type: String
    },
    departmentName: {
      type: String
    },
    assignedKGs: [usersSchema],
    count: {
      type: Number,
    },
    countRecommendation: {
      type: Number,
      default: 1,
    },
    weightage: {
      type: Number,
      default: 1,
    },
    children: [{ type: Schema.Types.Mixed }],
  },
  { _id: false }
);
listViewSchema.add({
  children: [listViewSchema],
});

const userTreeSchema = new Schema(
  {
    users: [
      {
        name: {
          type: String,
        },
        LDName: {
          type: String,
        },
        ITSID: {
          type: String,
        },
        logo: {
          type: String,
        },
        position: {
          type: String,
        },
        KGTypeColor: {
          type: String,
        },
        KGType: {
          type: String,
        },
        gender: {
          type: String,
        },
        isInternationalPlugin: {
          type: Boolean,
        },
      },
    ],
    children: [{ type: Schema.Types.Mixed }],
  },
  { _id: false }
);
userTreeSchema.add({
  children: [userTreeSchema],
});

const departmentTreeChildrenSchema = new Schema(
  {
    users: [
      {
        name: {
          type: String,
        },
        LDName: {
          type: String,
        },
        ITSID: {
          type: String,
        },
        logo: {
          type: String,
        },
        position: {
          type: String,
        },
        KGTypeColor: {
          type: String,
        },
        isInternationalPlugin: {
          type: Boolean,
        },
        KGType: {
          type: String,
        },
        gender: {
          type: String,
        },
      },
    ],
    children: { type: Schema.Types.Mixed },
  },
  { _id: false }
);
departmentTreeChildrenSchema.add({
  children: departmentTreeChildrenSchema,
});
const departmentTreeSchema = new Schema(
  {
    name: {
      type: String,
    },
    LDName: {
      type: String,
    },
    children: departmentTreeChildrenSchema,
  },
  { _id: false }
);

const hierarchySchema = new Schema(
  {
    isGenerating: {
      type: Boolean,
      default: false,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    departmental: listViewSchema,
    zonal: listViewSchema,
    userTree: userTreeSchema,
    departmentTree: [departmentTreeSchema],
    totalWeightages: {
      type: Number,
    },
    hierarchyWeightages: {
      type: Number,
    },
    pendingPercentage: {
      type: Number,
    },
  },
  { timestamps: true }
);

const Hierarchy = model("Hierarchy", hierarchySchema);

module.exports = { Hierarchy };
