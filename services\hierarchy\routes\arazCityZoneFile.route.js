const express = require("express");
const router = express.Router();
const {
  uploadFile,
  updateFileStatus,
  getFiles,
  getMasterFiles,
  getDownloadURL,
  deleteArazCityFile,
} = require("../controllers/arazCityZoneFile.controller");
const { upload } = require("../../../middlewares/multer.middleware");
const { validate } = require("../../../middlewares/validation.middleware");
const {
  uploadFileSchema,
  updateFileStatusSchema,
  getFilesSchema,
  getMasterFilesSchema,
  getDownloadURLSchema,
  deleteArazCityZoneFileSchema,
} = require("../validations/arazCityZoneFile.validation");

const fileUpload = upload("araz_city_zone_files");

// Routes with validation
router.post(
  "/upload",
  fileUpload.single("file"),
  validate(uploadFileSchema, "body"),
  uploadFile
);

router.put(
  "/update/status",
  validate(updateFileStatusSchema, "body"),
  updateFileStatus
);

router.post("/get", validate(getFilesSchema, "body"), getFiles);

router.get(
  "/get-master-files",
  validate(getMasterFilesSchema, "query"),
  getMasterFiles
);

router.post(
  "/get/download-url",
  validate(getDownloadURLSchema, "body"),
  getDownloadURL
);

router.delete(
  "/delete/:id",
  validate(deleteArazCityZoneFileSchema, "params"),
  deleteArazCityFile
);

module.exports = router;
