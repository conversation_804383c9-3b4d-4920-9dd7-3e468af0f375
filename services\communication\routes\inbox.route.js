const { upload } = require("../../../middlewares/multer.middleware")
const { validate, validateMultiple } = require("../../../middlewares/validation.middleware")
const { getMessages, getMessage, addMessageReply, addAttachments, getAttachment, deleteMessage, sendWorkPackageNotification, getMessageRecipientsByMessageID } = require("../controllers/message.controller")
const { getMessagesSchema, getMessageSchema, addMessageReplySchema, getAttachmentSchema, deleteMessageSchema } = require("../validations/message.validation")

const router = require("express").Router()

router.post("/get/message", validate(getMessagesSchema, ["body"]), getMessages)

router.post("/get/message/:id", validateMultiple(getMessageSchema, ["params", "body"]), getMessage)

router.put("/get/reply/:id", validateMultiple(addMessageReplySchema, ["params", "body"]), addMessageReply)

router.patch("/get/delete/message/:id", validate(deleteMessageSchema, ["params", "body"]), deleteMessage)

router.post("/get/attachment", upload("communication").array("files"), addAttachments);

router.post("/get/download-url", validate(getAttachmentSchema, "body"), getAttachment);

router.get("/get/message-recipients/:id", validateMultiple(getMessageSchema, ["params"]), getMessageRecipientsByMessageID)

module.exports = router