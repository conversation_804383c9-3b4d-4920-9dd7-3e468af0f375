const constants = require("../../../constants");
const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const {
  getHofWiseMemberStats,
  getJamiatWiseHofData,
  getJamaatWiseHofData,
} = require("../../../utils/ITSHelper.util");
const {
  FETCH,
  NOT_FOUND,
  CUSTOM_ERROR,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
} = require("../../../utils/message.util");
const {
  ArazCityZone,
  Jamaat,
  Jamiat,
  ArazCity,
} = require("../../hierarchy/models");
const { WaazVenue } = require("../models");
const { MasterHofRecords } = require("../models/masterHofRecords");
const { MawaidVenue } = require("../models/mawaidVenue.model");
const { ZoneMapping } = require("../models/zoneMapping.model");

const getAllZoneMappings = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const cmzZoneID = constants.ARAZ_CITY_ZONES.CMZ[0];

  const arazCityZones = await ArazCityZone.find({
    $or: [{ arazCityID }, { _id: cmzZoneID }],
  })
    .lean()
    .sort({ createdAt: -1 });

  const arazCityZoneMappingData = await Promise.all(
    arazCityZones.map(async (arazCityZone) => {
      const zoneMappings = await ZoneMapping.find({
        arazCityZoneID: arazCityZone._id,
        miqaatID,
      }).lean();

      const muqimeenCount = zoneMappings.reduce(
        (sum, mapping) =>
          sum +
          mapping.gents +
          mapping.ladies +
          mapping.childrenDikrao +
          mapping.childrenDikrio,
        0
      );

      const waazVenues = await WaazVenue.find({
        arazCityZoneID: arazCityZone._id,
      }).lean();

      const capacity = waazVenues.reduce(
        (sum, venue) =>
          sum +
          (venue.finalizedWaazSeatingCapacity ||
            venue.waazSeatingCapacity ||
            0),
        0
      );

      const mehmanCount = capacity - muqimeenCount;

      return {
        zoneName: arazCityZone.name,
        capacity,
        muqimeenCount,
        mehmanCount,
        ...arazCityZone,
      };
    })
  );

  return apiResponse(FETCH, "Zone Mappings", arazCityZoneMappingData, res);
});

const getSingleZoneMappingReport = apiHandler(async (req, res) => {
  const { arazCityZoneID } = req.params;

  const zone = await ArazCityZone.findOne({ _id: arazCityZoneID, status: true }).lean();
  if (!zone) {
    return apiResponse(NOT_FOUND, "Zone not found", null, res);
  }

  const zoneMappings = await ZoneMapping.find({ arazCityZoneID }).lean();

  let totalMardo = 0;
  let totalBairao = 0;
  let totalDikrao = 0;
  let totalDikrio = 0;

  zoneMappings.forEach((mapping) => {
    totalMardo += mapping.gents || 0;
    totalBairao += mapping.ladies || 0;
    totalDikrao += mapping.childrenDikrao || 0;
    totalDikrio += mapping.childrenDikrio || 0;
  });

  const totalGairBaligh = totalDikrao + totalDikrio;
  const muqimeenCount = totalMardo + totalBairao + totalGairBaligh;

  const mawaidVenues = await MawaidVenue.find({ arazCityZoneID })
    .populate("mawaidVenueTypeID")
    .populate("mawaidVenueSuitabilityID")
    .lean();

  const waazVenues = await WaazVenue.find({ arazCityZoneID })
    .populate("waazVenueTypeID")
    .populate("waazVenueSuitabilityID")
    .populate("waazVenuePriorityID")
    .lean();

  const totalCapacity =
    mawaidVenues.reduce((sum, venue) => sum + (venue.capacity || 0), 0) +
    waazVenues.reduce(
      (sum, venue) => sum + (venue.waazSeatingCapacity || 0),
      0
    );

  const remainingCapacity = totalCapacity - muqimeenCount;

  const formattedMawaidVenues = mawaidVenues.map((venue) => ({
    venueName: venue.name,
    venueType: venue.mawaidVenueTypeID?.name || "Unknown",
    venueSuitability: venue.mawaidVenueSuitabilityID?.name || "Unknown",
    noOfThok: venue.numberOfThoks || 0,
    thalPerThok: venue.plannedThoks || 0,
    capacity: venue.finalCapacity || venue.estimatedCapacity || 0,
  }));

  const formattedWaazVenues = waazVenues.map((venue) => ({
    venueName: venue.name,
    venueType: venue.waazVenueTypeID?.name || "Unknown",
    venueSuitability: venue.waazVenueSuitabilityID?.name || "Unknown",
    venuePriority: venue.waazVenuePriorityID?.name || "Unknown",
    capacity:
      venue.finalizedWaazSeatingCapacity || venue.waazSeatingCapacity || 0,
  }));

  const zoneData = {
    zoneName: zone.name,
    demographics: {
      totalMardo,
      totalBairao,
      totalDikrao,
      totalDikrio,
      totalGairBaligh,
      total: muqimeenCount,
    },
    capacity: {
      existingCapacity: totalCapacity,
      muqimeenCount,
      remaining: remainingCapacity,
    },
    venues: {
      mawaidVenues: formattedMawaidVenues,
      waazVenues: formattedWaazVenues,
    },
  };

  return apiResponse(FETCH, "Zone Mapping Details", zoneData, res);
});

const getSingleZoneMapping = apiHandler(async (req, res) => {
  const { arazCityZoneID } = req.params;

  const zone = await ArazCityZone.findOne({ _id: arazCityZoneID, status: true }).lean();
  if (!zone) {
    return apiResponse(NOT_FOUND, "Zone", null, res);
  }
  const zoneMappings = await ZoneMapping.find({ arazCityZoneID })
    .populate("jamiatID")
    .populate("jamaatID")
    .lean();

  let totalGents = 0;
  let totalLadies = 0;
  let totalChildrenDikrao = 0;
  let totalChildrenDikrio = 0;

  zoneMappings.forEach((mapping) => {
    totalGents += mapping.gents || 0;
    totalLadies += mapping.ladies || 0;
    totalChildrenDikrao += mapping.childrenDikrao || 0;
    totalChildrenDikrio += mapping.childrenDikrio || 0;
  });

  const totalChildren = totalChildrenDikrao + totalChildrenDikrio;
  const totalMuqimeen = totalGents + totalLadies + totalChildren;

  const nonHofMappings = zoneMappings.filter(
    (mapping) => mapping.addedByHof === false
  );

  const jamaatIds = [
    ...new Set(
      nonHofMappings
        .filter((mapping) => mapping.jamaatID && mapping.jamaatID._id)
        .map((mapping) => mapping.jamaatID._id.toString())
    ),
  ];

  const jamiatId = [
    ...new Set(
      nonHofMappings
        .filter((mapping) => mapping.jamiatID && mapping.jamiatID._id)
        .map((mapping) => mapping.jamiatID._id.toString())
    ),
  ];

  const response = {
    zone,
    mapping: zoneMappings,
    jamaatIds,
    jamiatId,
    demographics: {
      totalGents,
      totalLadies,
      totalChildrenDikrao,
      totalChildrenDikrio,
      totalChildren,
      totalMuqimeen,
    },
  };

  return apiResponse(FETCH, "Zone Mapping Details", response, res);
});

const deleteZoneMapping = apiHandler(async (req, res) => {
  const { id } = req.params;

  const deletedZoneMapping = await ZoneMapping.findByIdAndDelete(id);

  if (!deletedZoneMapping) {
    return apiError(NOT_FOUND, "Zone Mapping", null, res);
  }

  return apiResponse(DELETE_SUCCESS, "Zone Mapping", null, res);
});

const getHouseholds = apiHandler(async (req, res) => {
  const { HOFITSID, jamiatID, jamaatID, sector, subSector } = req.body;

  if (HOFITSID) {
    const existingHof = await MasterHofRecords.findOne({
      HOF_ITS_ID: HOFITSID,
    });
    if (existingHof) {
      return apiResponse(FETCH, "Household", existingHof, res);
    }

    const household = await getHofWiseMemberStats(HOFITSID);

    if (!household || household.length === 0) {
      return apiError(NOT_FOUND, "Household", null, res);
    }

    const householdData = household[0];

    const externalJamiatID = householdData.Jamiaat_ID;
    const externalJamaatID = householdData.Jamaat_ID;

    const jamaat = await Jamaat.findOne({
      ITSJamiatID: externalJamiatID,
      ITSID: externalJamaatID,
    });
    const jamiat = await Jamiat.findOne({
      ITSID: externalJamiatID,
    });

    if (!jamaat || !jamiat) {
      return apiError(
        NOT_FOUND,
        "Jamaat or Jamiat not found in database",
        null,
        res
      );
    }

    const formattedHousehold = {
      jamiatID: jamiat._id,
      jamaatID: jamaat._id,
      HOF_ITS_ID: householdData.HOF_ID,
      totalCount: householdData.Total_Count,
      mardoCount: householdData.Mardo_Count,
      bairoCount: householdData.Bairo_Count,
      gairBalighDikraCount: householdData.Gair_Baligh_Dikra_Count,
      gairBalighDikriCount: householdData.Gair_Baligh_Dikri_Count,
      sector: householdData.Sector,
      subSector: householdData.Sub_Sector,
    };

    const savedHousehold = await MasterHofRecords.create(formattedHousehold);
    return apiResponse(FETCH, "Household", savedHousehold, res);
  } else if (jamiatID || jamaatID || sector || subSector) {
    let householdData = [];

    let query = {};
    if (jamiatID) query.jamiatID = jamiatID;
    if (jamaatID) query.jamaatID = jamaatID;
    if (sector) query.sector = sector;
    if (subSector) query.subSector = subSector;

    const existingRecords = await MasterHofRecords.find(query);
    if (existingRecords && existingRecords.length > 0) {
      return apiResponse(FETCH, "Households", existingRecords, res);
    }

    if (jamiatID) {
      const jamiat = await Jamiat.findById(jamiatID);
      if (!jamiat) {
        return apiError(NOT_FOUND, "Jamiat not found", null, res);
      }

      householdData = await getJamiatWiseHofData(jamiat.ITSID);
    } else if (jamaatID) {
      const jamaat = await Jamaat.findById(jamaatID);
      if (!jamaat) {
        return apiError(NOT_FOUND, "Jamaat not found", null, res);
      }

      householdData = await getJamaatWiseHofData(jamaat.ITSID);
    } else if (sector || subSector) {
      return apiError(
        CUSTOM_ERROR,
        "Fetching by sector or subSector is not supported",
        null,
        res
      );
    }

    if (!householdData || householdData.length === 0) {
      return apiError(NOT_FOUND, "No household data found", null, res);
    }

    const savedRecords = [];
    const processedHofIds = new Set();

    for (const data of householdData) {
      if (processedHofIds.has(data.HOF_ID)) continue;
      processedHofIds.add(data.HOF_ID);

      const existingRecord = await MasterHofRecords.findOne({
        HOF_ITS_ID: data.HOF_ID,
      });
      if (existingRecord) {
        savedRecords.push(existingRecord);
        continue;
      }

      try {
        const externalJamiatID = data.Jamiaat_ID;
        const externalJamaatID = data.Jamaat_ID;

        const jamaat = await Jamaat.findOne({
          ITSJamiatID: externalJamiatID,
          ITSID: externalJamaatID,
        });
        const jamiat = await Jamiat.findOne({
          ITSID: externalJamiatID,
        });

        if (!jamaat || !jamiat) {
          console.warn(
            `Skipping record with HOF_ID ${data.HOF_ID}: Jamaat or Jamiat not found`
          );
          continue;
        }

        const formattedData = {
          jamiatID: jamiat._id,
          jamaatID: jamaat._id,
          HOF_ITS_ID: data.HOF_ID,
          totalCount: data.Total_Count,
          mardoCount: data.Mardo_Count,
          bairoCount: data.Bairo_Count,
          gairBalighDikraCount: data.Gair_Baligh_Dikra_Count,
          gairBalighDikriCount: data.Gair_Baligh_Dikri_Count,
          sector: data.Sector,
          subSector: data.Sub_Sector,
        };

        const savedRecord = await MasterHofRecords.create(formattedData);
        savedRecords.push(savedRecord);
      } catch (error) {
        console.error(
          `Error processing record with HOF_ID ${data.HOF_ID}:`,
          error
        );
      }
    }

    if (savedRecords.length === 0) {
      return apiError(
        CUSTOM_ERROR,
        "No valid records could be saved",
        null,
        res
      );
    }

    return apiResponse(FETCH, "Households", savedRecords, res);
  } else {
    return apiError(
      CUSTOM_ERROR,
      "Either HOFITSID or parameters (jamiatID, jamaatID, sector, subSector) are required",
      null,
      res
    );
  }
});

const getArazCityWiseJamaatAndJamiat = apiHandler(async (req, res) => {
  const { arazCityID } = req.body;

  try {
    const arazCity = await ArazCity.findById(arazCityID).select(
      "jamaats jamiats"
    );

    if (!arazCity) {
      return apiError(NOT_FOUND, "ArazCity", null, res);
    }

    const jamiats = await Jamiat.find({
      _id: { $in: arazCity.jamiats },
    });

    const nestedResult = [];

    for (const jamiat of jamiats) {
      const jamiatObj = {
        ...jamiat.toObject(),
        jamaats: [],
      };

      if (arazCity.jamaats && arazCity.jamaats.includes("all")) {
        const allJamaatsForJamiat = await Jamaat.find({
          jamiatID: jamiat._id,
        });

        jamiatObj.jamaats = allJamaatsForJamiat;
      } else if (arazCity.jamaats && arazCity.jamaats.length > 0) {
        const specificJamaats = await Jamaat.find({
          _id: { $in: arazCity.jamaats.filter((id) => id !== "all") },
          jamiatID: jamiat._id,
        });

        jamiatObj.jamaats = specificJamaats;
      }

      nestedResult.push(jamiatObj);
    }

    return apiResponse(FETCH, "Jamaat and Jamiat", nestedResult, res);
  } catch (error) {
    console.error("Error fetching jamaat and jamiat:", error);
    return apiError(SERVER_ERROR, error.message, null, res);
  }
});

const addZoneMappingByHof = apiHandler(async (req, res) => {
  const { arazCityZoneID, arazCityID, miqaatID, mappings } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  // Check if zone exists
  const arazCityZone = await ArazCityZone.findById(arazCityZoneID);
  if (!arazCityZone) {
    return apiResponse(NOT_FOUND, "ArazCityZone not found", null, res);
  }

  // Check if sync is already in progress
  if (arazCityZone.isSynchingZoneMapping) {
    return apiResponse(
      CUSTOM_ERROR,
      "Sync is already in progress for this zone. Please wait for it to complete.",
      null,
      res
    );
  }

  // Mark zone as syncing
  await ArazCityZone.findByIdAndUpdate(
    arazCityZoneID,
    { isSynchingZoneMapping: true },
    { new: true }
  );

  // Start background processing
  processMappingsByHOFInBackground(
    arazCityID,
    arazCityZoneID,
    miqaatID,
    mappings,
    req
  );

  // Return immediately to client
  return apiResponse(
    FETCH,
    "We are syncing zone mapping in background, please wait for few minutes and then check again",
    null,
    res
  );
});

async function processMappingsByHOFInBackground(
  arazCityID,
  arazCityZoneID,
  miqaatID,
  mappings,
  req
) {
  try {
    console.log(`Starting background sync for zone ${arazCityZoneID}`);
    const uniqueHOFIDs = new Set(mappings);
    console.log(
      `Original mapping size: ${mappings.length}, After removing duplicates: ${uniqueHOFIDs.size}`
    );
    const existingMappings = await ZoneMapping.find({
      arazCityID,
      arazCityZoneID,
      miqaatID,
      HOFITSID: { $in: Array.from(uniqueHOFIDs) },
    }).lean();

    const existingMappingsMap = new Map();
    existingMappings.forEach((mapping) => {
      existingMappingsMap.set(mapping.HOFITSID, mapping);
    });

    console.log(`Found ${existingMappings.length} existing mappings`);

    let totalProcessed = 0;
    let totalSuccess = 0;
    let totalErrors = 0;
    let totalUpdated = 0;
    let totalAdded = 0;

    const jamiatCache = new Map();
    const jamaatCache = new Map();

    for (const HOFITSID of uniqueHOFIDs) {
      try {
        const hofDataArray = await getHofWiseMemberStats(HOFITSID);

        if (!hofDataArray || hofDataArray.length === 0) {
          console.log(`No data found for HOFITSID: ${HOFITSID}`);
          continue;
        }

        const hofData = hofDataArray[0];
        console.log(`Processing HOF: ${HOFITSID}`);

        const externalJamiatID = hofData.Jamiaat_ID;
        const externalJamaatID = hofData.Jamaat_ID;

        let jamiat, jamaat;

        if (jamiatCache.has(externalJamiatID)) {
          jamiat = jamiatCache.get(externalJamiatID);
        } else {
          jamiat = await Jamiat.findOne({ ITSID: externalJamiatID }).lean();
          if (jamiat) jamiatCache.set(externalJamiatID, jamiat);
        }

        const jamaatKey = `${externalJamiatID}-${externalJamaatID}`;
        if (jamaatCache.has(jamaatKey)) {
          jamaat = jamaatCache.get(jamaatKey);
        } else {
          jamaat = await Jamaat.findOne({
            ITSJamiatID: externalJamiatID,
            ITSID: externalJamaatID,
          }).lean();
          if (jamaat) jamaatCache.set(jamaatKey, jamaat);
        }

        // Check if mapping already exists
        const existingMapping = existingMappingsMap.get(HOFITSID);

        const mappingData = {
          arazCityID,
          arazCityZoneID,
          miqaatID,
          HOFITSID,
          jamiatID: jamiat?._id,
          jamaatID: jamaat?._id,
          sector: hofData.Sector,
          subSector: hofData.Sub_Sector,
          gents: hofData.Mardo_Count || 0,
          ladies: hofData.Bairo_Count || 0,
          childrenDikrao: hofData.Gair_Baligh_Dikra_Count || 0,
          childrenDikrio: hofData.Gair_Baligh_Dikri_Count || 0,
          addedByHof: true,
        };

        if (existingMapping) {
          // Update existing record
          await ZoneMapping.findByIdAndUpdate(
            existingMapping._id,
            mappingData,
            { new: true }
          );
          totalUpdated++;
        } else {
          // Create new record
          const zoneMapping = new ZoneMapping(mappingData);
          await zoneMapping.save();
          totalAdded++;
        }

        totalSuccess++;
      } catch (error) {
        console.error(`Error processing HOFITSID ${HOFITSID}:`, error);
        totalErrors++;
      }

      totalProcessed++;
      if (totalProcessed % 10 === 0) {
        console.log(`Processed ${totalProcessed}/${uniqueHOFIDs.size} HOFs`);
      }
    }

    console.log(`Background sync completed for zone ${arazCityZoneID}`);
    console.log(
      `Total: ${totalProcessed}, Successful: ${totalSuccess} (Updated: ${totalUpdated}, Added: ${totalAdded}), Failed: ${totalErrors}`
    );
  } catch (error) {
    console.error(
      `Error in background processing for zone ${arazCityZoneID}:`,
      error
    );
  } finally {
    await ArazCityZone.findByIdAndUpdate(
      arazCityZoneID,
      {
        isSynchingZoneMapping: false,
        lastSynchedDate: new Date(),
        lastSynchedBy: req?.user._id,
      },
      { new: true }
    );
    console.log(`Reset sync flag for zone ${arazCityZoneID}`);
  }
}

const addZoneMappingByJamaat = apiHandler(async (req, res) => {
  const { arazCityZoneID, arazCityID, miqaatID, jamiatID, jamaatIDs } =
    req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  } 
  const arazCityZone = await ArazCityZone.findById(arazCityZoneID);
  if (!arazCityZone) {
    return apiResponse(NOT_FOUND, "ArazCityZone not found", null, res);
  }
  if (arazCityZone.isSynchingZoneMapping) {
    return apiResponse(
      CUSTOM_ERROR,
      "Sync is already in progress for this zone. Please wait for it to complete.",
      null,
      res
    );
  }
  await ArazCityZone.findByIdAndUpdate(
    arazCityZoneID,
    { isSynchingZoneMapping: true },
    { new: true }
  );
  processMappingsByJamaatInBackground(
    arazCityID,
    arazCityZoneID,
    miqaatID,
    jamiatID,
    jamaatIDs,
    req
  );

  return apiResponse(
    FETCH,
    "We are syncing zone mapping in background, please wait for few minutes and then check again",
    null,
    res
  );
});
async function processMappingsByJamaatInBackground(
  arazCityID,
  arazCityZoneID,
  miqaatID,
  jamiatID,
  jamaatIDs,
  req
) {
  try {
    const jamiatCache = new Map();
    const jamaatCache = new Map();
    for (const jamaatID of jamaatIDs) {
      await ZoneMapping.deleteMany({
        arazCityZoneID,
        arazCityID,
        miqaatID,
        addedByHof: false,
      });

      let jamiat, jamaat;

      if (jamiatCache.has(jamiatID)) {
        jamiat = jamiatCache.get(jamiatID);
      } else {
        jamiat = await Jamiat.findById(jamiatID).lean();
        if (jamiat) jamiatCache.set(jamiatID, jamiat);
      }
      if (jamaatCache.has(jamaatID)) {
        jamaat = jamaatCache.get(jamaatID);
      } else {
        jamaat = await Jamaat.findById(jamaatID).lean();
        if (jamaat) jamaatCache.set(jamaatID, jamaat);
      }

      const hofDatas = await getJamaatWiseHofData(jamaat.ITSID);
      if (!hofDatas || hofDatas.length === 0) {
        console.log(`No data found for Jamaat: ${jamaat.name}`);
        continue;
      }
      console.log(
        `Data found for Jamaat: ${jamaat.name} , count (${hofDatas.length})`
      );

      let totalProcessed = 0;
      let totalSuccess = 0;
      let totalUpdated = 0;
      let totalAdded = 0;
      let totalErrors = 0;

      for (const hofData of hofDatas) {
        try {
          const mappingData = {
            arazCityID,
            arazCityZoneID,
            miqaatID,
            HOFITSID: hofData.HOF_ID,
            jamiatID: jamiat?._id,
            jamaatID: jamaat?._id,
            sector: hofData.Sector,
            subSector: hofData.Sub_Sector,
            gents: hofData.Mardo_Count || 0,
            ladies: hofData.Bairo_Count || 0,
            childrenDikrao: hofData.Gair_Baligh_Dikra_Count || 0,
            childrenDikrio: hofData.Gair_Baligh_Dikri_Count || 0,
          };

          const existingMapping = await ZoneMapping.findOne({
            arazCityZoneID,
            arazCityID,
            miqaatID,
            HOFITSID: hofData.HOF_ID,
          });
          if (existingMapping) {
            existingMapping.addedByHof = false;
            existingMapping.jamiatID = jamiat?._id;
            existingMapping.jamaatID = jamaat?._id;
            existingMapping.sector = hofData.Sector;
            existingMapping.subSector = hofData.Sub_Sector;
            existingMapping.gents = hofData.Mardo_Count || 0;
            existingMapping.ladies = hofData.Bairo_Count || 0;
            existingMapping.childrenDikrao =
              hofData.Gair_Baligh_Dikra_Count || 0;
            existingMapping.childrenDikrio =
              hofData.Gair_Baligh_Dikri_Count || 0;
            await existingMapping.save();
            totalUpdated++;
            totalSuccess++;
            continue;
          } else {
            const zoneMapping = new ZoneMapping(mappingData);
            await zoneMapping.save();
            totalAdded++;
          }

          totalSuccess++;
        } catch (error) {
          console.error(`Error processing HOFITSID ${hofData.HOF_ID}:`, error);
          totalErrors++;
        }

        totalProcessed++;
        console.log(
          `Processed ${totalProcessed} HOFs for Jamaat ${jamaatID} in zone ${arazCityZoneID}. Success: ${totalSuccess}, Updated: ${totalUpdated}, Added: ${totalAdded}, Errors: ${totalErrors}`
        );
      }
    }
  } catch (error) {
    console.error(
      `Error in background processing for zone ${arazCityZoneID}:`,
      error
    );
  } finally {
    await ArazCityZone.findByIdAndUpdate(
      arazCityZoneID,
      {
        isSynchingZoneMapping: false,
        lastSynchedDate: new Date(),
        lastSynchedBy: req?.user?._id,
      },
      { new: true }
    );
    console.log(`Reset sync flag for zone ${arazCityZoneID}`);
  }
}

module.exports = {
  getHouseholds,
  getAllZoneMappings,
  getSingleZoneMapping,
  deleteZoneMapping,
  getSingleZoneMappingReport,
  getArazCityWiseJamaatAndJamiat,
  addZoneMappingByHof,
  addZoneMappingByJamaat,
};
