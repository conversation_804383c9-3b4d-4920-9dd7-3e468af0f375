const Joi = require("joi");
const {
  idValidation,
  stringValidation,
  numberValidation,
  arrayIdValidation,
} = require("../../../utils/validator.util");
const { questionTypeList, chartEnums } = require("../models/question.model");

const getAllQuestionsSchema = Joi.object({
  // miqaatID: idValidation.optional(),
  // arazCityID: idValidation.optional(),
});

const getSingleQuestionSchema = Joi.object({
  id: idValidation,
});

const optionValidation = Joi.object({
  placeholder: stringValidation.allow("").required(),
  weightage: numberValidation.optional(),
  required: Joi.boolean().default(false),
});
const option2Validation = Joi.object({
  placeholder: Joi.any(),
  weightage: numberValidation.optional(),
  required: Joi.boolean().default(false),
});

const addEditQuestionSchema = Joi.object({
  title: stringValidation.required(),
  description: Joi.string().trim().allow(""),
  questionType: Joi.string()
    .valid(...Object.values(questionTypeList))
    .required(),
  options: Joi.array().items(optionValidation).default([]),
  otherOptions: Joi.array().items(option2Validation).default([]),
  required: Joi.boolean().default(false),
  isActive: Joi.boolean().optional(),
  department:arrayIdValidation.optional(),
  chartType: Joi.string().valid(...Object.values(chartEnums)).optional(),
});

const deleteQuestionSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  getAllQuestionsSchema,
  addEditQuestionSchema,
  getSingleQuestionSchema,
  deleteQuestionSchema,
};
