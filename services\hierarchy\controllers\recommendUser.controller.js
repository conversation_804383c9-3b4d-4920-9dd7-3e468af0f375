const { DELAY_TIME_1 } = require("bullmq");
const constants = require("../../../constants");
const {
  apiHandler,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  UPDATE_ERROR,
  DELETE_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { toObjectId, isEmpty } = require("../../../utils/misc.util");
const {
  TravelArazCity,
  Department,
  KGUser,
  TravelPriority,
  Jamiat,
  Jamaat,
} = require("../models");
const { RazaRecommendation } = require("../models/razaRecommendation.model");
const { importITSUsersHelper } = require("./kgUser.controller");

// Cache for department, jamiat, and jamaat entities
let detailsCache = {
  departments: new Map(),
  jamiats: new Map(),
  jamaats: new Map(),
};

// Helper function to get or cache entity details
const getEntityDetails = async (model, id, cache) => {
  if (!id) return null;

  const objectId = toObjectId(id);
  const cacheKey = objectId.toString();

  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }

  const entity = await model.findById(objectId).select("name LDName");
  if (entity) {
    cache.set(cacheKey, entity.toObject());
    return entity.toObject();
  }

  return null;
};

const getUsersFromITS = apiHandler(async (req, res) => {
  const response = await importITSUsersHelper(req);

  return apiResponse(FETCH, "Users", response.data, res);
});

const getSmeUserDepartments = apiHandler(async (req, res) => {
  const { _id: loggedInUserID } = req.user;
  const systemDepartmentID = req.user.systemDepartmentID;

  const pipeline = [
    {
      $facet: {
        userDepartments: [
          { $match: { smeUsers: toObjectId(loggedInUserID) } },
          { $project: { name: 1, LDName: 1 } },
        ],
        ...(systemDepartmentID
          ? {
              systemDepartments: [
                { $match: { _id: toObjectId(systemDepartmentID) } },
                { $project: { name: 1, LDName: 1 } },
              ],
            }
          : {}),
        allDepartments: [{ $project: { name: 1, LDName: 1 } }],
      },
    },
    {
      $project: {
        departments: {
          $cond: [
            { $gt: [{ $size: "$userDepartments" }, 0] },
            "$userDepartments",
            systemDepartmentID
              ? {
                  $cond: [
                    { $gt: [{ $size: "$systemDepartments" }, 0] },
                    "$systemDepartments",
                    "$allDepartments",
                  ],
                }
              : "$allDepartments",
          ],
        },
      },
    },
  ];

  const results = await Department.aggregate(pipeline);

  const departments = results[0]?.departments || [];

  if (!departments.length) {
    return apiError(NOT_FOUND, "Departments", null, res);
  }

  return apiResponse(FETCH, "Departments", departments, res);
});

const getRecommendationArazCities = apiHandler(async (req, res) => {
  const travelArazCities = await TravelArazCity.find().select("name");
  if (!travelArazCities) {
    return apiError(NOT_FOUND, "Araz Cities", null, res);
  }

  return apiResponse(FETCH, "Araz Cities", travelArazCities, res);
});

const getTravelPriorities = apiHandler(async (req, res) => {
  const travelPriorities = await TravelPriority.find().select("name");
  if (!travelPriorities) {
    return apiError(NOT_FOUND, "Travel Priorities", null, res);
  }

  return apiResponse(FETCH, "Travel Priorities", travelPriorities, res);
});

const getRazaRecommendations = apiHandler(async (req, res) => {
  const razaRecommendations = await RazaRecommendation.find().select("name");
  if (!razaRecommendations) {
    return apiError(NOT_FOUND, "Raza Recommendations", null, res);
  }

  return apiResponse(FETCH, "Raza Recommendations", razaRecommendations, res);
});

const getRecommendedUsers = apiHandler(async (req, res) => {
  const { _id: loggedInUserID } = req.user;
   const systemDepartmentID = req.user.systemDepartmentID;
  const pipeline = [
    {
      $facet: {
        userDepartments: [
          { $match: { smeUsers: toObjectId(loggedInUserID) } },
          { $project: { name: 1, LDName: 1 } },
        ],
        ...(systemDepartmentID
          ? {
              systemDepartments: [
                { $match: { _id: toObjectId(systemDepartmentID) } },
                { $project: { name: 1, LDName: 1 } },
              ],
            }
          : {}),
        allDepartments: [{ $project: { name: 1, LDName: 1 } }],
      },
    },
    {
      $project: {
        departments: {
          $cond: [
            { $gt: [{ $size: "$userDepartments" }, 0] },
            "$userDepartments",
            systemDepartmentID
              ? {
                  $cond: [
                    { $gt: [{ $size: "$systemDepartments" }, 0] },
                    "$systemDepartments",
                    "$allDepartments",
                  ],
                }
              : "$allDepartments",
          ],
        },
      },
    },
    {
      $addFields: {
        departmentIds: {
          $map: {
            input: "$departments",
            as: "dept",
            in: "$$dept._id",
          },
        },
      },
    },
  ];




  const results = await Department.aggregate(pipeline);
  const { departmentIds = [] } = results[0] || {};

  const recommendedUsers = await KGUser.find({
    "smeRecommendation.departmentID": toObjectId(departmentIds),
  })
    .select(
      "age logo name LDName ITSID smeRecommendation jamiatID jamaatID gender"
    )
    .populate([
      { path: "jamiatID", select: "name" },
      { path: "jamaatID", select: "name" },
      { path: "smeRecommendation.departmentID", select: "name LDName" },
      { path: "smeRecommendation.kgTypeID", select: "name" },
      {
        path: "smeRecommendation.hierarchyPositionID",
        select: "name LDName alias",
      },
      { path: "smeRecommendation.functionID", select: "name" },
      { path: "smeRecommendation.travelPriority", select: "name" },
      { path: "smeRecommendation.razaRecommendation", select: "name" },
      { path: "smeRecommendation.travelCities", select: "name" },
      { path: "smeRecommendation.recommendedBy", select: "name" },
    ]);
  if (!recommendedUsers.length) {
    return apiError(NOT_FOUND, "Recommended Users", null, res);
  }

  return apiResponse(FETCH, "Recommended Users", recommendedUsers, res);
});

const getRecommendedUser = apiHandler(async (req, res) => {
  const { _id: loggedInUserID } = req.user;
  const { id } = req.params;

  const pipeline = [
    {
      $facet: {
        userDepartments: [
          { $match: { smeUsers: toObjectId(loggedInUserID) } },
          { $project: { name: 1, LDName: 1 } },
        ],
        allDepartments: [{ $project: { name: 1, LDName: 1 } }],
      },
    },
    {
      $project: {
        departments: {
          $cond: [
            { $gt: [{ $size: "$userDepartments" }, 0] },
            "$userDepartments",
            "$allDepartments",
          ],
        },
      },
    },
    {
      $addFields: {
        departmentIds: {
          $map: {
            input: "$departments",
            as: "dept",
            in: "$$dept._id",
          },
        },
      },
    },
  ];

  const results = await Department.aggregate(pipeline);
  const { departmentIds = [] } = results[0] || {};

  const recommendedUser = await KGUser.find({
    "smeRecommendation.departmentID": toObjectId(departmentIds),
    _id: toObjectId(id),
  })
    .select("age logo name LDName ITSID smeRecommendation jamiatID jamaatID")
    .populate([
      { path: "jamiatID", select: "name" },
      { path: "jamaatID", select: "name" },
      { path: "smeRecommendation.departmentID", select: "name LDName" },
      { path: "smeRecommendation.kgTypeID", select: "name" },
      {
        path: "smeRecommendation.hierarchyPositionID",
        select: "name LDName alias",
      },
      { path: "smeRecommendation.functionID", select: "name" },
      { path: "smeRecommendation.travelPriority", select: "name" },
      { path: "smeRecommendation.razaRecommendation", select: "name" },
      { path: "smeRecommendation.travelCities", select: "name" },
      { path: "smeRecommendation.recommendedBy", select: "name" },
    ]);
  if (!recommendedUser) {
    return apiError(NOT_FOUND, "Recommended user", null, res);
  }

  return apiResponse(FETCH, "Recommended User", recommendedUser, res);
});

// Helper function to enrich user data with related details
const enrichUserData = async (user) => {
  // Get department details
  const departmentDetails = user.departmentID
    ? await getEntityDetails(
        Department,
        user.departmentID,
        detailsCache.departments
      )
    : null;

  // Get jamiat and jamaat details from the user record

  // Fetch jamiat and jamaat details if they exist
  const jamiatDetails = user?.jamiatID
    ? await getEntityDetails(Jamiat, user.jamiatID, detailsCache.jamiats)
    : null;

  const jamaatDetails = user?.jamaatID
    ? await getEntityDetails(Jamaat, user.jamaatID, detailsCache.jamaats)
    : null;

  return {
    ...user,
    departmentDetails,
    jamiatDetails,
    jamaatDetails,
  };
};

const addRecommendedUsers = apiHandler(async (req, res) => {
  const { users } = req.body;
  const { _id: loggedInUserID } = req.user;

  const successfulUsers = [];
  const rejectedUsers = [];

  // Process users sequentially to accurately track quota
  for (const user of users) {
    // Check department quota before adding
    const quotaStatus = await checkDepartmentQuota(user.departmentID);

    if (!quotaStatus.isQuotaAvailable) {
      // Enrich rejected user with details
      const enrichedUser = await enrichUserData(user);
      rejectedUsers.push({
        ...enrichedUser,
        success: false,
        reason: quotaStatus.message,
      });
      continue; // Skip this user
    }

    // Prepare user data
    user.recommendedBy = user.recommendedBy
      ? user.recommendedBy
      : loggedInUserID;
    for (const key in user) {
      if (isEmpty(user[key])) {
        user[key] = null;
      }
    }

    const updateQuery = { $set: { smeRecommendation: user } };

    const isSystemUser = await KGUser.countDocuments({
      ITSID: user.ITSID,
      systemRoleID: { $exists: true },
    });
    if (!isSystemUser) {
      updateQuery["systemRoleID"] = constants.SYSTEM_ROLES.RECOMMENDED_USER[0];
    }
    updateQuery["systemDepartmentID"] = user.departmentID;
    try {
      const result = await KGUser.updateOne({ ITSID: user.ITSID }, updateQuery);

      if (result.modifiedCount > 0 || result.matchedCount > 0) {
        // Enrich successful user with details
        const enrichedUser = await enrichUserData(user);
        successfulUsers.push({
          ...enrichedUser,
          success: true,
          reason: "Done",
        });
      } else {
        // Enrich rejected user with details
        const enrichedUser = await enrichUserData(user);
        rejectedUsers.push({
          ...enrichedUser,
          success: false,
          reason: "Failed",
        });
      }
    } catch (error) {
      // Enrich rejected user with details
      const enrichedUser = await enrichUserData(user);
      rejectedUsers.push({
        ...enrichedUser,
        success: false,
        reason: error.message,
      });
    }
  }

  // Return information about successful and rejected users
  return apiResponse(
    ADD_SUCCESS,
    "Users",
    {
      addedCount: successfulUsers.length,
      successfulUsers: successfulUsers.length > 0 ? successfulUsers : null,
      rejectedUsers: rejectedUsers.length > 0 ? rejectedUsers : null,
    },
    res
  );
});

const editRecommendedUser = apiHandler(async (req, res) => {
  const { id } = req.body;
  const { _id: loggedInUserID } = req.user;
  const smeData = req.body;

  // Check if department is being changed
  if (smeData.departmentID) {
    const currentUser = await KGUser.findById(toObjectId(id)).select(
      "smeRecommendation"
    );
    const isChangingDepartment =
      !currentUser?.smeRecommendation?.departmentID ||
      !currentUser.smeRecommendation.departmentID.equals(
        toObjectId(smeData.departmentID)
      );

    if (isChangingDepartment) {
      const quotaStatus = await checkDepartmentQuota(smeData.departmentID);
      if (!quotaStatus.isQuotaAvailable) {
        return apiError(
          CUSTOM_ERROR,
          "Quota not available for this department",
          null,
          res
        );
      }
    }
  }

  smeData.recommendedBy = smeData.recommendedBy
    ? smeData.recommendedBy
    : loggedInUserID;
  const updateFields = {};

  const isSystemUser = await KGUser.countDocuments({
    _id: toObjectId(id),
    systemRoleID: { $exists: true },
  });
  if (!isSystemUser) {
    updateFields["systemRoleID"] = constants.SYSTEM_ROLES.RECOMMENDED_USER[0];
  }
  updateFields["systemDepartmentID"] = smeData.departmentID;
  // updateFields["updatedBy"] = toObjectId(loggedInUserID);
  for (const key in smeData) {
    updateFields[`smeRecommendation.${key}`] = isEmpty(smeData[key])
      ? null
      : smeData[key];
  }

  const updatedUser = await KGUser.findOneAndUpdate(
    { _id: toObjectId(id) },
    { $set: updateFields },
    { new: true }
  ).select("smeRecommendation jamiatID jamaatID ITSID");

  if (!updatedUser) {
    return apiError(UPDATE_ERROR, "User", null, res);
  }

  // Enrich updated user with details
  const enrichedUser = await enrichUserData({
    ...smeData,
    ITSID: updatedUser.ITSID,
  });

  return apiResponse(UPDATE_SUCCESS, "User", enrichedUser, res);
});

const deleteRecommendedUser = apiHandler(async (req, res) => {
  const { _id: loggedInUserID } = req.user;
  const { id } = req.params;

  const pipeline = [
    {
      $facet: {
        userDepartments: [
          { $match: { smeUsers: toObjectId(loggedInUserID) } },
          { $project: { name: 1, LDName: 1 } },
        ],
        allDepartments: [{ $project: { name: 1, LDName: 1 } }],
      },
    },
    {
      $project: {
        departments: {
          $cond: [
            { $gt: [{ $size: "$userDepartments" }, 0] },
            "$userDepartments",
            "$allDepartments",
          ],
        },
      },
    },
    {
      $addFields: {
        departmentIds: {
          $map: {
            input: "$departments",
            as: "dept",
            in: "$$dept._id",
          },
        },
      },
    },
  ];

  const results = await Department.aggregate(pipeline);
  const { departmentIds = [] } = results[0] || {};
  const unsetQuery = { smeRecommendation: "" };

  const isRecommendedUser = await KGUser.countDocuments({
    _id: toObjectId(id),
    systemRoleID: toObjectId(constants.SYSTEM_ROLES.RECOMMENDED_USER[0]),
  });
  if (isRecommendedUser) {
    unsetQuery["systemRoleID"] = "";
    unsetQuery["systemDepartmentID"] = "";
  }
  const recommendedUser = await KGUser.findOneAndUpdate(
    {
      "smeRecommendation.departmentID": toObjectId(departmentIds),
      _id: toObjectId(id),
    },
    { $unset: unsetQuery }
  );
  if (!recommendedUser) {
    return apiError(NOT_FOUND, "Recommended User", null, res);
  }

  return apiResponse(DELETE_SUCCESS, "Recommended User", null, res);
});

const getDepartmentQuota = apiHandler(async (req, res) => {
  const { _id: loggedInUserID } = req.user;

  const departmentQuota = await Department.find({
    smeUsers: { $in: [loggedInUserID] },
  }).select("name departmentQuota _id");

  const departmentIds = departmentQuota.map((dept) => dept._id);

  const allocatedCounts = await KGUser.aggregate([
    { $match: { "smeRecommendation.departmentID": { $in: departmentIds } } },
    { $group: { _id: "$smeRecommendation.departmentID", count: { $sum: 1 } } },
  ]);

  const quotaWithAllocated = departmentQuota.map((dept) => {
    const allocated = allocatedCounts.find((count) =>
      count._id.equals(dept._id)
    );
    return {
      ...dept.toObject(),
      allocated: allocated ? allocated.count : 0,
    };
  });

  if (!quotaWithAllocated.length) {
    return apiError(
      NOT_FOUND,
      "Department Quota with Allocated Counts",
      null,
      res
    );
  }

  return apiResponse(FETCH, "Department Quota", quotaWithAllocated, res);
});

const checkDepartmentQuota = async (departmentId) => {
  const department = await Department.findById(toObjectId(departmentId)).select(
    "departmentQuota"
  );

  if (!department || !department.departmentQuota) {
    return { isQuotaAvailable: false, message: "Department quota not found" };
  }

  const allocatedCount = await KGUser.countDocuments({
    "smeRecommendation.departmentID": toObjectId(departmentId),
  });

  if (allocatedCount >= department.departmentQuota) {
    return {
      isQuotaAvailable: false,
      message: `Quota exceeded for department.`,
    };
  }

  return {
    isQuotaAvailable: true,
    remaining: department.departmentQuota - allocatedCount,
  };
};

module.exports = {
  getUsersFromITS,
  getSmeUserDepartments,
  getRecommendationArazCities,
  getTravelPriorities,
  getRazaRecommendations,
  getRecommendedUsers,
  getRecommendedUser,
  addRecommendedUsers,
  editRecommendedUser,
  deleteRecommendedUser,
  getDepartmentQuota,
};
