const { Schema, model } = require("mongoose");
const aggregatePaginate = require("mongoose-aggregate-paginate-v2");
const { deviceMeta } = require("../../hierarchy/models/kgUser.model");

const messageTypes = {
  NEW_MESSAGE: "NEW_MESSAGE",
  REPLY: "REPLY",
  MESSAGE: "MESSAGE",
  NOTIFICATION: "NOTIFICATION",
};

const deviceSchema = new Schema(
  {
    deviceType: {
      type: String,
      enum: ["WEB", "MOBILE"],
    },
    deviceMeta: {
      type: String,
      enum: [...deviceMeta.appTypes, "CMS"],
    },
    messageStatus: {
      type: String,
    },
    success: {
      type: Boolean,
      default: false,
    },
    deliveryTime: {
      type: Date,
    },
  },
  { _id: false }
);

const recipientSchema = new Schema(
  {
    userID: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
    },
    devices: [deviceSchema],
  },
  { _id: false }
);

const notificationLogSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    messageID: {
      type: Schema.Types.ObjectId,
      ref: "Message",
    },
    replyID: {
      type: Schema.Types.ObjectId,
    },
    messageType: {
      type: String,
      enum: Object.values(messageTypes),
    },
    recipients: [recipientSchema],
  },
  { timestamps: true }
);

notificationLogSchema.plugin(aggregatePaginate);

const NotificationLog = model("NotificationLog", notificationLogSchema);

module.exports = { NotificationLog, messageTypes };
