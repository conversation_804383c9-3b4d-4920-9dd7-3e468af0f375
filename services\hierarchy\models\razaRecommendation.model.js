const { Schema, model } = require("mongoose");

const razaRecommendationSchema = new Schema({
  name: {
    type: String,
    required: true
  }
}, { timestamps: true })

const razaRecommendations = ["R1", "R2", "R3"]

const RazaRecommendation = model("RazaRecommendation", razaRecommendationSchema);

const addRazaRecommendationIfNotExists = async () => {
  const existingRazaRecommendations = await RazaRecommendation.find().select("name");

  const existingRazaRecommendationNames = existingRazaRecommendations.map((razaRecommendation) => razaRecommendation.name);

  const razaRecommendationsToAdd = razaRecommendations.filter(
    (razaRecommendation) => !existingRazaRecommendationNames.includes(razaRecommendation)
  );

  if (razaRecommendationsToAdd.length > 0) {
    await RazaRecommendation.insertMany(
      razaRecommendationsToAdd.map((razaRecommendation) => ({ name: razaRecommendation }))
    );
  }
};
// addRazaRecommendationIfNotExists();

module.exports = {
  RazaRecommendation
}