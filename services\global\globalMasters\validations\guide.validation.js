const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
  emailValidation,
} = require("../../../utils/validator.util");

const addGuideSchema = Joi.object({
  name: stringValidation,
  departments: Joi.array().items(idValidation.optional()).optional(),
  attachments: Joi.array().items(Joi.object().unknown()).optional(),
  awsGroupID: idValidation,
});

const getSingleGuideSchema = Joi.object({
  id: idValidation,
});

const editGuideSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
  departments: Joi.array().items(idValidation.optional()).optional(),
  attachments: Joi.array().items(Joi.object().unknown()).optional(),
  awsGroupID: idValidation,
});

const deleteGuideSchema = Joi.object({
  id: idValidation,
});

const addUserClickSchema = Joi.object({
  guideID: idValidation,
  attachmentID: idValidation,
});

const getUserClickReportSchema = Joi.object({
  guideID: idValidation,
});
const getDownloadURLSchema = Joi.object({
  fileKey: stringValidation,
});

module.exports = {
  addGuideSchema,
  getSingleGuideSchema,
  editGuideSchema,
  deleteGuideSchema,
  addUserClickSchema,
  getUserClickReportSchema,
  getDownloadURLSchema,
};
