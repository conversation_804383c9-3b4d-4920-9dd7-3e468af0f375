const { Schema, model } = require("mongoose");

const positionAssignmentSchema = new Schema({
  miqaatID: {
    type: Schema.Types.ObjectId,
    ref: "Miqaat"
  },
  arazCityID: {
    type: Schema.Types.ObjectId,
    ref: "ArazCity"
  },
  arazCityZoneID: {
    type: Schema.Types.ObjectId,
    ref: "ArazCityZone"
  },
  positionID: {
    type: Schema.Types.ObjectId,
    ref: "HierarchyPosition"
  },
  departmentID: {
    type: Schema.Types.ObjectId,
    ref: "Department"
  },
  countRecommendation: {
    type: Number,
    default: 1
  },
  weightage: {
    type: Number,
    default: 10
  }
});

const PositionAssignment = new model("PositionAssignment", positionAssignmentSchema)

module.exports = {
  PositionAssignment
}