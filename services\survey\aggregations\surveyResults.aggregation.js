const { toObjectId } = require("../../../utils/misc.util");

const buildSurveyResultsPipelineForDaily = (
  surveyID,
  targetDate = null,
  latestAnswersMap
) => {
  const matchStage = {
    $match: {
      surveyID: toObjectId(surveyID),
    },
  };

  if (targetDate) {
    const dateOnly = new Date(targetDate).toISOString().split("T")[0];
    const startOfDay = new Date(dateOnly + "T00:00:00.000Z");
    const endOfDay = new Date(dateOnly + "T23:59:59.999Z");
    matchStage.$match.createdAt = {
      $gte: startOfDay,
      $lte: endOfDay,
    };
  }

  const pipeline = [
    matchStage,
    {
      $lookup: {
        from: "kgusers",
        localField: "userID",
        foreignField: "_id",
        as: "userDetails",
      },
    },
    {
      $lookup: {
        from: "surveyforms",
        localField: "surveyID",
        foreignField: "_id",
        as: "surveyDetails",
      },
    },
    {
      $addFields: {
        userDetails: { $arrayElemAt: ["$userDetails", 0] },
        surveyDetails: { $arrayElemAt: ["$surveyDetails", 0] },
      },
    },
    {
      $lookup: {
        from: "questions",
        localField: "surveyDetails.questions",
        foreignField: "_id",
        as: "surveyQuestions",
      },
    },
    {
      $addFields: {
        processedAnswers: {
          $map: {
            input: "$answers",
            as: "answer",
            in: {
              questionID: "$$answer.questionID",
              value: "$$answer.value",
              hasAnswer: {
                $gt: [{ $size: { $ifNull: ["$$answer.value", []] } }, 0],
              },
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: "questions",
        localField: "processedAnswers.questionID",
        foreignField: "_id",
        as: "questionDetails",
      },
    },
    {
      $addFields: {
        resp: {
          $map: {
            input: "$surveyDetails.questions",
            as: "qid",
            in: {
              $let: {
                vars: {
                  existingAnswer: {
                    $first: {
                      $filter: {
                        input: "$processedAnswers",
                        as: "pa",
                        cond: { $eq: ["$$pa.questionID", "$$qid"] },
                      },
                    },
                  },
                  externalAnswers: {
                    $literal: latestAnswersMap,
                  },
                  uid: { $toString: "$userID" },
                  qidStr: { $toString: "$$qid" },
                },
                in: {
                  questionID: "$$qid",
                  question: {
                    $first: {
                      $filter: {
                        input: "$questionDetails",
                        cond: { $eq: ["$$this._id", "$$qid"] },
                      },
                    },
                  },
                  answers: {
                    $cond: [
                      {
                        $gt: [
                          {
                            $size: { $ifNull: ["$$existingAnswer.value", []] },
                          },
                          0,
                        ],
                      },
                      "$$existingAnswer.value",
                      {
                        $ifNull: [
                          {
                            $getField: {
                              field: "$$qidStr",
                              input: {
                                $getField: {
                                  field: "$$uid",
                                  input: "$$externalAnswers",
                                },
                              },
                            },
                          },
                          [],
                        ],
                      },
                    ],
                  },
                  hasAnswer: {
                    $gt: [
                      {
                        $size: {
                          $ifNull: [
                            {
                              $cond: [
                                {
                                  $gt: [
                                    {
                                      $size: {
                                        $ifNull: ["$$existingAnswer.value", []],
                                      },
                                    },
                                    0,
                                  ],
                                },
                                "$$existingAnswer.value",
                                {
                                  $ifNull: [
                                    {
                                      $getField: {
                                        field: "$$qidStr",
                                        input: {
                                          $getField: {
                                            field: "$$uid",
                                            input: "$$externalAnswers",
                                          },
                                        },
                                      },
                                    },
                                    [],
                                  ],
                                },
                              ],
                            },
                            [],
                          ],
                        },
                      },
                      0,
                    ],
                  },
                },
              },
            },
          },
        },
      },
    },
    {
      $project: {
        _id: 1,
        user: {
          _id: "$userDetails._id",
          name: "$userDetails.name",
          LDName: "$userDetails.LDName",
          logo: "$userDetails.logo",
          ITSID: "$userDetails.ITSID",
          email: "$userDetails.email",
          gender: "$userDetails.gender",
          phone: "$userDetails.phone",
          whatsapp: "$userDetails.whatsapp",
        },
        createdAt: 1,
        responses: { answers: "$resp" },
        surveyQuestions: 1,
        totalQuestions: { $size: "$resp" },
        answeredQuestions: {
          $size: {
            $filter: {
              input: "$resp",
              cond: "$$this.hasAnswer",
            },
          },
        },
      },
    },
    {
      $addFields: {
        completionPercentage: {
          $cond: [
            { $gt: ["$totalQuestions", 0] },
            {
              $multiply: [
                { $divide: ["$answeredQuestions", "$totalQuestions"] },
                100,
              ],
            },
            0,
          ],
        },
      },
    },
    {
      $sort: { createdAt: -1 },
    },
  ];
  return pipeline;
};

module.exports = {
  buildSurveyResultsPipelineForDaily,
};
