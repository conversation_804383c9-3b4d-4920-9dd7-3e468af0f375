// modules/survey/validations/surveyResponse.validation.js
const Joi = require("joi");
const { idValidation } = require("../../../utils/validator.util");

const answerSchema = Joi.object({
  questionID: idValidation,
  value: Joi.any().required(),
});

const getSurveyResponseSchema = Joi.object({
  id: idValidation,
});

const createSurveyResponseSchema = Joi.object({
  surveyID: idValidation,
  answers: Joi.array().items(answerSchema).required(),
  responseID: idValidation.optional(),
});

module.exports = {
  createSurveyResponseSchema,
  getSurveyResponseSchema
};

