const { default: axios } = require("axios");
const { OPEN_PROJECT_API_KEY, OPEN_PROJECT_BASE_URL } = require("../constants");
const basicAuth = btoa(`apikey:${OPEN_PROJECT_API_KEY}`);
const { getOpenProjectName, generateOpenProjectGroupName } = require("./misc.util");
const { ArazCity, Miqaat, ArazCityZone, Department, HierarchyPosition, KGUser } = require("../services/hierarchy/models");
const dayjs = require("dayjs");

const roles = {
  ipmo: 24,
  ipmoteam: 24,
  aamilsahab: 27,
  pmo: 25,
  pmoteam: 25,
  jamaatboard: 26,
  leadaqamaulautaro: 28,
  hod: 12,
  hodteam: 12,
  zonalchairman: 20,
  zonalchairmanteam: 20,
  zonehead: 21,
  zonelead: 22,
  zoneteam: 23,
}

// const roles = {
//   ipmo: 13,
//   ipmoteam: 13,
//   aamilsahab: 14,
//   pmo: 15,
//   pmoteam: 15,
//   jamaatboard: 16,
//   leadaqamaulautaro: 17,
//   hod: 18,
//   hodteam: 18,
//   zonalchairman: 19,
//   zonalchairmanteam: 19,
//   zonehead: 20,
//   zonelead: 21,
//   zoneteam: 22,
// }

const api = axios.create({
  baseURL: `${OPEN_PROJECT_BASE_URL}/api/v3`,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Basic ${basicAuth}`,
    'Accept': 'application/hal+json'
  }
});

const getOpenProjectMemberships = async (url) => {
  try {
    let allMembers = []
    while(url) {
      const response = await api.get(`${OPEN_PROJECT_BASE_URL}${url}`)

      allMembers = allMembers.concat(...response.data._embedded.elements)

      url = response.data._links.nextByOffset ? response.data._links.nextByOffset.href : null;
    }

    return allMembers
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project getOpenProjectMemberships")
  }
}

const getOpenProjectWPWatchers = async (wpID) => {
  try {
    let allWatchers = []
    let url = `api/v3/work_packages/${wpID}/watchers`
    const response = await api.get(`${OPEN_PROJECT_BASE_URL}/${url}`)
    console.log(response.data._embedded)
    allWatchers = [...response.data._embedded.elements]
    // while(url) {
    //   const response = await api.get(`${OPEN_PROJECT_BASE_URL}/${url}`)

    //   allWatchers = allWatchers.concat(...response.data._embedded.elements)

    //   url = response.data._links.nextByOffset ? response.data._links.nextByOffset.href : null;
    // }

    return allWatchers
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project getOpenProjectWPWatchers")
  }
}

const extractRoleFromGroupName = (groupName) => {
  const roleKey = Object.keys(roles).find(role =>
    // groupName.toLowerCase().includes(role.toLowerCase())
    groupName.split("_")[2] === role
  );

  if (roleKey) {
    return roles[roleKey];
  }

  return 7; // Default Open Project Non Member Role
}

const splitName = (fullName) => {
  const parts = fullName.trim().split(/\s+/);
  const keyword = parts.includes("bs") ? "bs" : parts.includes("bhai") ? "bhai" : null;
  const i = keyword ? parts.indexOf(keyword) : -1;

  return i !== -1 && i < parts.length - 1
    ? {
        firstName: parts.slice(0, i + 1).join(' '),
        lastName: parts.slice(i + 1).join(' ')
      }
    : { firstName: fullName, lastName: '' };
}

const getOpenProjectProject = async (projectID) => {
  try {
    const response = await api.get(`/projects/${projectID}`)

    if(!response.data) {
      return null
    }

    return response.data
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project getOpenProjectProject")
  }
}

const createOpenProjectProject = async (projectName, parentID, nameSuffix) => {
  console.log(projectName)
  try {
    const identifier = `${getOpenProjectName(projectName)}_${dayjs(new Date()).format("DDMMYYhmsSSS")}`
    const projectData = {
      name: nameSuffix ? `${projectName} - ${nameSuffix}` : projectName,
      identifier,
    }
    if(parentID) {
      projectData.parent = { href: `/api/v3/projects/${parentID}` }
    }

    const response = await api.post(`/projects`, projectData)

    return response.data
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project createOpenProjectProject")
  }
}

const updateOpenProjectProject = async (id, data) => {
  try {
    const response = await api.patch(`/projects/${id}`, data)

    return response.data
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project updateOpenProjectProject")
  }
}

const getOpenProjectUsers = async () => {
  try {
    let allUsers = []
    let url = "api/v3/users?pageSize=500"
    while(url) {
      const response = await api.get(`${OPEN_PROJECT_BASE_URL}/${url}`)

      allUsers = allUsers.concat(...response.data._embedded.elements)

      url = response.data._links.nextByOffset ? response.data._links.nextByOffset.href : null;
    }

    return allUsers
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project getOpenProjectUsers")
  }
}

const getOpenProjectGroups = async () => {
  try {
    let allGroups = []
    let url = "api/v3/groups?pageSize=500"
    while(url) {
      const response = await api.get(`${OPEN_PROJECT_BASE_URL}/${url}`)

      allGroups = allGroups.concat(...response.data._embedded.elements)

      url = response.data._links.nextByOffset ? response.data._links.nextByOffset.href : null;
    }

    return allGroups
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project getOpenProjectGroups")
  }
}

const createOpenProjectGroup = async (data) => {
  try {    
    const groupData = {
      name: data.name,
    }

    const response = await api.post(`/groups`, groupData)

    return response.data
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project createOpenProjectGroup")
  }
}

const createOpenProjectMembership = async (data) => {
  try {
    const roleID = extractRoleFromGroupName(data.groupName)

    for(const projectId of data.projectIds) {
      try {
        const membershipData = {
          principal: {
            href: `/api/v3/groups/${data.groupID}`
          },
          project: {
            href: `/api/v3/projects/${projectId}`
          },
          roles: [
            {
              href: `/api/v3/roles/${roleID}`
            }
          ]
        }
    
        const response = await api.post(`/memberships`, membershipData)
      } catch (err) {
        console.log(err?.response?.data?.message, "from open project memberships", roleID, projectId, data.groupID)
        continue
      }
    }

    // return response.data
  } catch (error) {
    console.log(error?.response?.data?.message, "from open project createOpenProjectMembership")
  }
}

const assignUserToGroup = async (existingUser, arazCityID, miqaatID, positionID, departmentID, zoneID, isReturnError = false) => {
  try {
    const user = await KGUser.findOne({ ITSID: existingUser.ITSID })
    let userID = user.openProjectID
    const { firstName, lastName } = splitName(user.name)
    
    if(!userID) {
      const password = `${user.name.toUpperCase().substring(0, 2)}${user.ITSID.substring(4)}@523`
      const userData = {
        login: user.ITSID,
        firstName,
        lastName,
        email: user.email,
        password
      }
  
      try {
        const userResponse = await api.post(`/users`, userData)
        userID = userResponse.data.id
      } catch (error) {
        console.log(error?.response?.data?.message, user.ITSID, "from open project")
        const allUsers = await getOpenProjectUsers()
        userID = allUsers.find(u => u.login === user.ITSID).id
      }

      await KGUser.findByIdAndUpdate(
        user._id,
        { $set: { openProjectID: userID, openProjectPassword: password } }
      )
    }

    const [arazCity, miqaat, position, department, zone] = await Promise.all([
      ArazCity.findById(arazCityID).select("name openProjectGroups"),
      Miqaat.findById(miqaatID).select("name"),
      HierarchyPosition.findById(positionID).select("name alias"),
      departmentID ? Department.findById(departmentID).select("name") : Promise.resolve(null),
      zoneID ? ArazCityZone.findById(zoneID).select("name") : Promise.resolve(null)
    ]);

    const [groupName] = generateOpenProjectGroupName(arazCity, miqaat, position, department, zone)
    const group = arazCity.openProjectGroups.find(grp => grp.name === groupName)

    if(!group) {
      console.log("group not found in araz city")
      return 
    }

    const groupResponse = await api.get(`/groups/${group.id}`)
    const openProjectGroup = groupResponse.data
    const existingGroupMembers = openProjectGroup._links.members

    const groupData = {
      _links: {
        members: [
          ...existingGroupMembers,
          { href: `/api/v3/users/${userID}` }
        ]
      }
    }
    await api.patch(`/groups/${group.id}`, groupData)

    console.log("user assigned to group")
  } catch (error) {

    console.log(error?.response?.data?.message, "from open project assignUserToGroup")
    if(isReturnError) {
      throw new Error(error)
    }
  }
}

module.exports = { 
  getOpenProjectMemberships,
  getOpenProjectWPWatchers,
  getOpenProjectProject,
  createOpenProjectProject,
  updateOpenProjectProject,
  getOpenProjectGroups,
  createOpenProjectGroup,
  createOpenProjectMembership,
  assignUserToGroup
}
