const { statusTypes } = require("../services/communication/models/message.model");
const { isEmpty, toObjectId } = require("./misc.util");

const applyFilters = (query, filters, userID) => {
  if(filters && filters.length) {
    filters
    .sort((a, b) => a.type === "MESSAGE_STATUS" ? -1 : 1)
    .map(filter => {
      const { type, key, value, options } = filter
  
      switch (type) {
        case "SEARCH":
          // query["$or"].push({
          //   [key]: { $regex: value, $options: "i" }
          // })
          query[key] = { $regex: value, $options: "i" }
          break;
      
        case "MESSAGE_STATUS":
          if(value === "READ") {
            query[`isReadBy.${userID.toString()}`] = true
          } else if (value === "UNREAD") {
            query[`isReadBy.${userID.toString()}`] = false
          } else if (value === statusTypes.DECLINED) {
            query["status"] = statusTypes.DECLINED
          } else if (value === statusTypes.SCHEDULED) {
            query["status"] = statusTypes.SCHEDULED
          } else if (value === "SENT") {
            query["sender"] = userID
            query["$or"] = []
          } else if (value === "CC") {
            query["CCrecipients"] = { $in: [userID] }
          }
          break;
      
        case "CATEGORY":
          query[key] = { $in: toObjectId(options) }
          break;
      
        default:
          break;
      }
    })
  }

  return query
}

const applySort = (query, filters) => {
  if(filters && filters.length) {
    filters.map(filter => {
      const { type, key, value, options } = filter
  
      switch (type) {
        case "SORT":
          query = { [`${key}`]: value === "asc" ? 1 : -1 }
          break;
      
        default:
          break;
      }
    })
  }

  return query
}

module.exports = {
  applyFilters,
  applySort
}