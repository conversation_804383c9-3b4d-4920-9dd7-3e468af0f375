const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON>rror,
  apiResponse,
} = require("../../../../utils/api.util");
const { Hotel } = require("../models");
const {
  ADD_SUCCESS,
  FETCH,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../../utils/message.util");
const {
  generatePreSignedURLs,
  uploadFileToS3,
  generateGetPreSignedURL,
} = require("../../../../utils/aws.util");
const { toObjectId } = require("../../../../utils/misc.util");

const getAllHotels = apiHandler(async (req, res) => {
  const { arazCityZoneID, waazVenueID, arazCityID, miqaatID } = req.query;

  const query = {};
  if (arazCityZoneID) query.arazCityZoneID = toObjectId(arazCityZoneID);
  if (waazVenueID) query.waazVenueID = toObjectId(waazVenueID);
  if (arazCityID) query.arazCityID = toObjectId(arazCityID);
  if (miqaatID) query.miqaatID = toObjectId(miqaatID);

  const hotels = await Hotel.find(query)
    .populate("arazCityID")
    .populate("miqaatID")
    .populate("arazCityZoneID")
    .populate("waazVenueID");

  const formattedHotels = hotels.map((hotel) => {
    const {
      _id,
      hotelName,
      address,
      latitude,
      longitude,
      googleMapLink,
      images // and any other direct fields from hotel schema
    } = hotel;

    return {
      _id,
      hotelName,
      address,
      latitude,
      longitude,
      images,
      googleMapLink,
      arazCityID: hotel.arazCityID?._id,
      arazCityName: hotel.arazCityID?.name || null,
      miqaatID: hotel.miqaatID?._id,
      miqaatName: hotel.miqaatID?.name || null,
      arazCityZoneID: hotel.arazCityZoneID?._id,
      zoneName: hotel.arazCityZoneID?.name || null,
      waazVenueID: hotel.waazVenueID?._id,
      waazVenueName: hotel.waazVenueID?.name || null,
    };
  });

  return apiResponse(FETCH, "Hotels", formattedHotels, res);
});


const getSingleHotel = apiHandler(async (req, res) => {
  const { id: hotelID } = req.params;
  
  const hotel = await Hotel.findOne({ _id: toObjectId(hotelID) })
    .populate("arazCityID")
    .populate("miqaatID")
    .populate("arazCityZoneID")
    .populate("waazVenueID")
    .select("-__v");
  
  if (!hotel) {
    return apiError(NOT_FOUND, "Hotel", null, res);
  }
  const formattedHotel = hotel.toObject();
  
  const {
    _id,
    hotelName,
    address,
    latitude,
    longitude,
    googleMapLink,
    images // and any other direct fields from hotel schema
  } = formattedHotel;

  const resp = {
    _id,
    hotelName,
    address,
    latitude,
    longitude,
    images,
    googleMapLink,
    arazCityID: hotel.arazCityID?._id,
    arazCityName: hotel.arazCityID?.name || null,
    miqaatID: hotel.miqaatID?._id,
    miqaatName: hotel.miqaatID?.name || null,
    arazCityZoneID: hotel.arazCityZoneID?._id,
    zoneName: hotel.arazCityZoneID?.name || null,
    waazVenueID: hotel.waazVenueID?._id,
    waazVenueName: hotel.waazVenueID?.name || null,
  };
  
  return apiResponse(FETCH, "Hotel", resp, res);
});

const addHotel = apiHandler(async (req, res) => {
  if(req.body.waazVenueID ===""){
    delete req.body.waazVenueID
  }
  const hotel = new Hotel(req.body);
  const savedHotel = await hotel.save();
  return apiResponse(ADD_SUCCESS, "Hotel", savedHotel, res);
});

const updateHotel = apiHandler(async (req, res) => {
  const { id: hotelID } = req.body;
  if(req.body.waazVenueID ===""){
    delete req.body.waazVenueID
  }
  const hotel = await Hotel.findOneAndUpdate(
    { _id: toObjectId(hotelID) },
    { $set: req.body },
    { new: true, runValidators: true }
  );
  if (!hotel) {
    return apiError(NOT_FOUND, "Hotel", null, res);
  }
  return apiResponse(UPDATE_SUCCESS, "Hotel", hotel, res);
});

const deleteHotel = apiHandler(async (req, res) => {
  const { id: hotelID } = req.params;
  const hotel = await Hotel.findByIdAndDelete(hotelID);
  if (!hotel) {
    return apiError(NOT_FOUND, "Hotel", null, res);
  }
  return apiResponse(DELETE_SUCCESS, "Hotel", hotel, res);
});

const uploadHotelImage = apiHandler(async (req, res) => {
  if (!req.file) {
    return apiError(CUSTOM_ERROR, "No file uploaded", null, res);
  }

  const uploadResult = await handleS3Upload(req.file);
  if (!uploadResult) {
    return apiResponse(CUSTOM_ERROR, "Failed to upload file", {}, res);
  }

  return apiResponse(
    ADD_SUCCESS,
    "File uploaded successfully",
    uploadResult,
    res
  );
});

const handleS3Upload = async (file) => {
  const fileDetails = {
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  };

  const preSignedURLs = await generatePreSignedURLs(
    "accomodation",
    "hotel_images",
    [fileDetails]
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return null;
  }

  const { fileKey, preSignedURL } = preSignedURLs[0];
  const uploadResult = await uploadFileToS3(file, preSignedURL, fileKey);

  if (!uploadResult) {
    return null;
  }

  return { fileKey, fileDetails };
};

const getDownloadURL = apiHandler(async (req, res) => {
  const { fileKey } = req.body;
  const preSignedURL = await generateGetPreSignedURL(fileKey);
  return apiResponse(FETCH, "Download URL", {preSignedURL}, res);
});

module.exports = {
  getAllHotels,
  getSingleHotel,
  addHotel,
  updateHotel,
  deleteHotel,
  uploadHotelImage,
  getDownloadURL,
};
