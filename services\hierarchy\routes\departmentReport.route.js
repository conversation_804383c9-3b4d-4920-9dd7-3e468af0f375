const { validate } = require("../../../middlewares/validation.middleware");

const { getDepartmentReport, getDepartmentWiseHODReport, getZoneWisHODReport } = require("../../globalMasters/controllers/department.controller");

const {
  getDepartmentReportSchema,
  getHODReportSchema,
} = require("../../globalMasters/validations/department.validation");

const router = require("express").Router();

router.post(
  "/get",
  validate(getDepartmentReportSchema, "body"),
  getDepartmentReport
);
//Will remove in future to allow only zone report view
router.post(
  "/get/department-wise-zonelead-report",
  validate(getHODReportSchema, "body"),
  getZoneWisHODReport
);
router.post(
  "/get/department-wise-hod-report",
  validate(getHODReportSchema, "body"),
  getDepartmentWiseHODReport
);

module.exports = router;
