const { Schema, model } = require("mongoose");

const AnswerSchema = new Schema(
  {
    questionID: {
      type: Schema.Types.ObjectId,
      ref: "Question",
      required: true,
    },
    value: {
      type: Schema.Types.Mixed,
      required: true,
    }
  },
  { _id: false }
);

const SurveyResponseSchema = new Schema(
  {
    surveyID: {
      type: Schema.Types.ObjectId,
      ref: "SurveyForm",
      required: true,
    },
    userID: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    answers: [AnswerSchema],
    submittedAt: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

SurveyResponseSchema.index({ surveyID: 1, userID: 1, submittedAt: 1 });
const SurveyResponse = model("SurveyResponse", SurveyResponseSchema);

module.exports = {
  SurveyResponse,
};
