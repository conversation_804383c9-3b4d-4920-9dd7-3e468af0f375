const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");

const { getAllQuestions, addEditQuestion, activateQuestion, deleteQuestion, getSingleQuestion } = require("../controllers/question.controller");
const { addEditQuestionSchema, getSingleQuestionSchema, deleteQuestionSchema } = require("../validations/question.validation");

router.get("/get", getAllQuestions);

router.post("/add", validate(addEditQuestionSchema, "body"), addEditQuestion);

router.get("/get/:id", validate(getSingleQuestionSchema, "params"), getSingleQuestion);

router.put("/edit/:id", validate(addEditQuestionSchema, "body"), addEditQuestion);

router.post("/add/activate/:id", validate(deleteQuestionSchema, "params"), activateQuestion);

router.delete("/delete/:id", validate(deleteQuestionSchema, "params"), deleteQuestion);

module.exports = router;
