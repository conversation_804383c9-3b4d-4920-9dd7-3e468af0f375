const { spawn } = require('child_process');
const path = require('path');

// Define all microservices
const services = [
  { name: 'Gateway', script: 'server.js', port: process.env.GATEWAY_PORT || 8000 },
  { name: 'Global', script: 'services/global/app.js', port: process.env.GLOBAL_PORT || 8001 },
  { name: 'Hierarchy', script: 'services/hierarchy/app.js', port: process.env.HIERARCHY_PORT || 8002 },
  { name: 'Communication', script: 'services/communication/app.js', port: process.env.COMMUNICATION_PORT || 8003 },
  { name: 'Zones Capacity', script: 'services/zonesCapacity/app.js', port: process.env.ZONES_CAPACITY_PORT || 8004 },
  { name: 'Task Management', script: 'services/taskManagement/app.js', port: process.env.TASK_MANAGEMENT_PORT || 8005 },
  { name: 'Survey', script: 'services/survey/app.js', port: process.env.SURVEY_PORT || 8006 },
  { name: 'Auth Service', script: 'services/authService/app.js', port: process.env.AUTH_SERVICE_PORT || 8007 },
];

const processes = [];

// Function to start a service
function startService(service) {
  console.log(`Starting ${service.name} on port ${service.port}...`);
  
  const child = spawn('node', ['--max-old-space-size=2560', service.script], {
    stdio: 'inherit',
    cwd: __dirname
  });

  child.on('error', (err) => {
    console.error(`Failed to start ${service.name}:`, err);
  });

  child.on('exit', (code) => {
    console.log(`${service.name} exited with code ${code}`);
  });

  processes.push({ name: service.name, process: child });
  return child;
}

// Function to stop all services
function stopAllServices() {
  console.log('\nStopping all services...');
  processes.forEach(({ name, process }) => {
    console.log(`Stopping ${name}...`);
    process.kill('SIGTERM');
  });
  process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', stopAllServices);
process.on('SIGTERM', stopAllServices);

// Start all services
console.log('Starting AMS Microservices...\n');
services.forEach(service => {
  startService(service);
  // Add a small delay between starting services
  setTimeout(() => {}, 1000);
});

console.log('\nAll services started. Press Ctrl+C to stop all services.\n');
console.log('Service URLs:');
services.forEach(service => {
  console.log(`${service.name}: http://localhost:${service.port}`);
});
console.log('');
