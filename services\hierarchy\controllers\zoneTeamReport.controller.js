const constants = require("../../../constants");
const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const { FETCH, CUSTOM_ERROR } = require("../../../utils/message.util");
const { toObjectId, isEmpty } = require("../../../utils/misc.util");
const { redisCacheKeys, setCache, getCache } = require("../../../utils/redis.cache");
const { KGUser, Department, ArazCity } = require("../models");

const addUserToHod = (user, hods, validKgTypes, allowPhone, allowFemalePhoto) => {
  const { foundMiqaat } = user;

  const isValidKgType = validKgTypes.length === 0 || validKgTypes.includes(foundMiqaat?.kgType?.uniqueName);

  if (
    foundMiqaat?.hierarchyPosition?.id.toString() ===
    constants.HIERARCHY_POSITIONS.ZONE_LEAD[0].toString() &&
    isValidKgType
  ) {
    const newHod = {
      position:
        foundMiqaat?.kgType.uniqueName === "local_kg"
          ? foundMiqaat.arazCity.showPositionAlias
            ? foundMiqaat?.hierarchyPosition.alias
            : foundMiqaat?.hierarchyPosition.name
          : foundMiqaat.kgType.name,
      users: [
        {
          name: user.name,
          LDName: user.LDName,
          logo: user.gender === "M" ? user.logo : allowFemalePhoto ? user.logo : undefined,
          ITSID: user?.ITSID,
          gender: user?.gender,
          priority: foundMiqaat?.kgType?.priority,
          ...(user.gender === "M" ? { phone: user.phone, whatsapp: user.whatsapp } : allowPhone ? { phone: user.phone, whatsapp: user.whatsapp } : {}),
        },
      ],
      priority: foundMiqaat?.kgType?.priority,
      bgColor: foundMiqaat?.kgType?.color,
    };

    const existingHod = hods.find((hod) => hod.position === newHod.position);

    if (existingHod) {
      existingHod.users.push({
        name: user.name,
        LDName: user.LDName,
        logo: user.gender === "M" ? user.logo : allowFemalePhoto ? user.logo : undefined,
        gender: user?.gender,
        ITSID: user?.ITSID,
        priority: foundMiqaat?.kgType?.priority,
        ...(user.gender === "M" ? { phone: user.phone, whatsapp: user.whatsapp } : allowPhone ? { phone: user.phone, whatsapp: user.whatsapp } : {}),
      });
    } else {
      hods.push(newHod);
    }
  }
};

const addUserToZone = (user, zonal, validKgTypes, allowPhone, allowFemalePhoto) => {
  const { foundMiqaat } = user;
  const isValidKgType = validKgTypes.length === 0 || validKgTypes.includes(foundMiqaat?.kgType?.uniqueName);

  if (
    foundMiqaat?.hierarchyPosition?.id.toString() ===
    constants.HIERARCHY_POSITIONS.ZONE_TEAM[0].toString() &&
    isValidKgType
  ) {
    zonal.push({
      name: user.name,
      LDName: user.LDName,
      logo: user.gender === "M" ? user.logo : allowFemalePhoto ? user.logo : undefined,
      ITSID: user?.ITSID,
      priority: foundMiqaat?.kgType?.priority,
      gender: user?.gender,
      position:
        foundMiqaat?.kgType.uniqueName === "local_kg"
          ? foundMiqaat.arazCity.showPositionAlias
            ? foundMiqaat?.hierarchyPosition.alias
            : foundMiqaat?.hierarchyPosition.name
          : foundMiqaat.kgType.name,
      kgType: foundMiqaat?.kgType.uniqueName === "local_kg" ? null : foundMiqaat.kgType.name,
      ...(user.gender === "M" ? { phone: user.phone, whatsapp: user.whatsapp } : allowPhone ? { phone: user.phone, whatsapp: user.whatsapp } : {}),
      otherFunction: foundMiqaat?.otherFunction || "",
      function: foundMiqaat.function
    });
  }
};

const sortUsersByPriorityAndName = (users) => {
  return users.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }
    return (a.name || "").localeCompare(b.name || "");
  });
};

const sortByPriorityAndName = (items, nameField) => {
  const sortedItems = items.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }
    return (a[nameField] || "").localeCompare(b[nameField] || "");
  });

  sortedItems.forEach((item) => {
    if (item.users && Array.isArray(item.users)) {
      item.users = sortUsersByPriorityAndName(item.users);
    }
  });

  return sortedItems;
};

const validateEntities = async (arazCityID, arazCityZoneID, departmentID) => {
  try {
    // Check if department exists
    const departmentExists = await Department.findById(
      toObjectId(departmentID)
    );
    if (!departmentExists) {
      return { isValid: false, message: "Department not found" };
    }

    // Check if ArazCity exists
    const arazCity = await ArazCity.findById(arazCityID);
    if (!arazCity) {
      return { isValid: false, message: "ArazCity not found" };
    }

    // Check if ArazCity has the specified zone
    const hasZone =
      arazCity.arazCityZones &&
      Array.isArray(arazCity.arazCityZones) &&
      arazCity.arazCityZones.some(
        (zone) => zone.toString() === arazCityZoneID.toString()
      );

    if (!hasZone) {
      return {
        isValid: false,
        message: "Specified zone does not exist in this ArazCity",
      };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, message: "Error validating entities", error };
  }
};
const getZoneTeamReport = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    arazCityZoneID,
    departmentID,
    kgTypes = [],
  } = req.body;
  const validationResult = await validateEntities(
    arazCityID,
    arazCityZoneID,
    departmentID
  );
  if (!validationResult.isValid) {
    return apiError(CUSTOM_ERROR, validationResult.message, null, res);
  }

  const validKgTypes = Array.isArray(kgTypes) ? kgTypes : [kgTypes];

  const cacheKey = `${redisCacheKeys.HIERARCHY}:${redisCacheKeys.ARAZ_CITY_ZONE_TEAM_REPORT}:${miqaatID}:${arazCityID}:${arazCityZoneID}:${departmentID}:${validKgTypes.sort().join(":")}:${req.allowPhone}`;

  let data = await getCache(cacheKey, miqaatID, arazCityID);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Zone Team Report", data, res, true);
  }

  const checkIsActiveMiqaatAndArazCity = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkIsActiveMiqaatAndArazCity) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let users = await KGUser.aggregate([
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: false } },
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.departmentID": toObjectId(departmentID),
        "miqaats.arazCityZoneID": toObjectId(arazCityZoneID),
        "miqaats.isActive": true,
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "arazCityZone",
      },
    },
    { $unwind: { path: "$arazCityZone", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "arazcities",
        localField: "miqaats.arazCityID",
        foreignField: "_id",
        as: "arazCity",
      },
    },
    { $unwind: { path: "$arazCity", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "department",
      },
    },
    { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "functions",
        localField: "miqaats.functionID",
        foreignField: "_id",
        as: "function",
      },
    },
    { $unwind: { path: "$function", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaats.miqaatID",
        foreignField: "_id",
        as: "miqaat",
      },
    },
    { $unwind: { path: "$miqaat", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $unwind: { path: "$hierarchyPosition", preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    { $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaats.kgGroupID",
        foreignField: "_id",
        as: "kgGroup",
      },
    },
    { $unwind: { path: "$kgGroup", preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        "miqaats.miqaat": {
          id: "$miqaat._id",
          name: "$miqaat.name",
        },
        "miqaats.arazCity": {
          id: "$arazCity._id",
          name: "$arazCity.name",
          showPositionAlias: "$arazCity.showPositionAlias",
        },
        "miqaats.arazCityZone": {
          id: "$arazCityZone._id",
          name: "$arazCityZone.name",
          priority: "$arazCityZone.priority",
        },
        "miqaats.department": {
          id: "$department._id",
          name: "$department.name",
          priority: "$department.priority",
        },
        "miqaats.function": {
          id: "$function._id",
          name: "$function.name",
          priority: "$function.priority",
        },
        "miqaats.kgType": {
          id: "$kgType._id",
          name: "$kgType.name",
          uniqueName: "$kgType.uniqueName",
          priority: "$kgType.priority",
          color: "$kgType.color",
        },
        "miqaats.kgGroup": {
          id: "$kgGroup._id",
          name: "$kgGroup.name",
        },
        "miqaats.hierarchyPosition": {
          id: "$hierarchyPosition._id",
          name: "$hierarchyPosition.name",
          alias: "$hierarchyPosition.alias",
          uniqueName: "$hierarchyPosition.uniqueName",
        },
        "miqaats.otherFunction": "$miqaats.otherFunction",
      },
    },
    {
      $group: {
        _id: "$_id",
        name: { $first: "$name" },
        LDName: { $first: "$LDName" },
        ITSID: { $first: "$ITSID" },
        phone: { $first: "$phone" },
        whatsapp: { $first: "$whatsapp" },
        logo: { $first: "$logo" },
        gender: { $first: "$gender" },
        miqaats: { $push: "$miqaats" },
      },
    },
    {
      $addFields: {
        foundMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                as: "miqaat",
                cond: {
                  $and: [
                    { $eq: ["$$miqaat.miqaat.id", toObjectId(miqaatID)] },
                    { $eq: ["$$miqaat.arazCity.id", toObjectId(arazCityID)] },
                    {
                      $eq: [
                        "$$miqaat.arazCityZone.id",
                        toObjectId(arazCityZoneID),
                      ],
                    },
                    {
                      $eq: ["$$miqaat.department.id", toObjectId(departmentID)],
                    },
                    { $eq: ["$$miqaat.isActive", true] },
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        LDName: 1,
        logo: 1,
        phone: 1,
        whatsapp: 1,
        ITSID: 1,
        gender: 1,
        foundMiqaat: 1,
      },
    },
  ]);

  if (validKgTypes.length > 0) {
    users = users.filter((user) =>
      validKgTypes.includes(user.foundMiqaat?.kgType?.uniqueName)
    );
  }

  let report = {};
  let hods = [];
  let zonal = [];

  users.sort((a, b) => (a.name || "").localeCompare(b.name || ""));


  users.forEach((user) => {
    addUserToHod(user, hods, validKgTypes, req.allowPhone, req.allowFemalePhoto);
    addUserToZone(user, zonal, validKgTypes, req.allowPhone, req.allowFemalePhoto);
  });

  report = {
    hod: sortByPriorityAndName(hods, "position"),
    zonal: sortByPriorityAndName(zonal, "zoneName"),
  };

  await setCache(cacheKey, report, miqaatID, arazCityID);

  apiResponse(FETCH, "Department Report", report, res);
});
module.exports = { getZoneTeamReport };
