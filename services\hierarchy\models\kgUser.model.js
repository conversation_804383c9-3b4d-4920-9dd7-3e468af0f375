const { Schema, model } = require("mongoose");
const constants = require("../../../constants");
const { stringValidation } = require("../../../utils/validator.util");

const deviceMeta = {
  allTypes: ["WEBAPP", "AND<PERSON><PERSON>", "IOS"],
  appTypes: ["ANDROID", "IOS"],
  webTypes: ["WEBAPP"],
};

const miqaatHRSchema = new Schema({
  Committee: { type: String },
  Designation: { type: String },
  MiqaatZone: { type: String, default: "" },
  Miqaat_ID: { type: String },
  RazaStatus: { type: String, default: "No Raza" },
  isAllocated: { type: Boolean },
  isArrived: { type: Boolean },
});

const miqaatSchema = new Schema(
  {
    isPrinted: {
      type: Boolean,
      default: false,
    },
    printedAt: {
      type: Date,
      required: false,
    },
    printedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: false,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: false,
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: false,
    },
    hierarchyPositionID: {
      type: Schema.Types.ObjectId,
      ref: "HierarchyPosition",
      required: false,
    },
    departmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: false,
    },
    functionID: {
      type: Schema.Types.ObjectId,
      ref: "Function",
      required: false,
    },
    otherFunction: {
      type: String,
      required: false,
    },
    kgTypeID: {
      type: Schema.Types.ObjectId,
      ref: "KGType",
      required: false,
    },
    kgGroupID: {
      type: Schema.Types.ObjectId,
      ref: "KGGroup",
      required: false,
    },
    cityRoleID: {
      type: Schema.Types.ObjectId,
      ref: "SystemRole",
      required: false,
      default: constants.SYSTEM_ROLES.DEFAULT[0],
    },
    consentRequired: {
      type: Boolean,
      required: false,
    },
    consentAccepted: {
      type: Boolean,
      required: false,
    },
    status: {
      type: String,
      required: false,
    },
    declineReason: {
      type: String,
      required: false,
    },
    isInternationalPlugin: {
      type: Boolean,
      required: false,
      default: false,
    },
    updatedAt: {
      type: Date,
      required: false,
    },
    isAddedFromRazaList: {
      type: Boolean,
      required: false,
      default: false,
    },
    recievedArrivalInfo: {
      type: Date,
      required: false,
    },
    modeOfTravel: {
      type: String,
      required: false,
      trim: true,
    },
    arrivalDate: {
      type: Date,
      required: false,
    },
    arrivalTime: {
      type: String,
      required: false,
      trim: true,
    },
    flightOrTrainNumber: {
      type: String,
      required: false,
      trim: true,
    },
    miqaatHR: miqaatHRSchema,
  },
  { _id: false }
);

const smeRecommendationSchema = new Schema(
  {
    departmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: false,
    },
    kgTypeID: {
      type: Schema.Types.ObjectId,
      ref: "KGType",
      required: false,
    },
    hierarchyPositionID: {
      type: Schema.Types.ObjectId,
      ref: "HierarchyPosition",
      required: false,
    },
    functionID: {
      type: Schema.Types.ObjectId,
      ref: "Function",
      required: false,
    },
    khidmatZone: {
      type: String,
      enum: ["CMZ", "ZONE"],
    },
    otherFunction: {
      type: String,
      required: false,
    },
    travelPriority: {
      type: Schema.Types.ObjectId,
      ref: "TravelPriority",
    },
    razaRecommendation: {
      type: Schema.Types.ObjectId,
      ref: "RazaRecommendation",
    },
    travelCities: [
      {
        type: Schema.Types.ObjectId,
        ref: "TravelArazCity",
      },
    ],
    recommendedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
    remarks: {
      type: String,
      required: false,
    },
  },
  { _id: false, timestamps: true }
);

const kgUserSchema = new Schema(
  {
    tempNew: {
      type: Boolean,
      default: false,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    LDName: {
      type: String,
      required: false,
      trim: true,
    },
    ITSID: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    logo: {
      type: String,
      required: false,
      trim: true,
    },
    email: {
      type: String,
      required: false,
      trim: true,
    },
    openProjectPassword: {
      type: String,
    },
    openProjectID: {
      type: String,
    },
    phone: {
      type: String,
      required: false,
      trim: true,
    },
    whatsapp: {
      type: String,
      required: false,
      trim: true,
    },
    age: {
      type: String,
      required: false,
      trim: true,
    },
    gender: {
      type: String,
      required: false,
      trim: true,
    },
    maritialStatus: {
      type: String,
      required: false,
      trim: true,
    },
    prefix: {
      type: String,
      required: false,
      trim: true,
    },
    misaq: {
      type: String,
      required: false,
      trim: true,
    },
    occupation: {
      type: String,
      required: false,
      trim: true,
    },
    qualification: {
      type: String,
      required: false,
      trim: true,
    },
    idara: {
      type: String,
      required: false,
      trim: true,
    },
    category: {
      type: String,
      required: false,
      trim: true,
    },
    organization: {
      type: String,
      required: false,
      trim: true,
    },
    address: {
      type: String,
      required: false,
      trim: true,
    },
    city: {
      type: String,
      required: false,
      trim: true,
    },
    country: {
      type: String,
      required: false,
      trim: true,
    },
    nationality: {
      type: String,
      required: false,
      trim: true,
    },
    vatan: {
      type: String,
      required: false,
      trim: true,
    },
    miqaats: [miqaatSchema],
    smeRecommendation: smeRecommendationSchema,
    status: {
      type: String,
      required: true,
      trim: true,
      enum: ["active", "inactive"],
    },
    jamiatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamiat",
      required: false,
    },
    jamaatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamaat",
      required: false,
    },
    treatAsCityUser: {
      type: Boolean,
      default: true,
    },
    systemRoleID: {
      type: Schema.Types.ObjectId,
      ref: "SystemRole",
      required: false,
    },
    systemDepartmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: false,
    },
    isSuperAdmin: {
      type: Boolean,
      required: false,
      default: false,
    },
    appDetails: {
      deviceID: {
        type: String,
      },
      version: {
        type: String,
      },
      OSVersion: {
        type: String,
      },
      deviceType: {
        type: String,
        enum: deviceMeta.appTypes,
      },
    },
    webDetails: {
      deviceID: {
        type: String,
      },
      CMSVersion: {
        type: String,
      },
      browserType: {
        type: String,
      },
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const KGUser = model("KGUser", kgUserSchema);

module.exports = { KGUser, deviceMeta };
