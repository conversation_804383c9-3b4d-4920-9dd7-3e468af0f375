const router = require("express").Router();

const {
  getHierarchy,
  regenerateHierarchy,
  editHierarchyUsers,
} = require("../controllers/hierarchy.controller");
const {
  validate,
  validateV2,
} = require("../../../middlewares/validation.middleware");
const {
  getHierarchySchema,
  regenerateHierarchySchema,
  editHierarchyUsersSchema,
} = require("../validations/hierarchy.validation");

router.post("/get", validateV2(getHierarchySchema, "body"), getHierarchy);

router.post(
  "/edit/regenerate",
  validate(regenerateHierarchySchema, "body"),
  regenerateHierarchy
);

router.post(
  "/edit/users",
  validate(editHierarchyUsersSchema, "body"),
  editHierarchyUsers
);

module.exports = router;
