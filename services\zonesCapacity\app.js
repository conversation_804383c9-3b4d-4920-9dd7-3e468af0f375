const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, ZONES_CAPACITY_PORT } = require("../../constants");
const routes = require("./routes");
const { authGuard, roleGuard } = require("../../middlewares/guard.middleware");
const { connectDB } = require("../../db");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));

app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});


// Zones Capacity routes
app.use("/api/zones-capacity", authGuard, roleGuard, routes);

app.get("/", (req, res) => {
  res.send(`Zones Capacity Service is running on port: ${ZONES_CAPACITY_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ 
    status: "healthy", 
    service: "zones-capacity",
    port: ZONES_CAPACITY_PORT,
    timestamp: new Date().toISOString()
  });
});

// Start server
const startServer = async () => {
  try {
    await connectDB();
    app.listen(ZONES_CAPACITY_PORT, () => {
      
      console.log(`Zones Capacity Service running on port: ${ZONES_CAPACITY_PORT}`);
    });
  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
