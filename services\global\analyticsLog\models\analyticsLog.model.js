const { Schema, model } = require("mongoose");

const userAnalyticsAction = {
  DOWNLOAD_REPORT: "download_report",
  PAGE_VISIT: "page_visit",
  USER_LOGIN: "user_login",
  USER_LOGOUT: "user_logout",
};

const deviceType = {
  WEB: "web",
  APP: "app",
};

const analyticsLogSchema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
    action: {
      type: String,
      enum: Object.values(userAnalyticsAction),
      required: false
    },
    module: {
      type: String,
      required: false,
    },
    page: {
      type: String,
      required: false,
    },
    url: {
      type: String,
      required: false,
    },
    deviceType: {
      type: String,
      enum: Object.values(deviceType),
      required: false
    },
    browser: {
      type: String,
      required: false
    },
    os: {
      type: String,
      required: false
    },
    IP: {
      type: String,
      required: false
    },
    location: {
      city: { type: String },
      state: { type: String },
      country: { type: String }
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: false,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: false,
    },
  },
  { timestamps: true }
);

const AnalyticsLog = model("AnalyticsLog", analyticsLogSchema);

module.exports = { AnalyticsLog, userAnalyticsAction, deviceType };
