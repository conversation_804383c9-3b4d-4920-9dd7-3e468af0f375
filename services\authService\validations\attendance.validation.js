const Joi = require("joi");
const { idValidation, dateValidation, stringValidation } = require("../../../utils/validator.util");

const markAttendanceSchema = Joi.object({
    miqaatID: idValidation.required(),
    arazCityID: idValidation.required(),
    latitude: stringValidation,
    longitude: stringValidation,
    timeZone: Joi.string().trim().optional(),
});

const getProfileSchema = Joi.object({
    miqaatID: idValidation.required(),
    arazCityID: idValidation.required(),
});

module.exports = {
    markAttendanceSchema,
    getProfileSchema,
};

