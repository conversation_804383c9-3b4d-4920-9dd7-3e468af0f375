const { Schema, model } = require("mongoose");

const TravelArazCitySchema = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

const cities = ["Indore", "Mumbai", "Colombo"];

const TravelArazCity = model("TravelArazCity", TravelArazCitySchema);

const addTravelArazCitiesIfNotExists = async () => {
  const existingCities = await TravelArazCity.find().select("name");

  const existingCityNames = existingCities.map((city) => city.name);

  const citiesToAdd = cities.filter(
    (city) => !existingCityNames.includes(city)
  );

  if (citiesToAdd.length > 0) {
    await TravelArazCity.insertMany(
      citiesToAdd.map((city) => ({ name: city }))
    );
  }
};
// addTravelArazCitiesIfNotExists();

module.exports = { TravelArazCity };
