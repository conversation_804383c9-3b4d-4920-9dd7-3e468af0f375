/*************  ✨ Windsurf Command ⭐  *************/
const {
  apiHandler,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const { MawaidVenue, WaazVenue } = require("../models");
const { isEmpty } = require("../../../utils/misc.util");
const { FETCH } = require("../../../utils/message.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getDashboard = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  if (isEmpty(miqaatID) || isEmpty(arazCityID)) {
    return apiError(NOT_FOUND, "Miqaat ID and Araz City ID are required", res);
  }
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const [mawaidVenues, waazVenues] = await Promise.all([
    MawaidVenue.countDocuments({ arazCityID, miqaatID,status:'active' }),
    WaazVenue.countDocuments({ arazCityID, miqaatID, status:'active', approvalStatus:'approved' }),
  ]);
  const response = {
    totalMawaidVenues: mawaidVenues,
    totalWaazVenues: waazVenues,
  };


  return apiResponse(FETCH, "Dashboard", response, res);
});

module.exports = {
  getDashboard,
};