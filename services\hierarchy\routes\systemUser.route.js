const { validate } = require("../../../middlewares/validation.middleware");

const {
  getAllSystemUsers,
  addEditSystemUser,
  getSingleSystemUser,
  deleteSystemUser,
  clearAllCache,
  getJamaiatJamaatMasterRecord,
} = require("../controllers/systemUser.controller");

const {
  addEditSystemUserSchema,
  getSingleSystemUserSchema,
  deleteSystemUserSchema,
} = require("../validations/systemUser.validation");

const router = require("express").Router();

router.get("/get", getAllSystemUsers);
router.get("/get/clearAllCache", clearAllCache);
router.get("/get/jamiat-jamaat-record",getJamaiatJamaatMasterRecord)

router.post(
  "/add",
  validate(addEditSystemUserSchema, "body"),
  addEditSystemUser
);

router.put(
  "/edit",
  validate(addEditSystemUserSchema, "body"),
  addEditSystemUser
);

router.get(
  "/get/:id",
  validate(getSingleSystemUserSchema, "params"),
  getSingleSystemUser
);

router.delete(
  "/delete/:id",
  validate(deleteSystemUserSchema, "params"),
  deleteSystemUser
);

module.exports = router;
