const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");

const {
  getSingleSurvey,
  deleteSurvey,
  addEditSurvey,
  getMySurveys,
  getQuestions,
  toggleSurveyStatus,
  getSingleSurveyWithoutMembers,
  checkQuestionUsedStatus,
} = require("../controllers/surveyForm.controller");

const {
  getAllSurveysSchema,
  getQeustionsSchema,
  getSingleSurveySchema,
  deleteSurveySchema,
  addEditSurveySchema,
} = require("../validations/surveyForm.validation");
const { getSurveyResults } = require("../controllers/reports.controller");

router.get("/get", validate(getAllSurveysSchema, "query"), getMySurveys);

router.get("/add/get-questions", getQuestions);

router.post("/add", validate(addEditSurveySchema, "body"), addEditSurvey);

router.post(
  "/get/check-question-used",
  validate(getQeustionsSchema, "body"),
  checkQuestionUsedStatus
);

router.post(
  "/get/survey-results/:id",
  validate(getSingleSurveySchema, "params"),
  getSurveyResults
);

router.get(
  "/get/without-members/:id",
  validate(getSingleSurveySchema, "params"),
  getSingleSurveyWithoutMembers
);

router.get(
  "/get/:id",
  validate(getSingleSurveySchema, "params"),
  getSingleSurvey
);

router.put("/edit/:id", validate(addEditSurveySchema, "body"), addEditSurvey);

router.delete(
  "/delete/:id",
  validate(deleteSurveySchema, "params"),
  deleteSurvey
);

router.post(
  "/add/activate/:id",
  validate(deleteSurveySchema, "params"),
  toggleSurveyStatus
);

module.exports = router;
