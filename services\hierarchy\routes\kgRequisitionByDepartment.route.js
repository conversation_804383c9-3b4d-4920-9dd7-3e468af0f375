const express = require("express");
const { validate } = require("../../../middlewares/validation.middleware");
const router = express.Router();
const { getKgRequisitionByDepartment, addKgRequisitionByDepartment, getArazCityZoneById } = require("../controllers/kgRequisitionDepartment.controller");
const { getArazCityZoneByIdSchema, getKgRequisitionByDepartmentSchema, getKgRequisitionByZoneSchema } = require("../validations/kgRequisitionByDepartment.validation");

router.get("/get/araz-city-zone",validate(getArazCityZoneByIdSchema, "query"), getArazCityZoneById);
router.post("/get/kg-requisition-by-department",validate(getKgRequisitionByDepartmentSchema, "body"), getKgRequisitionByDepartment);
router.put("/add/kg-requisition-by-department", addKgRequisitionByDepartment);

module.exports = router;
