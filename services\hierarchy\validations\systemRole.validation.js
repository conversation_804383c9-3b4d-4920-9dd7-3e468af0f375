const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
  emailValidation,
} = require("../../../utils/validator.util");

const permissionsSchema = Joi.object({
  add: booleanValidation.optional(),
  view: booleanValidation.optional(),
  edit: booleanValidation.optional(),
  delete: booleanValidation.optional(),
});

const optionSchema = Joi.object({
  name: stringValidation.required(),
  permissions: permissionsSchema.required(),
});

const moduleSchema = Joi.object({
  name: stringValidation.required(),
  options: Joi.array().items(optionSchema).required(),
});

const addSystemRoleSchema = Joi.object({
  name: Joi.string().required(),
  // modules: Joi.array().items(moduleSchema).required(),
}).unknown();

const getSingleSystemRoleSchema = Joi.object({
  id: idValidation,
});

const editSystemRoleSchema = Joi.object({
  id: idValidation,
  name: Joi.string().required(),
  // modules: Joi.array().items(moduleSchema).required(),
}).unknown();

const deleteSystemRoleSchema = Joi.object({
  id: idValidation,
});

const getEncryptedSystemRoleSchema = Joi.object({
  userID: idValidation,
  miqaatID: idValidation,
  arazCityID: idValidation,
});

module.exports = {
  addSystemRoleSchema,
  getSingleSystemRoleSchema,
  editSystemRoleSchema,
  deleteSystemRoleSchema,
  getEncryptedSystemRoleSchema,
};
