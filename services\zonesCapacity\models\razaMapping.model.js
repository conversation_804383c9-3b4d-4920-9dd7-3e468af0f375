const { Schema, model } = require("mongoose");

const razaMapping = new Schema(
  {
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    ITSID: { type: String },
    MiqaatZone: { type: String },
    RazaStatus: { type: String },
  },
  {
    timestamps: true,
  }
);

const RazaMapping = model("razaMapping", razaMapping);

module.exports = { RazaMapping };
