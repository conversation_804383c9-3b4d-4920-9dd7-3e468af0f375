const { validate } = require("../../../middlewares/validation.middleware");
const { getNotifications, addNotification, getNotification, sendMeetingNotification } = require("../controllers/notification.controller");
const { getNotificationSchema, getNotificationsSchema, sendMeetingReminderSchema  } = require("../validations/notification.validation");

const router = require("express").Router();

router.get("/get", validate(getNotificationsSchema, "query"), getNotifications)

router.get("/get/:id", validate(getNotificationSchema, "params"), getNotification)

router.post("/add", addNotification)

router.post("/add/meeting", validate(sendMeetingReminderSchema, "body"), sendMeetingNotification);

module.exports = router;