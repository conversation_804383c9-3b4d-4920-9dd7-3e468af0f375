const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");
const { authGuard } = require("../../../middlewares/guard.middleware");

// Import controllers
const { getProfile, markAttendance } = require("../controllers/attendance.controller");

// Import validations
const { getProfileSchema, markAttendanceSchema } = require("../validations/attendance.validation");

// All attendance routes require authentication
router.use(authGuard);

// Attendance routes
router.post("/profile", validate(getProfileSchema, "body"), getProfile);
router.post("/mark", validate(markAttendanceSchema, "body"), markAttendance);

module.exports = router;
