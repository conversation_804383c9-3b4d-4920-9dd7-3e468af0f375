const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON><PERSON>r,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { KGType } = require("../../hierarchy/models");
const { isEmpty, getUniqueName } = require("../../../utils/misc.util");
const {
  redisCacheKeys,
  getCache,
  setCache,
  clearCacheByPattern,
} = require("../../../utils/redis.cache");
const {
  addEventLog,
  Modules,
  EventActions,
} = require("../../../utils/eventLogs.util");

const getAllKGTypes = apiHandler(async (req, res) => {
  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.KG_TYPES}:{all}`;
  let data = await getCache(cacheKey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "KG Types", data, res, true);
  }
  const kgTypes = await KGType.find({ isActive: true }).sort({ _id: -1 });

  setCache(cacheKey, kgTypes);

  return apiResponse(FETCH, "KG Types", kgTypes, res);
});

const addKGType = apiHandler(async (req, res) => {
  const { name, color, priority } = req.body;

  const uniqueName = getUniqueName(name);

  const existingKGType = await KGType.findOne({ uniqueName, isActive: true });
  if (!isEmpty(existingKGType)) {
    return apiError(
      CUSTOM_ERROR,
      "KG Type with this name already exists",
      null,
      res
    );
  }

  const newKGType = new KGType({
    name,
    uniqueName,
    color,
    priority,
    createdBy: req.user._id,
  });

  await newKGType.save();

  clearCacheByPattern(
    `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.KG_TYPES}`
  );

  await addEventLog(
    EventActions.CREATE,
    Modules.GlobalMasters,
    "KG Type",
    req.user._id
  );

  return apiResponse(ADD_SUCCESS, "KG Type", null, res);
});

const getSingleKGType = apiHandler(async (req, res) => {
  const { id } = req.params;

  const kgTypeData = await KGType.findOne({ _id: id, isActive: true });

  if (isEmpty(kgTypeData)) {
    return apiError(NOT_FOUND, "KG Type", null, res);
  }
  return apiResponse(FETCH, "KG Type", kgTypeData, res);
});

const editKGType = apiHandler(async (req, res) => {
  const { id, name, color, priority } = req.body;

  const uniqueName = getUniqueName(name);

  const existingKGType = await KGType.findOne({
    uniqueName,
    isActive: true,
    _id: { $ne: id },
  });

  if (!isEmpty(existingKGType)) {
    return apiError(
      CUSTOM_ERROR,
      "KG Type with this name already exists",
      null,
      res
    );
  }

  const updateData = {
    name,
    uniqueName,
    color,
    priority,
    updatedBy: req.user._id,
  };

  const kgTypeData = await KGType.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  if (isEmpty(kgTypeData)) {
    return apiError(NOT_FOUND, "KG Type", null, res);
  }
  clearCacheByPattern(
    `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.KG_TYPES}`
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "KG Type",
    req.user._id
  );

  return apiResponse(UPDATE_SUCCESS, "KG Type", kgTypeData, res);
});

const deleteKGType = apiHandler(async (req, res) => {
  const { id } = req.params;

  const kgTypeData = await KGType.findOneAndUpdate(
    { _id: id },
    {
      isActive: false,
      updatedBy: req.user._id,
    }
  );

  if (isEmpty(kgTypeData)) {
    return apiError(NOT_FOUND, "KG Type", null, res);
  }
  clearCacheByPattern(
    `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.KG_TYPES}`
  );
  await addEventLog(
    EventActions.DELETE,
    Modules.GlobalMasters,
    "KG Type",
    req.user._id
  );

  return apiResponse(DELETE_SUCCESS, "KG Type", null, res);
});

module.exports = {
  getAllKGTypes,
  addKGType,
  getSingleKGType,
  editKGType,
  deleteKGType,
};
