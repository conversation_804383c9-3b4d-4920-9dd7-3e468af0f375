const { toObjectId } = require("../../../utils/misc.util");
const { PositionAssignment } = require("../models");

const upsertPositionAssignment = async ({
  positionID,
  arazCityID,
  miqaatID,
  countRecommendation,
  weightage,
  departmentID,
  arazCityZoneID
}) => {
  const findQuery = {
    positionID: toObjectId(positionID),
    arazCityID: toObjectId(arazCityID),
    miqaatID: toObjectId(miqaatID)
  };

  if (departmentID) {
    findQuery["departmentID"] = toObjectId(departmentID);
  }
  if (arazCityZoneID) {
    findQuery["arazCityZoneID"] = toObjectId(arazCityZoneID);
  }

  const options = { upsert: true, new: true, setDefaultsOnInsert: true };

  const assignment = await PositionAssignment.findOneAndUpdate(
    findQuery,
    {
      countRecommendation,
      weightage
    },
    options
  );
  return assignment;
}

const getPositionAssignment = (positionAssignments, positionID, arazCityZoneID, departmentID) => {
  return positionAssignments.find(pa => {
    let findCondition = pa.positionID.toString() === positionID.toString()

    if(arazCityZoneID) {
      findCondition = findCondition && pa.arazCityZoneID.toString() === arazCityZoneID.toString()
    }
    if(departmentID) {
      findCondition = findCondition && pa.departmentID.toString() === departmentID.toString()
    }

    if(findCondition) return pa
  })
}

module.exports = {
  upsertPositionAssignment,
  getPositionAssignment
}
