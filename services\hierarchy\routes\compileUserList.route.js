const router = require("express").Router();
const { validate } = require("../../../middlewares/validation.middleware");
const { getDepartmentWiseUsers, getCompileLists, getCompileList, addCompileList, addCompileListQuota, editCompileList, deleteCompileList, downloadCompileListPDF, editCompileListUser, deleteCompileListUser, getUsersFromITS, getArazCity } = require("../controllers/compileUserList.controller");
const { addCompileListSchema, getDepartmentWiseUsersSchema, editCompileListSchema, addCompileListQuotaSchema, deleteCompileListSchema, downloadCompileListSchema, editCompileListUserSchema, deleteCompileListUserSchema, getUsersFromITSSchema } = require("../validations/compileUserList.validation");

router.get("/get/download/:departmentID?", validate(downloadCompileListSchema, "params"), downloadCompileListPDF)

router.post("/get/import-its-users", validate(getUsersFromITSSchema, "body"), getUsersFromITS)

router.post("/get/department-users", validate(getDepartmentWiseUsersSchema, "body"), getDepartmentWiseUsers)

router.get("/get/araz-cities", getArazCity)


router.get("/get", getCompileLists)

router.get("/get/:id", getCompileList)

router.post("/add", validate(addCompileListSchema, "body"), addCompileList)

router.patch("/add/department-quota", validate(addCompileListQuotaSchema, "body"), addCompileListQuota)

router.patch("/edit", validate(editCompileListSchema, "body"), editCompileList)

router.patch("/edit/user", validate(editCompileListUserSchema, "body"), editCompileListUser)

router.delete("/delete/:id", validate(deleteCompileListSchema, "params"), deleteCompileList)

router.patch("/delete/user", validate(deleteCompileListUserSchema, "body"), deleteCompileListUser)

module.exports = router
