const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  uploadFileToS3,
  generatePreSignedURLs,
  generateGetPreSignedURL,
} = require("../../../utils/aws.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
} = require("../../../utils/misc.util");
const {
  getUserRoleAndPermissions,
} = require("../../hierarchy/controllers/systemRole.controller");
const { ArazCityZoneFile, ArazCityZone } = require("../../hierarchy/models");
const { WaazVenue } = require("../models");

const getAllWaazVenue = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID,arazCityZoneID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  let query = {}
  if(miqaatID) query.miqaatID = toObjectId(miqaatID)
  if(arazCityID) query.arazCityID = toObjectId(arazCityID)
  if(arazCityZoneID) query.arazCityZoneID = toObjectId(arazCityZoneID)
  const waazVenues = await WaazVenue.find(query)
    .populate("miqaatID", "name")
    .populate("arazCityID", "name")
    .populate("mawaidVenues", "name")
    .populate("arazCityZoneID", "name")
    .populate("waazVenueTypeID", "name")
    .populate("waazVenueSuitabilityID", "name")
    .populate("waazVenuePriorityID", "name")
    .populate("createdBy", "name")
    .populate("updatedBy", "name")
    .sort({ _id: -1 });

  const waazVenuesWithDrawingStatus = await Promise.all(
    waazVenues.map(async (venue) => {
      const venueObj = venue.toObject();

      const arazCityZoneFile = await ArazCityZoneFile.findOne({
        arazCityZoneID: venue.arazCityZoneID?._id,
        arazCityID: venue.arazCityID?._id,
        miqaatID: venue.miqaatID?._id,
        venueType: "ZONES",
        status: "APPROVED",
      })
        .sort({ createdAt: -1 })
        .limit(1);

      venueObj.isDrawing = !!arazCityZoneFile;

      return venueObj;
    })
  );

  return apiResponse(FETCH, "Waaz Venue", waazVenuesWithDrawingStatus, res);
});

const addEditWaazVenue = apiHandler(async (req, res) => {
  const { id } = req.body;
  let data = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(data.miqaatID, data.arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const systemRole = getUserRoleAndPermissions(
    req?.header("Permissions")?.trim()
  );

  if (!systemRole) return apiError(NOT_FOUND, "System Role", null, res);

  if (
    data.finalizedWaazSeatingCapacity &&
    systemRole.systemRoleName !== "super_admin" &&
    systemRole.systemRoleName !== "impo"
  ) {
    return apiError(
      CUSTOM_ERROR,
      "User does not have permissions to finalize Seating Capacity",
      null,
      res
    );
  }

  if (
    data.approvalStatus === "approved" &&
    systemRole.systemRoleName !== "super_admin" &&
    systemRole.systemRoleName !== "impo"
  ) {
    return apiError(
      CUSTOM_ERROR,
      "User does not have permissions to Approve Venue",
      null,
      res
    );
  }

  data.uniqueName = getUniqueName(data.name);

  const uniqueNameQueryObject = {
    uniqueName: data.uniqueName,
    arazCityID: data.arazCityID,
    arazCityZoneID: data.arazCityZoneID,
  };

  if (id) uniqueNameQueryObject._id = { $ne: id };

  const existingWaazVenue = await WaazVenue.findOne(uniqueNameQueryObject);

  if (existingWaazVenue) {
    return apiError(
      CUSTOM_ERROR,
      "Waaz Venue with this name already exists",
      null,
      res
    );
  }

  if (!id) {
    data.createdBy = req.user._id;
    const newWaazVenue = new WaazVenue(data);
    let savedWaazVenueData = await newWaazVenue.save();
    return apiResponse(ADD_SUCCESS, "Waaz Venue", savedWaazVenueData, res);
  } else {
    data.updatedBy = req.user._id;
    let updatedWaazVenueData = await WaazVenue.findByIdAndUpdate(id, data, {
      new: true,
      runValidators: true,
    });

    if (!updatedWaazVenueData) {
      return apiError(NOT_FOUND, "Waaz Venue", null, res);
    }

    return apiResponse(UPDATE_SUCCESS, "Waaz Venue", updatedWaazVenueData, res);
  }
});

const getSingleWaazVenue = apiHandler(async (req, res) => {
  const { id } = req.params;
  const waazVenueData = await WaazVenue.findById(id)
    .populate("miqaatID", "name")
    .populate("arazCityID", "name")
    .populate("mawaidVenues", "name")
    .populate("arazCityZoneID", "name")
    .populate("waazVenueTypeID", "name")
    .populate("waazVenueSuitabilityID", "name")
    .populate("waazVenuePriorityID", "name")
    .populate("createdBy", "name")
    .populate("updatedBy", "name");

  if (!waazVenueData) return apiError(NOT_FOUND, "Waaz Venue", null, res);

  return apiResponse(FETCH, "Waaz Venue", waazVenueData, res);
});

const deleteWaazVenue = apiHandler(async (req, res) => {
  const { id } = req.params;

  const waazVenueData = await WaazVenue.findOneAndDelete({ _id: id });

  if (!waazVenueData) return apiError(NOT_FOUND, "Waaz Venue", null, res);

  return apiResponse(DELETE_SUCCESS, "Waaz Venue", null, res);
});

const approveWaazVenue = apiHandler(async (req, res) => {
  const { approvalStatus, id } = req.body;

  const updateData = {
    approvalStatus,
    updatedBy: req.user?._id || undefined,
  };

  const waazVenueData = await WaazVenue.findOneAndUpdate(
    { _id: id },
    updateData,
    { new: true }
  );

  if (!waazVenueData) {
    return apiError(NOT_FOUND, "Waaz Venue", null, res);
  }

  return apiResponse(CUSTOM_SUCCESS, "Waaz Venue Status Updated", null, res);
});

const activateWaazVenue = apiHandler(async (req, res) => {
  const { status, id } = req.body;

  const updateData = {
    status,
    updatedBy: req.user?._id || undefined,
  };

  const waazVenueData = await WaazVenue.findOneAndUpdate(
    { _id: id },
    updateData,
    { new: true }
  );

  if (!waazVenueData) {
    return apiError(NOT_FOUND, "Waaz Venue", null, res);
  }

  return apiResponse(CUSTOM_SUCCESS, "Waaz Venue Status Updated", null, res);
});

const finalCapacityWaazVenue = apiHandler(async (req, res) => {
  const { id, finalCapacity } = req.body;
  const waazVenue = await WaazVenue.findOneAndUpdate(
    { _id: id },
    { finalizedWaazSeatingCapacity: finalCapacity }
  );

  if (!waazVenue) {
    return apiError(NOT_FOUND, "Waaz venue", null, res);
  }

  return apiResponse(UPDATE_SUCCESS, "Final Capacity", null, res);
});

const statusTypes = {
  DRAFT: "DRAFT",
  APPROVED: "APPROVED",
  UNLOCKED: "UNLOCKED",
};

const venueTypes = {
  WAAZ_VENUE: "WAAZ_VENUE",
};

const handleS3Upload = async (file, arazCityZoneID) => {
  const fileDetails = {
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  };

  const preSignedURLs = await generatePreSignedURLs(
    "araz_city_waaz_venue_files",
    arazCityZoneID,
    [fileDetails]
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return null;
  }

  const { fileKey, preSignedURL } = preSignedURLs[0];
  const uploadResult = await uploadFileToS3(file, preSignedURL, fileKey);

  if (!uploadResult) {
    return null;
  }

  return { fileKey, fileDetails };
};

const validateZoneExists = async (arazCityZoneID) => {
  if (!arazCityZoneID) {
    return false;
  }

  const arazCityData = await ArazCityZone.findById(arazCityZoneID);
  return !!arazCityData;
};

const createFileDocument = (data, userId) => {
  return {
    arazCityZoneID: data.arazCityZoneID,
    miqaatID: data.miqaatID,
    arazCityID: data.arazCityID,
    fileData: {
      fileName: data.fileDetails.fileName,
      fileType: data.fileDetails.fileType,
      fileSize: data.fileDetails.fileSize,
      fileKey: data.fileKey,
    },
    venueType: venueTypes.WAAZ_VENUE,
    functionID: data.functionID,
    venueID: data.venueID,
    userID: userId,
    status: data.status || statusTypes.DRAFT,
  };
};

const getRecentFile = async (query) => {
  return await ArazCityZoneFile.findOne({
    ...query,
  }).sort({ createdAt: -1 });
};

const uploadWaazVenueFile = apiHandler(async (req, res) => {
  if (!req.file) {
    return apiError(CUSTOM_ERROR, "No file uploaded", null, res);
  }

  const { arazCityZoneID, functionID, miqaatID, arazCityID } = req.body;
  const venueID = req.body.waazVenueID;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  if (!(await validateZoneExists(arazCityZoneID))) {
    return apiResponse(CUSTOM_ERROR, "Invalid arazCityZoneID", null, res);
  }

  const recentFile = await getRecentFile({
    arazCityZoneID,
    functionID,
    venueID,
    venueType: venueTypes.WAAZ_VENUE,
    miqaatID,
    arazCityID, // Add this field
  });

  if (recentFile?.status === statusTypes.APPROVED) {
    return apiResponse(
      CUSTOM_ERROR,
      "An approved file already exists. Please unlock it before uploading a new file.",
      { fileId: recentFile._id },
      res
    );
  }

  const uploadResult = await handleS3Upload(req.file, arazCityZoneID);
  if (!uploadResult) {
    return apiResponse(CUSTOM_ERROR, "Failed to upload file", null, res);
  }

  const { fileKey, fileDetails } = uploadResult;

  let fileDocument;
  if (recentFile?.status === statusTypes.DRAFT) {
    recentFile.fileData = {
      fileName: fileDetails.fileName,
      fileType: fileDetails.fileType,
      fileSize: fileDetails.fileSize,
      fileKey: fileKey,
    };
    recentFile.userID = req.user._id;

    fileDocument = await recentFile.save();
  } else {
    const newFileData = createFileDocument(
      {
        arazCityZoneID,
        arazCityID, // Add this field
        fileDetails,
        fileKey,
        functionID,
        venueID,
        miqaatID,
      },
      req.user._id
    );

    fileDocument = await ArazCityZoneFile.create(newFileData);
  }

  return apiResponse(
    CUSTOM_SUCCESS,
    "WaazVenue File Uploaded Successfully",
    fileDocument,
    res
  );
});

const updateWaazVenueFileStatus = apiHandler(async (req, res) => {
  const { fileId, status } = req.body;

  if (!fileId || !status) {
    return apiResponse(
      CUSTOM_ERROR,
      "FileId and status are required",
      null,
      res
    );
  }

  if (!Object.values(statusTypes).includes(status)) {
    return apiResponse(CUSTOM_ERROR, "Invalid status value", null, res);
  }

  const existingFile = await ArazCityZoneFile.findById(fileId);
  if (!existingFile) {
    return apiResponse(CUSTOM_ERROR, "File not found", null, res);
  }

  // Ensure we're only handling WAAZ_VENUE type files
  if (existingFile.venueType !== venueTypes.WAAZ_VENUE) {
    return apiResponse(
      CUSTOM_ERROR,
      "Only WAAZ_VENUE type files can be handled",
      null,
      res
    );
  }

  if (existingFile.status === status) {
    return apiResponse(
      CUSTOM_ERROR,
      `File is already in ${status} status`,
      null,
      res
    );
  }

  if (
    status === statusTypes.UNLOCKED &&
    existingFile.status !== statusTypes.APPROVED
  ) {
    return apiResponse(
      CUSTOM_ERROR,
      "Only files with 'approved' status can be changed to 'unlocked'",
      null,
      res
    );
  }

  if (status === statusTypes.APPROVED) {
    const query = {
      arazCityZoneID: existingFile.arazCityZoneID,
      arazCityID: existingFile.arazCityID, // Add this field
      functionID: existingFile.functionID,
      venueID: existingFile.venueID,
      venueType: venueTypes.WAAZ_VENUE,
      miqaatID: existingFile.miqaatID,
    };
    const recentFile = await ArazCityZoneFile.findOne({
      ...query,
      _id: { $ne: fileId },
    }).sort({ createdAt: -1 });

    if (recentFile && recentFile.status === statusTypes.APPROVED) {
      return apiResponse(
        CUSTOM_ERROR,
        "An approved file already exists. Please unlock it first.",
        { fileId: recentFile._id },
        res
      );
    }
  }

  const newFileData = createFileDocument(
    {
      arazCityZoneID: existingFile.arazCityZoneID,
      arazCityID: existingFile.arazCityID, // Add this field
      miqaatID: existingFile.miqaatID,
      fileDetails: {
        fileName: existingFile.fileData.fileName,
        fileType: existingFile.fileData.fileType,
        fileSize: existingFile.fileData.fileSize,
      },
      fileKey: existingFile.fileData.fileKey,
      functionID: existingFile.functionID,
      venueID: existingFile.venueID,
      venueType: existingFile.venueType,
      status: status,
    },
    req.user._id
  );

  const newFileDocument = await ArazCityZoneFile.create(newFileData);

  return apiResponse(
    CUSTOM_SUCCESS,
    `WaazVenue file status updated to ${status}`,
    { fileDocument: newFileDocument },
    res
  );
});

const getWaazVenueFiles = apiHandler(async (req, res) => {
  const { arazCityZoneID, functionID, miqaatID, arazCityID } = req.body;
  const venueID = req.body.waazVenueID;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const query = {
    arazCityZoneID,
    venueType: venueTypes.WAAZ_VENUE,
    miqaatID,
    arazCityID,
  };

  if (venueID) query.venueID = venueID;
  if (functionID) query.functionID = functionID;

  const files = await ArazCityZoneFile.find(query)
    .sort({ createdAt: 1 })
    .populate("userID", "name email");

  return apiResponse(
    CUSTOM_SUCCESS,
    "WaazVenue files retrieved successfully",
    { files },
    res
  );
});

const getWaazVenueMasterFiles = apiHandler(async (req, res) => {
  const { arazCityZoneID, miqaatID, arazCityID } = req.body;
  const venueID = req.body.waazVenueID;
  const query = {
    status: statusTypes.APPROVED,
    arazCityZoneID,
    arazCityID,
    venueType: "ZONES",
    miqaatID,
  };
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  if (venueID) query.venueID = venueID;

  const masterFiles = await ArazCityZoneFile.findOne(query)
    .sort({ updatedAt: -1 })
    .populate("userID", "name email");

  if (isEmpty(masterFiles)) {
    return apiError(NOT_FOUND, "WaazVenue master file", null, res);
  }

  return apiResponse(
    CUSTOM_SUCCESS,
    "WaazVenue master files retrieved successfully",
    { masterFiles },
    res
  );
});

const getWaazVenueFileDownloadURL = apiHandler(async (req, res) => {
  const { fileKey } = req.body;

  if (!fileKey) {
    return apiResponse(CUSTOM_ERROR, "File key is required", null, res);
  }

  const preSignedURL = await generateGetPreSignedURL(fileKey);

  return apiResponse(
    FETCH,
    "WaazVenue file download URL generated",
    { preSignedURL },
    res
  );
});

const deleteWaazVenueFile = apiHandler(async (req, res) => {
  const { id } = req.params;
  const currentUserId = req.user._id;

  const file = await ArazCityZoneFile.findById(id);

  if (!file) {
    return apiError(NOT_FOUND, "WaazVenue file", null, res);
  }

  if (file.venueType !== venueTypes.WAAZ_VENUE) {
    return apiResponse(
      CUSTOM_ERROR,
      "Only WAAZ_VENUE type files can be deleted using this endpoint",
      null,
      res
    );
  }

  if (file.status === statusTypes.APPROVED) {
    return apiResponse(
      CUSTOM_ERROR,
      "Approved files cannot be deleted. Please unlock the file first.",
      null,
      res
    );
  }

  if (file.userID.toString() !== currentUserId.toString()) {
    return apiResponse(
      CUSTOM_ERROR,
      "You can only delete files that you have uploaded",
      null,
      res
    );
  }

  const fileData = await ArazCityZoneFile.findByIdAndDelete(id);

  return apiResponse(DELETE_SUCCESS, "WaazVenue file", null, res);
});

module.exports = {
  uploadWaazVenueFile,
  updateWaazVenueFileStatus,
  getWaazVenueFiles,
  getWaazVenueMasterFiles,
  getWaazVenueFileDownloadURL,
  deleteWaazVenueFile,
  getAllWaazVenue,
  addEditWaazVenue,
  getSingleWaazVenue,
  deleteWaazVenue,
  approveWaazVenue,
  activateWaazVenue,
  finalCapacityWaazVenue,
};
