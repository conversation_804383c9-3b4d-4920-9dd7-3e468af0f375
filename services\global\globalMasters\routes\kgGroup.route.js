const { validate } = require("../../../middlewares/validation.middleware");

const {
  getAllKGGroups,
  addKGGroup,
  getSingleKGGroup,
  editKGGroup,
  deleteKGGroup,
} = require("../controllers/kgGroup.controller");

const {
  addKGGroupSchema,
  getSingleKGGroupSchema,
  editKGGroupSchema,
  deleteKGGroupSchema,
} = require("../validations/kgGroup.validation");
const router = require("express").Router();

router.get("/get", getAllKGGroups);

router.post(
  "/add",
  validate(addKGGroupSchema, "body"),
  addKGGroup
);

router.get(
  "/get/:id",
  validate(getSingleKGGroupSchema, "params"),
  getSingleKGGroup
);

router.put(
  "/edit",
  validate(editKGGroupSchema, "body"),
  editKGGroup
);

router.delete(
  "/delete/:id",
  validate(deleteKGGroupSchema, "params"),
  deleteKGGroup
);

module.exports = router;

