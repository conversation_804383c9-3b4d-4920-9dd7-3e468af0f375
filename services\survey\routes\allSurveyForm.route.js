const express = require("express");
const router = express.Router();

const {
  getAllSurveys,
  addEditSurvey,
  deleteSurvey,
  getSingleSurvey,
  getSingleSurveyWithoutMembers,
} = require("../controllers/surveyForm.controller");
const { validate } = require("../../../middlewares/validation.middleware");
const {
  addEditSurveySchema,
  deleteSurveySchema,
  getSingleSurveySchema,
} = require("../validations/surveyForm.validation");
const { getSurveyResults } = require("../controllers/reports.controller");

router.get("/get", getAllSurveys);
router.get(
  "/get/without-members/:id",
  validate(getSingleSurveySchema, "params"),
  getSingleSurveyWithoutMembers
);
router.get(
  "/get/:id",
  validate(getSingleSurveySchema, "params"),
  getSingleSurvey
);
router.post("/add", validate(addEditSurveySchema, "body"), addEditSurvey);
router.put("/edit/:id", validate(addEditSurveySchema, "body"), addEditSurvey);
router.delete(
  "/delete/:id",
  validate(deleteSurveySchema, "params"),
  deleteSurvey
);
router.post(
  "/report/:id",
  validate(deleteSurveySchema, "params"),
  getSurveyResults
);

module.exports = router;
