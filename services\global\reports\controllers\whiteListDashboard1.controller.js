const { apiHandler, apiResponse, apiError } = require("../../../../utils/api.util");
const { FETCH } = require("../../../../utils/message.util");
const { Department } = require("../../../globalMasters/models/department.model");
const { TravelArazCity, KGUser } = require("../../../hierarchy/models");

const getWhiteListDashboard1 = apiHandler(async (req, res) => {

  const departments = await Department.find({ status: 'active' }).select('name departmentQuota');

  const travelCities = await TravelArazCity.find().select('name');

  const kgUsers = await KGUser.find({
    'smeRecommendation.departmentID': { $exists: true }
  })
    .populate('smeRecommendation.departmentID', 'name LDName')
    .populate('smeRecommendation.travelCities', 'name')
    .select('name LDName smeRecommendation');

  const columns = [
    { title: 'Department', name: 'department' },
    { title: 'Quota', name: 'quota' },
    { title: 'Filled', name: 'filled' } // Added Filled column
  ];

  travelCities.forEach(city => {
    columns.push({
      title: city.name,
      name: city._id.toString()
    });
  });

  columns.push({
    title: 'No Travel Cities Selected',
    name: 'noTravelCities' // Added No Travel Cities column
  });

  const rows = [];
  const totals = {
    quota: 0,
    filled: 0,
    noTravelCities: 0
  };

  travelCities.forEach(city => {
    totals[city._id.toString()] = 0;
  });

  departments.forEach(dept => {
    const row = {
      department: dept.name,
      quota: dept.departmentQuota || 0,
      filled: 0,
      noTravelCities: 0
    };

    travelCities.forEach(city => {
      row[city._id.toString()] = 0;
    });

    kgUsers.forEach(user => {
      const sme = user.smeRecommendation;
      const userDeptID = sme?.departmentID?._id?.toString();
      if (userDeptID === dept._id.toString()) {
        row.filled++;

        if (sme.travelCities && sme.travelCities.length > 0) {
          sme.travelCities.forEach(travelCity => {
            const cityKey = travelCity._id.toString();
            if (row.hasOwnProperty(cityKey)) {
              row[cityKey]++;
            }
          });
        } else {
          row.noTravelCities++;
        }
      }
    });

    rows.push(row);
    totals.quota += (dept.departmentQuota || 0);
    totals.filled += row.filled;
    totals.noTravelCities += row.noTravelCities;
  });

  rows.forEach(row => {
    travelCities.forEach(city => {
      const cityKey = city._id.toString();
      totals[cityKey] += row[cityKey];
    });
  });
  rows.sort((a, b) => a.department.localeCompare(b.department));

  const data = {
    columns,
    rows,
    total: totals
  };

  return apiResponse(
    FETCH,
    "White List Dashboard 1",
    data,
    res
  );
});


module.exports = {
  getWhiteListDashboard1,
};
