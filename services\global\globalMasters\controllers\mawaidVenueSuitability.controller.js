const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
} = require("../../../utils/misc.util");
const { MawaidVenueSuitability, MawaidVenue } = require("../../zonesCapacity/models");

const getAllMawaidVenueSuitabilities = apiHandler(async (req, res) => {
  const mawaidVenueSuitabilities = await MawaidVenueSuitability.find().sort({
    _id: -1,
  });
  return apiResponse(
    FETCH,
    "Mawaid Venue Suitabilities",
    mawaidVenueSuitabilities,
    res
  );
});

const addEditMawaidVenueSuitability = apiHandler(async (req, res) => {
  const { id } = req.body;
  let data = req.body;

  data.uniqueName = getUniqueName(data.name);

  const uniqueNameQueryObject = { uniqueName: data.uniqueName };

  if (id) uniqueNameQueryObject._id = { $ne: id };

  const existingMawaidVenueSuitability = await MawaidVenueSuitability.findOne(
    uniqueNameQueryObject
  );

  if (existingMawaidVenueSuitability) {
    return apiError(
      CUSTOM_ERROR,
      "Mawaid Venue Suitability with this name already exists",
      null,
      res
    );
  }

  if (!id) {
    data.createdBy = req.user._id;

    const newMawaidVenueSuitability = new MawaidVenueSuitability(data);

    let savedMawaidVenueSuitabilityData =
      await newMawaidVenueSuitability.save();

    return apiResponse(
      ADD_SUCCESS,
      "Mawaid Venue Suitability",
      savedMawaidVenueSuitabilityData,
      res
    );
  } else {
    data.updatedBy = req.user._id;

    let updatedMawaidVenueSuitabilityData =
      await MawaidVenueSuitability.findByIdAndUpdate(id, data, {
        new: true,
        runValidators: true,
      });

    if (!updatedMawaidVenueSuitabilityData) {
      return apiError(NOT_FOUND, "Mawaid Venue Suitability", null, res);
    }

    return apiResponse(
      UPDATE_SUCCESS,
      "Mawaid Venue Suitability",
      updatedMawaidVenueSuitabilityData,
      res
    );
  }
});

const getSingleMawaidVenueSuitability = apiHandler(async (req, res) => {
  const { id } = req.params;
  const mawaidVenueSuitabilityData = await MawaidVenueSuitability.findById(id);

  if (!mawaidVenueSuitabilityData)
    return apiError(NOT_FOUND, "Mawaid Venue Suitability", null, res);

  return apiResponse(
    FETCH,
    "Mawaid Venue Suitability",
    mawaidVenueSuitabilityData,
    res
  );
});

const deleteMawaidVenueSuitability = apiHandler(async (req, res) => {
  const { id } = req.params;

  const existsInMawaidVenue = await MawaidVenue.exists({
    mawaidVenueSuitabilityID: id,
  });

  if (existsInMawaidVenue) {
    return apiError(
      CUSTOM_ERROR,
      "Couldn't Delete Mawaid Venue Suitability as present in a Mawaid Venue",
      null,
      res
    );
  }

  const mawaidVenueSuitabilityData =
    await MawaidVenueSuitability.findOneAndDelete({
      _id: id,
    });

  if (!mawaidVenueSuitabilityData)
    return apiError(NOT_FOUND, "Mawaid Venue Suitability", null, res);

  return apiResponse(DELETE_SUCCESS, "Mawaid Venue Suitability", null, res);
});

module.exports = {
  getAllMawaidVenueSuitabilities,
  addEditMawaidVenueSuitability,
  getSingleMawaidVenueSuitability,
  deleteMawaidVenueSuitability,
};
