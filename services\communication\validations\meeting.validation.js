const Joi = require("joi");
const {
  stringValidation,
  numberValidation,
  booleanValidation,
  dateValidation,
  urlValidation,
  idValidation,
  arrayIdValidation,
  arrayStringValidation,
  messages,
} = require("../../../utils/validator.util");
const { recipientMeta } = require("../models");

// Time validation schema
const timeValidation = Joi.object({
  hh: numberValidation.integer().min(1).max(12).optional().label("Hour"),
  mm: numberValidation.integer().min(0).max(59).optional().label("Minute"),
  period: stringValidation.valid("AM", "PM").optional().label("Period"),
});

// Member validation schema
const memberValidation = Joi.object({
  arazCityID: idValidation.required().label("ArazCity ID"),
  memberType: Joi.string()
    .trim()
    .required()
    .valid(...recipientMeta.allTypes)
    .label("Message type")
    .messages({
      "any.only": "Message type is required",
      "string.empty": "Message type is required",
    }),
  departmentIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.departmentTypes),
    then: arrayIdValidation.min(1),
  }),
  zoneIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.zoneTypes),
    then: arrayIdValidation.min(1),
  }),
  positionIDs: arrayIdValidation.optional().default([]).label("Position IDs"),
  kgGroupIDs: arrayIdValidation.optional().default([]).label("KG Group IDs"),
  kgTypeIDs: arrayIdValidation.optional().default([]).label("KG Type IDs"),
  ITSIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.ITSTypes),
    then: arrayStringValidation.min(1),
  }),
}).unknown();

// Assignee validation schema (updated to match DB structure)
const assigneeValidation = Joi.object({
  arazCityID: idValidation.required().label("ArazCity ID"),
  memberType: Joi.string()
    .trim()
    .required()
    .valid(...recipientMeta.allTypes)
    .label("Message type")
    .messages({
      "any.only": "Message type is required",
      "string.empty": "Message type is required",
    }),
  departmentIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.departmentTypes),
    then: arrayIdValidation.min(1),
  }),
  zoneIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.zoneTypes),
    then: arrayIdValidation.min(1),
  }),
  positionIDs: arrayIdValidation.optional().default([]).label("Position IDs"),
  kgGroupIDs: arrayIdValidation.optional().default([]).label("KG Group IDs"),
  kgTypeIDs: arrayIdValidation.optional().default([]).label("KG Type IDs"),
  ITSIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.ITSTypes),
    then: arrayStringValidation.min(1),
  }),
}).unknown();

// Agenda validation schema (updated to match DB structure)
const agendaValidation = Joi.object({
  language: stringValidation.optional().label("Language"),
  agenda: stringValidation.optional().label("Agenda"),
  proceedings: stringValidation.optional().allow("").label("Proceedings"),
  actionable: stringValidation.optional().allow("").label("Actionable"),
  // Changed from single assignee object to array of assignees matching DB schema
  assignee: Joi.array()
    .items(assigneeValidation)
    .optional()
    .default([])
    .label("Assignee"),
  // Added fields from DB schema
  systemRoles: arrayIdValidation.optional().default([]).label("System Roles"),
  ITSIDs: arrayIdValidation.optional().default([]).label("ITS User IDs"),
  _id: idValidation.optional().label("Agenda ID"),
});

// Base meeting validation schema
const baseMeetingValidation = Joi.object({
  miqaatID: idValidation.optional().allow(null, "").label("Miqaat ID"),
  title: stringValidation.required().label("Title"),
  hostedBy: idValidation.optional().label("Host").allow(""),
  date: dateValidation.optional().allow("").label("Date"),
  time: timeValidation.optional().label("Time").allow("", null),
  meetingUrl: urlValidation.optional().allow("", null).label("Meeting URL"),
  meetingAddress: stringValidation
    .optional()
    .allow("", null)
    .label("Meeting Address"),
  invities: Joi.array()
    .items(memberValidation)
    .optional()
    .allow(null)
    .label("Invitees"),
  systemRoles: arrayIdValidation.optional().default([]).label("System Roles"),
  // Changed ITSIDs to arrayIdValidation to match DB schema (ObjectId references)
  ITSIDs: arrayIdValidation.optional().default([]).label("ITS User IDs"),
  agendas: Joi.array()
    .items(agendaValidation)
    .optional()
    .default([])
    .label("Agendas"),
  mom: stringValidation.optional().allow("", null).label("Minutes of Meeting"),
  meetingCreatedBy: idValidation.optional().label("Meeting Creator"),
});

// Schema for adding new meeting
const addMeetingSchema = baseMeetingValidation.fork(
  [
    "miqaatID",
    "meetingUrl",
    "meetingAddress",
    "systemRoles",
    "ITSIDs",
    "agendas",
    "mom",
  ],
  (schema) => schema.optional()
);

// Schema for editing existing meeting
const editMeetingSchema = baseMeetingValidation.fork(
  [
    "miqaatID",
    "title",
    "hostedBy",
    "date",
    "time",
    "meetingUrl",
    "meetingAddress",
    "invities",
    "systemRoles",
    "ITSIDs",
    "agendas",
    "mom",
    "meetingCreatedBy",
  ],
  (schema) => schema.optional()
);

// Schema for partial updates (PATCH)
const patchMeetingSchema = baseMeetingValidation
  .keys({
    _id: idValidation.required().label("Meeting ID"),
  })
  .fork(Object.keys(baseMeetingValidation.describe().keys), (schema) =>
    schema.optional()
  );

// Schema for getting meeting by ID (params validation)
const getMeetingParamsSchema = Joi.object({
  id: idValidation.required().label("Meeting ID"),
});

const getAllMeetingsSchema = Joi.object({
  miqaatID: idValidation,
});

const sendReminderToAgendaAssigneesSchema = Joi.object({
  meetingId: idValidation.required().label("Meeting ID"),
  agendaId: idValidation.required().label("Agenda ID"),
});

// Schema for query parameters (list meetings)
const getMeetingsQuerySchema = Joi.object({
  page: numberValidation.integer().min(1).optional().default(1).label("Page"),
  limit: numberValidation
    .integer()
    .min(1)
    .optional()
    .default(10)
    .label("Limit"),
  hostedBy: idValidation.optional().label("Hosted By"),
  arazCityID: idValidation.optional().label("ArazCity ID"),
  startDate: dateValidation.optional().label("Start Date"),
  endDate: dateValidation.optional().label("End Date"),
  search: stringValidation.optional().label("Search Term"),
});

// Schema for adding agenda to existing meeting
const addAgendaSchema = Joi.object({
  meetingId: idValidation.required().label("Meeting ID"),
  agenda: agendaValidation.required().label("Agenda"),
});

// Schema for updating agenda
const updateAgendaSchema = Joi.object({
  meetingId: idValidation.required().label("Meeting ID"),
  agendaIndex: numberValidation
    .integer()
    .min(0)
    .required()
    .label("Agenda Index"),
  agenda: agendaValidation.required().label("Agenda"),
});

// Schema for adding invitee to existing meeting
const addInviteeSchema = Joi.object({
  meetingId: idValidation.required().label("Meeting ID"),
  invitee: memberValidation.required().label("Invitee"),
});

// Schema for removing invitee
const removeInviteeSchema = Joi.object({
  meetingId: idValidation.required().label("Meeting ID"),
  inviteeIndex: numberValidation
    .integer()
    .min(0)
    .required()
    .label("Invitee Index"),
});

// Schema for updating meeting status
const updateMeetingStatusSchema = Joi.object({
  _id: idValidation.required().label("Meeting ID"),
  status: stringValidation
    .valid("scheduled", "ongoing", "completed", "cancelled")
    .required()
    .label("Status"),
});

const getRecipientsSchema = Joi.object({
  miqaatID: idValidation.optional().allow(null, ""),
  arazCityID: idValidation.optional().allow(null, ""),
  memberType: stringValidation.valid(...recipientMeta.allTypes, "SYSTEM_ROLE"),
  departmentIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.departmentTypes),
    then: arrayIdValidation,
  }),
  zoneIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.zoneTypes),
    then: arrayIdValidation,
  }),
  positionIDs: arrayIdValidation.optional(),
  systemRoles: arrayIdValidation.optional(),
  kgTypeIDs: arrayIdValidation.optional(),
  kgGroupIDs: arrayIdValidation.optional(),
  ITSIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.ITSTypes),
    then: arrayStringValidation,
  }),
});

// Individual component schemas for reuse
const timeSchema = timeValidation;
const memberSchema = memberValidation;
const agendaSchema = agendaValidation;
const assigneeSchema = assigneeValidation;

module.exports = {
  // Main schemas for CRUD operations
  addMeetingSchema,
  editMeetingSchema,
  patchMeetingSchema,
  getMeetingParamsSchema,
  getMeetingsQuerySchema,

  // Additional operation schemas
  addAgendaSchema,
  updateAgendaSchema,
  addInviteeSchema,
  removeInviteeSchema,
  updateMeetingStatusSchema,

  // Individual component schemas for reuse
  timeSchema,
  memberSchema,
  agendaSchema,
  assigneeSchema,
  baseMeetingValidation,
  sendReminderToAgendaAssigneesSchema,

  getRecipientsSchema,
  getAllMeetingsSchema,
};
