const constants = require("../../../constants");
const { KGUser } = require("../../hierarchy/models");
const { Meeting, messageTypes } = require("../models");
const {
  apiHandler,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const {
  toObjectId,
  isEmpty,
  sanitizeHtml,
} = require("../../../utils/misc.util");
const {
  sendPushNotifications,
  notificationTemplate,
} = require("../../../utils/oneSignal.util");
const {
  NOT_FOUND,
  ADD_SUCCESS,
  FETCH,
  ADD_ERROR,
  CUSTOM_SUCCESS,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
} = require("../../../utils/message.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const createMeeting = apiHandler(async (req, res) => {
  const { invities, miqaatID, agendas, systemRoles } = req.body;
  const userID = req.user._id;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  let meetingMembers = [];

  if (invities && miqaatID && invities.length > 0) {
    await Promise.all(
      invities.map(async (invite) => {
        const members = await getMember(userID, { ...invite, miqaatID }).then(
          (res) => res.userIDs
        );
        invite["members"] = members;
        meetingMembers.push(...members);
        return invite;
      })
    );
  }

  if (agendas && agendas.length > 0) {
    await Promise.all(
      agendas.map(async (agenda) => {
        const agendaMembers = [];
        if (agenda.assignee && miqaatID && agenda.assignee.length > 0) {
          await Promise.all(
            agenda.assignee.map(async (assign) => {
              const members = await getMember(userID, {
                ...assign,
                miqaatID,
              });
              assign["members"] = members.userIDs;
              agendaMembers.push(...members.userIDs);
              return assign;
            })
          );
        }
        if (agenda.systemRoles && agenda.systemRoles.length > 0) {
          const members = await getMember(userID, {
            memberType: "SYSTEM_ROLE",
            systemRoles: agenda.systemRoles,
          });
          agendaMembers.push(...members.userIDs);
        }
        agenda["assigneeMembers"] = [
          ...new Set(agendaMembers.map((id) => id.toString())),
        ];
        return agenda;
      })
    );
  }

  if (systemRoles && systemRoles.length > 0) {
    const memeber = await getMember(userID, {
      memberType: "SYSTEM_ROLE",
      systemRoles,
    });
    meetingMembers.push(...memeber.userIDs);
  }
  meetingMembers = [...new Set(meetingMembers.map((id) => id.toString()))];

  const meetingData = {
    ...req.body,
    invities,
    agendas,
    meetingMembers,
    meetingCreatedBy: userID,
  };
  if (isEmpty(meetingData.hostedBy)) {
    delete meetingData.hostedBy;
  }
  if (isEmpty(meetingData.time)) {
    delete meetingData.time;
  }
  if (isEmpty(meetingData.miqaatID)) {
    delete meetingData.miqaatID;
  }

  const meeting = await Meeting.create(meetingData);
  if (!meeting) {
    return apiError(ADD_ERROR, "Meeting", null, res ? res : null);
  }

  return apiResponse(ADD_SUCCESS, "Meeting", meeting, res ? res : null);
});

const editMeeting = apiHandler(async (req, res) => {
  const { id: meetingID } = req.params;
  const { invities, miqaatID, agendas, systemRoles } = req.body;
  const userID = req.user._id;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  let meetingMembers = [];

  if (invities && miqaatID && invities.length > 0) {
    await Promise.all(
      invities.map(async (invite) => {
        const members = await getMember(userID, {
          ...invite,
          miqaatID,
        }).then((res) => res.userIDs);

        invite["members"] = members;
        meetingMembers.push(...members);
        return invite;
      })
    );
  }

  if (agendas && miqaatID && agendas.length > 0) {
    await Promise.all(
      agendas.map(async (agenda) => {
        const agendaMembers = [];
        if (agenda.assignee && agenda.assignee.length > 0) {
          await Promise.all(
            agenda.assignee.map(async (assign) => {
              const members = await getMember(userID, {
                ...assign,
                miqaatID,
              });
              assign["members"] = members.userIDs;
              agendaMembers.push(...members.userIDs);
              return assign;
            })
          );
        }
        if (agenda.systemRoles && agenda.systemRoles.length > 0) {
          const members = await getMember(userID, {
            memberType: "SYSTEM_ROLE",
            systemRoles: agenda.systemRoles,
          });
          agendaMembers.push(...members.userIDs);
        }
        agenda["assigneeMembers"] = [
          ...new Set(agendaMembers.map((id) => id.toString())),
        ];
      })
    );
  }

  if (systemRoles && systemRoles.length > 0) {
    const member = await getMember(userID, {
      memberType: "SYSTEM_ROLE",
      systemRoles,
    });
    meetingMembers.push(...member.userIDs);
  }
  meetingMembers = [...new Set(meetingMembers.map((id) => id.toString()))];
  const meetingData = {
    ...req.body,
    invities,
    agendas,
    meetingMembers,
    meetingCreatedBy: userID,
  };
  if (isEmpty(meetingData.time)) {
    delete meetingData.time;
  }
  if (isEmpty(meetingData.hostedBy)) {
    delete meetingData.hostedBy;
  }
  if (isEmpty(meetingData.miqaatID)) {
    delete meetingData.miqaatID;
  }
  const updatedMeeting = await Meeting.findByIdAndUpdate(
    meetingID,
    meetingData,
    {
      new: true,
      runValidators: true,
    }
  );

  if (isEmpty(updatedMeeting)) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }

  return apiResponse(UPDATE_SUCCESS, "Meeting", updatedMeeting, res);
});

const addEditAgenda = apiHandler(async (req, res) => {
  const { _id: agendaId } = req.body;
  const agenda = req.body;
  const { id: meetingId } = req.params;
  const userID = req.user._id;

  // Find the meeting first
  const meeting = await Meeting.findById(meetingId);
  if (!meeting) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }

  const { miqaatID } = meeting;
  let agendaMembers = [];

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  // Process assignees and get member IDs
  if (agenda.assignee && miqaatID && agenda.assignee.length > 0) {
    await Promise.all(
      agenda.assignee.map(async (assign) => {
        const members = await getMember(userID, {
          ...assign,
          miqaatID,
        });
        assign["members"] = members.userIDs;
        agendaMembers.push(...members.userIDs);
        return assign;
      })
    );
  }

  // Process system roles
  if (agenda.systemRoles && agenda.systemRoles.length > 0) {
    const members = await getMember(userID, {
      memberType: "SYSTEM_ROLE",
      systemRoles: agenda.systemRoles,
    });
    agendaMembers.push(...members.userIDs);
  }

  // Remove duplicates from agenda members
  agendaMembers = [...new Set(agendaMembers.map((id) => id.toString()))];

  // Add assigneeMembers to agenda object
  const processedAgenda = {
    ...agenda,
    assigneeMembers: agendaMembers,
  };

  let updatedMeeting;

  if (agendaId) {
    // Update existing agenda using MongoDB array update
    updatedMeeting = await Meeting.findOneAndUpdate(
      {
        _id: meetingId,
        "agendas._id": agendaId,
      },
      {
        $set: { "agendas.$": processedAgenda },
        $addToSet: { meetingMembers: { $each: agendaMembers } },
      },
      {
        new: true,
        runValidators: true,
      }
    );

    if (!updatedMeeting) {
      return apiError(NOT_FOUND, "Agenda", null, res);
    }

    const updatedAgenda = updatedMeeting.agendas.find(
      (item) => item._id.toString() === agendaId
    );

    return apiResponse(
      UPDATE_SUCCESS,
      "Agenda",
      {
        meeting: updatedMeeting,
        updatedAgenda: updatedAgenda,
      },
      res
    );
  } else {
    updatedMeeting = await Meeting.findByIdAndUpdate(
      meetingId,
      {
        $push: { agendas: processedAgenda },
        $addToSet: { meetingMembers: { $each: agendaMembers } },
      },
      {
        new: true,
        runValidators: true,
      }
    );

    if (!updatedMeeting) {
      return apiError(ADD_ERROR, "Agenda", null, res);
    }

    const newAgenda = updatedMeeting.agendas[updatedMeeting.agendas.length - 1];

    return apiResponse(
      ADD_SUCCESS,
      "Agenda",
      {
        meeting: updatedMeeting,
        newAgenda: newAgenda,
      },
      res
    );
  }
});

const getMeetings = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;
  const userId = req.user._id;
  const userSystemRoleId = req.user.systemRoleID;

  let findQuery = {};

  if (miqaatID) {
    findQuery.miqaatID = toObjectId(miqaatID);
  }
  // if (arazCityID) {
  //   findQuery["invities.arazCityID"] = toObjectId(arazCityID);
  // }

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  if (userSystemRoleId) {
    findQuery.$or = [
      { systemRoles: userSystemRoleId },
      { "agendas.systemRoles": userSystemRoleId },
      { ITSIDs: userId },
      { "agendas.ITSIDs": userId },
      { meetingCreatedBy: userId },
    ];
  } else {
    findQuery.$or = [
      { "invities.members": userId },
      { ITSIDs: userId },
      { meetingCreatedBy: userId },
    ];
  }
  if (userSystemRoleId === constants.SYSTEM_ROLES.SUPER_ADMIN[0].toString()) {
    findQuery = {};
  }

  const meetingData = await Meeting.find(findQuery)
    .select("title date time _id meetingCreatedBy")
    .sort({ updatedAt: -1 })
    .lean();

  if (isEmpty(meetingData)) {
    return apiError(NOT_FOUND, "Meetings", null, res);
  }

  await Promise.all(
    meetingData.map(async (meeting) => {
      meeting["allowEdit"] =
        meeting.meetingCreatedBy.toString() === String(userId);
      return meeting;
    })
  );

  return apiResponse(FETCH, "Meetings", meetingData, res);
});

const getAllMeetings = apiHandler(async (req, res) => {
  const { miqaatID } = req.body;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  const userId = req.user._id;

  let findQuery = {};

  if (miqaatID) {
    findQuery.miqaatID = toObjectId(miqaatID);
  }
  const meetingData = await Meeting.find(findQuery)
    .select("title date time _id meetingCreatedBy")
    .populate({
      path: "meetingCreatedBy",
      select: "name ITSID",
    })
    .sort({ updatedAt: -1 })
    .lean();

  if (isEmpty(meetingData)) {
    return apiError(NOT_FOUND, "Meetings", null, res);
  }

  await Promise.all(
    meetingData.map(async (meeting) => {
      meeting["allowEdit"] =
        meeting?.meetingCreatedBy?._id.toString() === String(userId);
      return meeting;
    })
  );

  return apiResponse(FETCH, "Meetings", meetingData, res);
});

const getMeeting = apiHandler(async (req, res) => {
  const { id: meetingID } = req.params;

  const kgUserSelectFields = "name ITSID logo LDName phone gender";

  const meeting = await Meeting.findById(meetingID)
    .populate([
      {
        path: "hostedBy",
        select: kgUserSelectFields,
      },
      {
        path: "miqaatID",
        select: "name description",
      },
      {
        path: "invities.arazCityID",
        select: "name showPositionAlias",
      },
      {
        path: "invities.departmentIDs",
        select: "name",
      },
      {
        path: "invities.zoneIDs",
        select: "name",
      },
      {
        path: "invities.positionIDs",
        select: "name alias",
      },
      {
        path: "invities.kgGroupIDs",
        select: "name",
      },
      {
        path: "invities.kgTypeIDs",
        select: "name",
      },
      {
        path: "agendas.assignee.arazCityID",
        select: "name showPositionAlias",
      },
      {
        path: "agendas.assignee.departmentIDs",
        select: "name",
      },
      {
        path: "agendas.assignee.zoneIDs",
        select: "name",
      },
      {
        path: "agendas.assignee.positionIDs",
        select: "name alias",
      },
      {
        path: "agendas.assignee.kgGroupIDs",
        select: "name",
      },
      {
        path: "agendas.assignee.kgTypeIDs",
        select: "name",
      },
      {
        path: "agendas.systemRoles",
        select: "name",
      },
      {
        path: "agendas.ITSIDs",
        select: kgUserSelectFields,
      },
      {
        path: "agendas.assigneeMembers",
        select: kgUserSelectFields,
      },
      {
        path: "systemRoles",
        select: "name",
      },
      {
        path: "ITSIDs",
        select: kgUserSelectFields,
      },
      {
        path: "meetingCreatedBy",
        select: "name ITSID logo LDName",
      },
    ])
    .lean();

  if (!meeting) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }

  // Refetch meeting members based on invitee conditions
  let allMeetingMembers = [];

  if (meeting.invities && meeting.invities.length > 0) {
    await Promise.all(
      meeting.invities.map(async (invite) => {
        const memberData = await getMember(req.user._id, {
          departmentIDs: invite.departmentIDs.map((e) => e._id),
          zoneIDs: invite.zoneIDs.map((e) => e._id),
          positionIDs: invite.positionIDs.map((e) => e._id),
          kgTypeIDs: invite.kgTypeIDs.map((e) => e._id),
          kgGroupIDs: invite.kgGroupIDs.map((e) => e._id),
          ITSIDs: invite.ITSIDs,
          memberType: invite.memberType,
          miqaatID: meeting.miqaatID?._id || meeting.miqaatID,
          arazCityID: invite.arazCityID?._id || invite.arazCityID,
        });

        // Add the member data with miqaat information to the invite
        invite.memberDetails = memberData.existingUsersData;
        allMeetingMembers.push(...memberData.existingUsersData);

        return invite;
      })
    );
  }

  // Handle system roles at meeting level
  if (meeting.systemRoles && meeting.systemRoles.length > 0) {
    const systemRoleMembers = await getMember(req.user._id, {
      memberType: "SYSTEM_ROLE",
      systemRoles: meeting.systemRoles.map((role) => role._id || role),
    });
    allMeetingMembers.push(...systemRoleMembers.existingUsersData);
  }

  // Handle ITSIDs at meeting level
  if (meeting.ITSIDs && meeting.ITSIDs.length > 0) {
    const itsMembers = await getMember(req.user._id, {
      memberType: "BY_ITS",
      ITSIDs: meeting.ITSIDs.map((user) => user.ITSID || user),
    });
    allMeetingMembers.push(...itsMembers.existingUsersData);
  }

  delete meeting.meetingMembers;
  // Remove duplicates from all meeting members
  meeting.meetingMembers = allMeetingMembers.filter(
    (member, index, self) =>
      index ===
      self.findIndex((m) => m._id.toString() === member._id.toString())
  );

  // Remove the old meetingMembers array and replace with detailed member info

  return apiResponse(FETCH, "Meeting", meeting, res);
});
const deleteMeeting = apiHandler(async (req, res) => {
  const { id } = req.params;

  const meeting = await Meeting.findById(id);
  if (!meeting) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    meeting.miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  const meetingData = await Meeting.findByIdAndDelete(id);
  if (!meetingData) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }

  return apiResponse(DELETE_SUCCESS, "Meeting", null, res);
});

const addMOM = apiHandler(async (req, res) => {
  const { id, mom } = req.body;

  const meeting = await Meeting.findById(id);
  if (!meeting) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    meeting.miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  const updatedMeeting = await Meeting.findByIdAndUpdate(
    id,
    { mom },
    { new: true, runValidators: true }
  );

  if (!updatedMeeting) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }
  return apiResponse(ADD_SUCCESS, "Minutes of Meeting", updatedMeeting, res);
});

const getMember = async (userID, data) => {
  const {
    miqaatID,
    arazCityID,
    memberType,
    departmentIDs,
    zoneIDs,
    positionIDs,
    kgTypeIDs,
    kgGroupIDs,
    ITSIDs,
    systemRoles,
  } = data;

  const query = {
    _id: { $ne: constants.AMS_SYSTEMID },
  };

  const orConditions = [];

  const miqaatCondition = {};

  if (miqaatID) {
    miqaatCondition.miqaats = {
      $elemMatch: {
        miqaatID: toObjectId(miqaatID),
      },
    };
  }
  if (arazCityID) {
    miqaatCondition.miqaats = {
      $elemMatch: {
        arazCityID: toObjectId(arazCityID),
      },
    };
  }

  switch (memberType) {
    case "ALL_IN_HIERARCHY":
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = {
        $ne: null,
      };
      break;

    case "ALL_IN_DEPARTMENT":
      miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
        $in: toObjectId(departmentIDs),
      };
      break;

    case "ALL_IN_ZONE":
      miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
        $in: toObjectId(zoneIDs),
      };
      break;

    case "ALL_DEPARTMENT_HOD":
      miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
        $in: toObjectId(departmentIDs),
      };
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.HOD[0].toString()
      );
      break;

    case "ALL_ZONE_HOD":
      miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
        $in: toObjectId(zoneIDs),
      };
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.ZONE_HEAD[0].toString()
      );
      break;

    case "BY_ITS":
      miqaatCondition["ITSID"] = { $in: ITSIDs };
      break;

    case "CUSTOM":
      if (departmentIDs && departmentIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
          $in: toObjectId(departmentIDs),
        };
      }
      if (zoneIDs && zoneIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
          $in: toObjectId(zoneIDs),
        };
      }
      if (positionIDs && positionIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = {
          $in: toObjectId(positionIDs),
        };
      }
      if (kgTypeIDs && kgTypeIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["kgTypeID"] = {
          $in: toObjectId(kgTypeIDs),
        };
      }
      if (kgGroupIDs && kgGroupIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["kgGroupID"] = {
          $in: toObjectId(kgGroupIDs),
        };
      }
      break;

    case "PMO":
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.PMO[0].toString()
      );
      break;
  }

  if (Object.keys(miqaatCondition).length > 0) {
    orConditions.push(miqaatCondition);
  }

  if (systemRoles && systemRoles.length > 0) {
    orConditions.push({
      systemRoleID: { $in: toObjectId(systemRoles) },
    });
  }

  if (orConditions.length > 1) {
    query.$or = orConditions;
  } else if (orConditions.length === 1) {
    Object.assign(query, orConditions[0]);
  }

  const existingUsers = await KGUser.find(query)
    .select(
      "_id name ITSID miqaats logo phone whatsapp gender LDName systemRoleID"
    )
    .populate({
      path: "miqaats.hierarchyPositionID",
      select: "name alias",
    })
    .populate({
      path: "miqaats.departmentID",
      select: "name",
    })
    .populate({
      path: "miqaats.arazCityZoneID",
      select: "name",
    })
    .populate({
      path: "miqaats.arazCityID",
      select: "name",
    })
    .populate({
      path: "systemRoleID",
      select: "name",
    });

  const userIDs = existingUsers.map((user) => user._id);

  const existingUsersData = existingUsers.map((user) => {
    const matchedMiqaat = user.miqaats.find(
      (miqaat) =>
        miqaat?.miqaatID?.toString() === miqaatID?.toString() &&
        miqaat?.arazCityID?._id?.toString() === arazCityID?.toString()
    );

    return {
      _id: user._id,
      name: user.name,
      LDName: user.LDName,
      ITSID: user.ITSID,
      logo: user.logo,
      phone: user.phone,
      whatsappNumber: user.whatsapp,
      gender: user.gender,
      hierarchyPosition: matchedMiqaat?.hierarchyPositionID?.name || null,
      hierarchyAlias: matchedMiqaat?.hierarchyPositionID?.alias || null,
      department: matchedMiqaat?.departmentID?.name || null,
      zone: matchedMiqaat?.arazCityZoneID?.name || null,
      systemRole: user.systemRoleID?.name || null,
      arazCity: matchedMiqaat?.arazCityID?.name || null,
    };
  });

  return { userIDs, existingUsersData };
};

const formatMeetingDetails = (meeting) => {
  const formattedDate = meeting?.date
    ? new Intl.DateTimeFormat("en-IN", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      }).format(new Date(meeting.date))
    : "";
  const time = meeting?.time;
  const formattedTime = time
    ? `${time.hh}:${String(time.mm).padStart(2, "0")} ${time.period}`
    : "";

  let messageParts = [];

  if (meeting?.meetingUrl) {
    messageParts.push(`Online: ${meeting.meetingUrl}`);
  }

  if (meeting?.meetingAddress) {
    messageParts.push(`Address: ${meeting.meetingAddress}`);
  }

  if (formattedDate) {
    messageParts.push(`Date: ${formattedDate}`);
  }

  if (formattedTime) {
    messageParts.push(`Time: ${formattedTime}`);
  }

  // Static agenda - Tilawat Dua
  messageParts.push("\n\nAgenda - 1\n\n Tilawat Dua");

  // Add existing agendas if they exist
  if (meeting?.agendas && meeting.agendas.length > 0) {
    meeting.agendas.forEach((agenda, index) => {
      let agendaText = `\nAgenda-${index + 2}\n`; // Start from 2 since Tilawat Dua is 1

      if (agenda.agenda) {
        // Strip HTML tags and convert to plain text
        const plainAgenda = agenda.agenda.replace(/<[^>]*>/g, "").trim();
        agendaText += plainAgenda;
      } else {
        agendaText += "No content";
      }

      messageParts.push(agendaText);

      if (agenda.proceedings) {
        // Strip HTML tags and convert to plain text
        const plainProceedings = agenda.proceedings
          .replace(/<[^>]*>/g, "")
          .trim();
        messageParts.push(`\n\nProceedings:\n${plainProceedings}`);
      }

      if (agenda.actionable) {
        // Strip HTML tags and convert to plain text
        const plainActionable = agenda.actionable
          .replace(/<[^>]*>/g, "")
          .trim();
        messageParts.push(`\n\nAction:\n${plainActionable}`);
      }
    });
  }

  return {
    formattedDate,
    formattedTime,
    messageParts,
  };
};

const sendNotificationToInvitees = apiHandler(async (req, res) => {
  const { id: meetingID } = req.params;

  const meeting = await Meeting.findById(meetingID).lean();

  if (!meeting) {
    return apiError(NOT_FOUND, "Meeting not found", null, res);
  }

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    meeting.miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  // Get user IDs directly from meetingMembers array
  let userIds = meeting.meetingMembers
    ? meeting.meetingMembers.map((id) => id.toString())
    : [];
  // userIds.push(meeting.meetingCreatedBy.toString());
  userIds.push(meeting?.hostedBy?.toString());
  // Remove duplicates
  userIds = [...new Set(userIds)];

  if (userIds.length === 0) {
    return apiResponse(
      CUSTOM_SUCCESS,
      "No invitees found to send notification",
      { recipients: 0 },
      res
    );
  }

  // Verify users exist in database
  const existingUsers = await KGUser.find({ _id: { $in: userIds } }).select(
    "_id"
  );
  const validUserIds = existingUsers.map((user) => user._id.toString());

  if (validUserIds.length === 0) {
    return apiResponse(
      CUSTOM_SUCCESS,
      "No valid users found to send notification",
      { recipients: 0 },
      res
    );
  }

  const { messageParts } = formatMeetingDetails(meeting);

  const notificationLogData = {
    messageID: null,
    replyID: null,
    messageType: messageTypes.NOTIFICATION,
    recipients: validUserIds,
  };

  const notificationTemplateData = {
    title: `Meeting Notification: ${meeting.title}`,
    message: messageParts.join("\n"),
  };

  await sendPushNotifications(
    notificationLogData,
    notificationTemplate.NOTIFICATION(notificationTemplateData)
  );

  return apiResponse(
    CUSTOM_SUCCESS,
    "Notification sent successfully to invitees",
    {
      recipients: validUserIds.length,
      meetingTitle: meeting.title,
    },
    res
  );
});

const sendReminderToAgendaAssignees = apiHandler(async (req, res) => {
  const { meetingId, agendaId } = req.params;

  const meeting = await Meeting.findById(meetingId).lean();

  if (!meeting) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    meeting.miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Meeting Miqaat is not active", null, res);
  }

  const agenda = meeting.agendas.find((a) => a._id.toString() === agendaId);

  if (!agenda) {
    return apiError(NOT_FOUND, "Agenda", null, res);
  }

  // Get user IDs directly from assigneeMembers array
  let userIds = agenda.assigneeMembers
    ? agenda.assigneeMembers.map((id) => id.toString())
    : [];

  // Remove duplicates
  userIds = [...new Set(userIds)];

  if (userIds.length === 0) {
    return apiResponse(
      CUSTOM_SUCCESS,
      "No assignees found for this agenda",
      { recipients: 0 },
      res
    );
  }

  // Verify users exist in database
  const existingUsers = await KGUser.find({ _id: { $in: userIds } }).select(
    "_id"
  );
  const validUserIds = existingUsers.map((user) => user._id.toString());

  if (validUserIds.length === 0) {
    return apiResponse(
      CUSTOM_SUCCESS,
      "No valid users found to send reminder",
      { recipients: 0 },
      res
    );
  }

  let messageLines = [];
  messageLines.push(`Meeting Title: ${meeting.title}`);
  messageLines.push("\nAgenda Details:\n");

  if (agenda.agenda) {
    const plainAgenda = agenda.agenda.replace(/<[^>]*>/g, "").trim();
    messageLines.push(`\nAgenda:\n${plainAgenda}`);
  }

  if (agenda.proceedings) {
    const plainProceedings = agenda.proceedings.replace(/<[^>]*>/g, "").trim();
    messageLines.push(`\nProceedings:\n${plainProceedings}`);
  }

  if (agenda.actionable) {
    const plainActionable = agenda.actionable.replace(/<[^>]*>/g, "").trim();
    messageLines.push(`\nAction:\n${plainActionable}`);
  }

  const notificationLogData = {
    messageID: null,
    replyID: null,
    messageType: messageTypes.NOTIFICATION,
    recipients: validUserIds,
  };

  const notificationTemplateData = {
    title: `Reminder: ${meeting.title}`,
    message: messageLines.join("\n"),
  };

  await sendPushNotifications(
    notificationLogData,
    notificationTemplate.NOTIFICATION(notificationTemplateData)
  );

  return apiResponse(
    CUSTOM_SUCCESS,
    "Reminder sent successfully to agenda assignees",
    {
      recipients: validUserIds.length,
      meetingTitle: meeting.title,
      agendaTitle: agenda.agenda
        ? agenda.agenda.replace(/<[^>]*>/g, "").trim()
        : "No title",
    },
    res
  );
});

const getAssignees = apiHandler(async (req, res) => {
  const userID = req.user._id;
  const { assignee, systemRoles } = req.body;

  let allRecipients = [];
  let allUserIDs = [];

  if (assignee && assignee.length > 0) {
    await Promise.all(
      assignee.map(async (assigneeItem) => {
        if (assigneeItem.memberType !== "") {
          const members = await getMember(userID, assigneeItem);
          allRecipients.push(...members.existingUsersData);
          allUserIDs.push(...members.userIDs);
        }
        return assigneeItem;
      })
    );
  }

  if (systemRoles && systemRoles.length > 0) {
    const systemRoleMembers = await getMember(userID, {
      memberType: "SYSTEM_ROLE",
      systemRoles,
    });
    allRecipients.push(...systemRoleMembers.existingUsersData);
    allUserIDs.push(...systemRoleMembers.userIDs);
  }
  const uniqueRecipients = allRecipients.filter(
    (recipient, index, self) =>
      index ===
      self.findIndex((r) => r._id.toString() === recipient._id.toString())
  );

  if (!uniqueRecipients.length) {
    return apiError(NOT_FOUND, "Recipients", null, res ? res : null);
  }

  return apiResponse(FETCH, "Recipients", uniqueRecipients, res);
});

module.exports = {
  createMeeting,
  getMeetings,
  getMeeting,
  editMeeting,
  deleteMeeting,
  addMOM,
  sendNotificationToInvitees,
  sendReminderToAgendaAssignees,
  getAssignees,
  addEditAgenda,
  getAllMeetings,
};
