const { toObjectId } = require("../../../utils/misc.util");

const getITSUsersAggregation = (ITSIDs, compileListID = null) => {
  const pipeline = [
    {
      $match: {
        ITSID: { $in: ITSIDs },
      },
    },
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiatID",
      },
    },
    {
      $unwind: {
        path: "$jamiatID",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaatID",
      },
    },
    {
      $unwind: {
        path: "$jamaatID",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: "$miqaats",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "systemroles",
        localField: "miqaats.cityRoleID",
        foreignField: "_id",
        as: "miqaatCityRole",
      },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "miqaatKgType",
      },
    },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaats.kgGroupID",
        foreignField: "_id",
        as: "miqaatKgGroup",
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "miqaatHierarchyPosition",
      },
    },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "miqaatDepartment",
      },
    },
    {
      $lookup: {
        from: "functions",
        localField: "miqaats.functionID",
        foreignField: "_id",
        as: "miqaatFunction",
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "miqaatZone",
      },
    },
    {
      $lookup: {
        from: "departments",
        localField: "smeRecommendation.departmentID",
        foreignField: "_id",
        as: "smeDepartment",
      },
    },
    {
      $lookup: {
        from: "functions",
        localField: "smeRecommendation.functionID",
        foreignField: "_id",
        as: "smeFunction",
      },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "smeRecommendation.kgTypeID",
        foreignField: "_id",
        as: "smeKgType",
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "smeRecommendation.hierarchyPositionID",
        foreignField: "_id",
        as: "smeHierarchyPosition",
      },
    },
    {
      $addFields: {
        "miqaats.permissionName": {
          $arrayElemAt: ["$miqaatCityRole.name", 0],
        },
        "miqaats.kgTypeName": {
          $arrayElemAt: ["$miqaatKgType.name", 0],
        },
        "miqaats.kgGroupName": {
          $arrayElemAt: ["$miqaatKgGroup.name", 0],
        },
        "miqaats.hierarchyPositionAliasName": {
          $arrayElemAt: ["$miqaatHierarchyPosition.alias", 0],
        },
        "miqaats.hierarchyPositionName": {
          $arrayElemAt: ["$miqaatHierarchyPosition.name", 0],
        },
        "miqaats.departmentName": {
          $arrayElemAt: ["$miqaatDepartment.name", 0],
        },
        "miqaats.functionName": { $arrayElemAt: ["$miqaatFunction.name", 0] },
        "miqaats.zoneName": { $arrayElemAt: ["$miqaatZone.name", 0] },
        "smeRecommendation.departmentName": {
          $arrayElemAt: ["$smeDepartment.name", 0],
        },
        "smeRecommendation.functionName": {
          $arrayElemAt: ["$smeFunction.name", 0],
        },
        "smeRecommendation.kgTypeName": {
          $arrayElemAt: ["$smeKgType.name", 0],
        },
        "smeRecommendation.hierarchyPositionName": {
          $arrayElemAt: ["$smeHierarchyPosition.name", 0],
        },
        "smeRecommendation.arazCityZoneID": toObjectId("678cc22ca4f5fc5d18b3cb8a"),
        "smeRecommendation.zoneName": "CMZ",
      },
    },
    {
      $project: {
        miqaatHierarchyPosition: 0,
        miqaatDepartment: 0,
        miqaatFunction: 0,
        miqaatZone: 0,
        miqaatCityRole: 0,
        miqaatKgType: 0,
        miqaatKgGroup: 0,
        smeDepartment: 0,
        smeFunction: 0,
        smeKgType: 0,
        smeHierarchyPosition: 0,
      },
    },
    {
      $group: {
        _id: "$_id",
        doc: { $first: "$$ROOT" },
        miqaats: { $push: "$miqaats" },
      },
    },
    {
      $addFields: {
        "doc.miqaats": "$miqaats",
      },
    },
    {
      $replaceRoot: {
        newRoot: "$doc",
      },
    },
    {
      $lookup: {
        from: "kgusers",
        let: { recById: "$smeRecommendation.recommendedBy" },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ["$_id", "$$recById"] },
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
        as: "smeRecommendation.recommendedBy",
      },
    },
    {
      $unwind: {
        path: "$smeRecommendation.recommendedBy",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "interests",
        localField: "ITSID",
        foreignField: "ITSID",
        as: "interest",
      },
    },
    {
      $unwind: {
        path: "$interest",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        interestOne: "$interest.interestOne.departmentID",
        interestTwo: "$interest.interestTwo.departmentID",
        interestThree: "$interest.interestThree.departmentID",
      },
    },
  ];

  // Handle compileListID if provided
  if (compileListID) {
    pipeline.splice(2, 0, {
      $lookup: {
        from: "compilelists",
        let: { userId: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$_id", toObjectId(compileListID)] },
                  {
                    $gt: [
                      {
                        $size: {
                          $filter: {
                            input: "$departments",
                            as: "dept",
                            cond: {
                              $in: ["$$userId", "$$dept.kgUsers"],
                            },
                          },
                        },
                      },
                      0,
                    ],
                  },
                ],
              },
            },
          },
        ],
        as: "isInCompileList",
      },
    });
    pipeline.splice(3, 0, {
      $addFields: {
        isInCompileList: {
          $gt: [{ $size: "$isInCompileList" }, 0],
        },
      },
    });
  }

  return pipeline;
};

module.exports = {
  getITSUsersAggregation,
};
