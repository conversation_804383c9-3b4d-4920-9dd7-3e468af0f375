const express = require("express");
const { validate } = require("../../../middlewares/validation.middleware");
const {
  createRequisition,
  listRequisitions,
  getRequisitionDetails,
  updateRequisition,
  deleteRequisition,
  assignRequisition,
} = require("../controllers/kgRequistion.controller");
const {
  createRequisitionSchema,
  updateRequisitionSchema,
  getSingleRequisitionSchema,
  getListRequisitionSchema,
  deleteRequisitionSchema,
  assignRequisitionSchema,
} = require("../validations/kgRequisition.validation");

const router = express.Router();

router.post(
  "/add/requisitions",
  validate(createRequisitionSchema, "body"),
  createRequisition
);

router.patch(
  "/edit/requisitions/assign",
  validate(assignRequisitionSchema, "body"),
  assignRequisition
);

router.get("/get/requisitions",validate(getListRequisitionSchema, "query"), listRequisitions);

router.get(
  "/get/requisitions/:id",
  validate(getSingleRequisitionSchema, "params"),
  getRequisitionDetails
);
router.delete(
  "/delete/requisitions/:id",
  validate(deleteRequisitionSchema, "params"),
  deleteRequisition
);

router.put(
  "/edit/requisitions",
  validate(updateRequisitionSchema, "body"),
  updateRequisition
);

module.exports = router;

