const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");
const { getProfile, markAttendance } = require("../controllers/attendance.controller");
const { getProfileSchema, markAttendanceSchema } = require("../validations/attendance.validation");

router.post("/get/profile", validate(getProfileSchema, "body"), getProfile);
router.post("/add", validate(markAttendanceSchema, "body"), markAttendance);

module.exports = router;
