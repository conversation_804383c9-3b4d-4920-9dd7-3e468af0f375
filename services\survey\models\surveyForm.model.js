const { Schema, model } = require("mongoose");

const surveyRecipientMeta = {
  allTypes: [
    "ALL_IN_HIERARCHY",
    "ALL_IN_DEPARTMENT",
    "ALL_IN_ZONE",
    "ALL_DEPARTMENT_HOD",
    "ALL_ZONE_HOD",
    "BY_ITS",
    "CUSTOM",
    "PMO",
  ],
  departmentTypes: ["ALL_IN_DEPARTMENT", "ALL_DEPARTMENT_HOD"],
  zoneTypes: ["ALL_IN_ZONE", "ALL_ZONE_HOD"],
  ITSTypes: ["BY_ITS"],
};

const surveyFormFrequencyList = {
  ONCE: "once",
  DAILY: "daily",
};

const recipientsSchema = new Schema({
  miqaatID: {
    type: Schema.Types.ObjectId,
    ref: "Miqaat",
    required: false,
  },
  arazCityID: {
    type: Schema.Types.ObjectId,
    ref: "ArazCity",
    required: false,
  },
  recipientType: {
    type: String,
    enum: surveyRecipientMeta.allTypes,
    required: false,
  },
  departmentIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "Department",
    },
  ],
  functionIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "Function",
    },
  ],
  zoneIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
    },
  ],
  positionIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "HierarchyPosition",
    },
  ],
  kgGroupIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGGroup",
    },
  ],
  kgTypeIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGType",
    },
  ],
  ITSIDs: [
    {
      type: String,
    },
  ],
});

const SurveyFormSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    frequency: {
      type: String,
      enum: Object.values(surveyFormFrequencyList),
      required: true,
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
    },
    startTime: {
      type: String,
    },
    endTime: {
      type: String,
    },
    alwaysAvailable: {
      type: Boolean,
      default: false,
    },
    questions: [
      {
        type: Schema.Types.ObjectId,
        ref: "Question",
        required: true,
      },
    ],
    questionMapping: [
      {
        surveyID: {
          type: Schema.Types.ObjectId,
          ref: "SurveyForm",
          required: true,
        },
        questionID: {
          type: Schema.Types.ObjectId,
          ref: "Question",
          required: true,
        },
      },
    ],
    recipients: {
      criteria: [recipientsSchema],
      systemRoleIDs: [
        {
          type: Schema.Types.ObjectId,
          ref: "SystemRole",
        },
      ],
      systemUserIDs: [
        {
          type: Schema.Types.ObjectId,
          ref: "KGUser",
        },
      ],
    },

    reportRecipients: {
      criteria: [recipientsSchema],
      systemRoleIDs: [
        {
          type: Schema.Types.ObjectId,
          ref: "SystemRole",
        },
      ],
      systemUserIDs: [
        {
          type: Schema.Types.ObjectId,
          ref: "KGUser",
        },
      ],
    },
    department: [
      {
        type: Schema.Types.ObjectId,
        ref: "Department",
      },
    ],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
    },
  },
  {
    timestamps: true,
  }
);

const SurveyForm = model("SurveyForm", SurveyFormSchema);
module.exports = {
  SurveyForm,
  surveyFormFrequencyList,
  surveyRecipientMeta,
};
