const constants = require("../../../constants");
const {
  apiHand<PERSON>,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const {
  FETCH,
  CUSTOM_SUCCESS,
  UPDATE_SUCCESS,
  NOT_FOUND,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { toObjectId, isEmpty } = require("../../../utils/misc.util");
const { clearCacheByPattern, redisCacheKeys } = require("../../../utils/redis.cache");

const {
  ArazCity,
  Department,
  KgRequisitionByDepartment,
  KGUser,
  Interest,
} = require("../models");

const getArazCityZoneById = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.query;
  
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCity = await ArazCity.findOne({ miqaatID, _id: arazCityID })
    .populate("arazCityZones")
    .select("arazCityZones");

  const cmzZoneID = constants.ARAZ_CITY_ZONES.CMZ[0].toString();
  const loggedInUser = req.user;

  // Super admin returns all
  if (
    loggedInUser.systemRoleID &&
    loggedInUser.systemRoleID.toString() ===
      constants.SYSTEM_ROLES.SUPER_ADMIN[0].toString()
  ) {
    const allZones = arazCity.arazCityZones.sort((a, b) => {
      if (a._id.toString() === cmzZoneID) return -1;
      if (b._id.toString() === cmzZoneID) return 1;
      return a.name.localeCompare(b.name);
    });

    return apiResponse(FETCH, "Araz City Zones", allZones, res);
  }

  const userMiqaat = loggedInUser.miqaats.find(
    (miqaat) =>
      miqaat &&
      miqaat.miqaatID?.toString() === miqaatID?.toString() &&
      miqaat.arazCityID?.toString() === arazCityID?.toString() &&
      miqaat.isActive
  );

  let resultZones;

  if (userMiqaat?.arazCityZoneID) {
    resultZones = arazCity.arazCityZones.filter(
      (zone) => zone._id.toString() === userMiqaat.arazCityZoneID.toString()
    );
  } else {
    resultZones = arazCity.arazCityZones;
  }

  const sortedZones = resultZones.sort((a, b) => {
    if (a._id.toString() === cmzZoneID) return -1;
    if (b._id.toString() === cmzZoneID) return 1;
    return a.name.localeCompare(b.name);
  });

  return apiResponse(FETCH, "Araz City Zones", sortedZones, res);
});

const getKgRequisitionByDepartment = apiHandler(async (req, res) => {
  const { arazCityZoneID, miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  // Populate the departments.departmentID to get department details
  const arazCity = await ArazCity.findById(arazCityID).populate({
    path: "departments.departmentID",
    select: "name uniqueName LDName",
  });

  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }

  // Filter departments based on zone
  const departments = arazCity.departments.filter((dept) => {
    if (arazCityZoneID !== constants.ARAZ_CITY_ZONES.CMZ[0].toString()) {
      return dept.status === "active" && dept.isZonal === true;
    }
    return dept.status === "active";
  });

  const departmentIds = departments.map((dept) => dept.departmentID._id);

  // Get all requirements for these departments
  const requirements = await KgRequisitionByDepartment.find({
    departmentID: { $in: departmentIds },
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
    arazCityZoneID: toObjectId(arazCityZoneID),
  })
    .populate("departmentID", "name uniqueName LDName")
    .lean();

  const departmentWithRequirements = await Promise.all(
    departments.map(async (dept) => {
      const deptId = dept.departmentID._id.toString();
      const deptRequirements = requirements.filter(
        (req) => req.departmentID._id.toString() === deptId
      );

      // Count already assigned users
      const maleUserCount = await KGUser.countDocuments({
        gender: "M",
        miqaats: {
          $elemMatch: {
            isActive: true,
            miqaatID,
            arazCityID,
            arazCityZoneID,
            departmentID: deptId,
          },
        },
      });

      const femaleUserCount = await KGUser.countDocuments({
        gender: "F",
        miqaats: {
          $elemMatch: {
            isActive: true,
            miqaatID,
            arazCityID,
            arazCityZoneID,
            departmentID: deptId,
          },
        },
      });

      // Count users in KG Pool with this department as their first priority
      const maleUserCountInKGPool = await KGUser.aggregate([
        {
          $match: {
            "miqaats.miqaatID": toObjectId(miqaatID),
            "miqaats.arazCityID": toObjectId(arazCityID),
            $or: [
              { hierarchyPositionID: null },
              { hierarchyPositionID: { $exists: false } },
            ],
            gender: "M",
            active: true,
          },
        },
        {
          $lookup: {
            from: "interests",
            localField: "_id",
            foreignField: "userID",
            as: "interest",
          },
        },
        {
          $unwind: "$interest",
        },
        {
          $match: {
            "interest.interestOne.departmentID": deptId,
            "interest.arazCityZoneID": toObjectId(arazCityZoneID),
          },
        },
        {
          $count: "total",
        },
      ]);

      const femaleUserCountInKGPool = await KGUser.aggregate([
        {
          $match: {
            "miqaats.miqaatID": toObjectId(miqaatID),
            "miqaats.arazCityID": toObjectId(arazCityID),
            $or: [
              { hierarchyPositionID: null },
              { hierarchyPositionID: { $exists: false } },
            ],
            gender: "F",
            active: true,
          },
        },
        {
          $lookup: {
            from: "interests",
            localField: "_id",
            foreignField: "userID",
            as: "interest",
          },
        },
        {
          $unwind: "$interest",
        },
        {
          $match: {
            "interest.interestOne.departmentID": deptId,
            "interest.arazCityZoneID": toObjectId(arazCityZoneID),
          },
        },
        {
          $count: "total",
        },
      ]);

      const requirement = deptRequirements[0];

      const maleKGPoolCount = maleUserCountInKGPool[0]?.total || 0;
      const femaleKGPoolCount = femaleUserCountInKGPool[0]?.total || 0;

      const requiredMardoKgCount = requirement?.requiredMardoKgCount || 0;
      const requiredBairaoKgCount = requirement?.requiredBairaoKgCount || 0;

      return {
        departmentID: dept.departmentID._id,
        departmentName: dept.departmentID.name, // Now this will work correctly
        requiredMardoKgCount,
        requiredBairaoKgCount,
        alreadyAssignedMardo: maleUserCount || 0,
        alreadyAssignedBairao: femaleUserCount || 0,
        presentInKgPoolMardo: maleKGPoolCount,
        presentInKgPoolBairao: femaleKGPoolCount,
        remainingMardo: requiredMardoKgCount - (maleUserCount || 0),
        remainingBairao: requiredBairaoKgCount - (femaleUserCount || 0),
      };
    })
  );
  departmentWithRequirements.sort((a, b) =>
    a.departmentName.localeCompare(b.departmentName)
  );

  return apiResponse(
    FETCH,
    "Department-wise KG Requisitions",
    departmentWithRequirements,
    res
  );
});

const addKgRequisitionByDepartment = apiHandler(async (req, res) => {
  const { arazCityZoneID, miqaatID, arazCityID, departments } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  await clearCacheByPattern(`${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.REPORTS}:*`, miqaatID, arazCityID);

  const convertedMiqaatID = toObjectId(miqaatID);
  const convertedArazCityID = toObjectId(arazCityID);
  const convertedArazCityZoneID = toObjectId(arazCityZoneID);

  // Prepare bulk operations for efficiency
  const bulkOps = departments.map((dept) => {
    // Validate each department entry
    if (
      !dept.departmentID ||
      dept.requiredMardoKgCount === undefined ||
      dept.requiredBairaoKgCount === undefined
    ) {
      apiError(
        CUSTOM_ERROR,
        "Invalid department entry",
        {
          departmentID: dept.departmentID,
          requiredMardoKgCount: dept.requiredMardoKgCount,
          requiredBairaoKgCount: dept.requiredBairaoKgCount,
        },
        res
      );
    }
    
    return {
      updateOne: {
        filter: {
          departmentID: toObjectId(dept.departmentID),
          miqaatID: convertedMiqaatID,
          arazCityID: convertedArazCityID,
          arazCityZoneID: convertedArazCityZoneID,
        },
        update: {
          $set: {
            departmentID: toObjectId(dept.departmentID),
            miqaatID: convertedMiqaatID,
            arazCityID: convertedArazCityID,
            arazCityZoneID: convertedArazCityZoneID,
            requiredMardoKgCount: parseInt(dept.requiredMardoKgCount) || 0,
            requiredBairaoKgCount: parseInt(dept.requiredBairaoKgCount) || 0,
            updatedAt: new Date(),
          },
          $setOnInsert: {
            createdAt: new Date(),
          },
        },
        upsert: true,
      },
    };
  });

  // Execute bulk operations
  const result = await KgRequisitionByDepartment.bulkWrite(bulkOps);
  await clearCacheByPattern(`${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.REPORTS}:*`,miqaatID, arazCityID);
  
  return apiResponse(
    CUSTOM_SUCCESS,
    "KG Requisitions By Zone Updated Successfully",
    null,
    res
  );
});

const getKgRequisitionByZone = apiHandler(async (req, res) => {
  const { departmentID, miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  
  // Get the ArazCity to access its zones
  const arazCity = await ArazCity.findById(arazCityID).populate({
    path: "arazCityZones",
    select: "_id name",
  });
  
  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }
  
  const dept = await Department.findById(departmentID).lean();

  let zones = arazCity.arazCityZones || [];
  
  if (!dept.isZonal) {
    zones = zones.filter((zone) => zone._id.toString() === constants.ARAZ_CITY_ZONES.CMZ[0].toString());
  }
  
  const zoneIds = zones.map((zone) => zone._id || zone.zoneID);

  // Get all requirements for this department across all zones
  const requirements = await KgRequisitionByDepartment.find({
    departmentID: toObjectId(departmentID),
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
    arazCityZoneID: { $in: zoneIds.map((id) => toObjectId(id)) },
  }).lean();

  const zoneWithRequirements = await Promise.all(
    zones.map(async (zone) => {
      const zoneId = zone._id ? zone._id.toString() : zone.zoneID.toString();
      const zoneRequirement = requirements.find(
        (req) => req.arazCityZoneID.toString() === zoneId
      );

      // Count already assigned users for this zone and department
      const maleUserCount = await KGUser.countDocuments({
        gender: "M",
        miqaats: {
          $elemMatch: {
            isActive: true,
            miqaatID,
            arazCityID,
            arazCityZoneID: zoneId,
            departmentID: departmentID,
          },
        },
      });

      const femaleUserCount = await KGUser.countDocuments({
        gender: "F",
        miqaats: {
          $elemMatch: {
            isActive: true,
            miqaatID,
            arazCityID,
            arazCityZoneID: zoneId,
            departmentID: departmentID,
          },
        },
      });

      // Count users in KG Pool with this department as their first priority for this zone
      const maleUserCountInKGPool = await KGUser.aggregate([
        {
          $match: {
            "miqaats.miqaatID": toObjectId(miqaatID),
            "miqaats.arazCityID": toObjectId(arazCityID),
            $or: [
              { hierarchyPositionID: null },
              { hierarchyPositionID: { $exists: false } },
            ],
            gender: "M",
            active: true,
          },
        },
        {
          $lookup: {
            from: "interests",
            localField: "_id",
            foreignField: "userID",
            as: "interest",
          },
        },
        {
          $unwind: "$interest",
        },
        {
          $match: {
            "interest.interestOne.departmentID": departmentID,
            "interest.arazCityZoneID": toObjectId(zoneId),
          },
        },
        {
          $count: "total",
        },
      ]);

      const femaleUserCountInKGPool = await KGUser.aggregate([
        {
          $match: {
            "miqaats.miqaatID": toObjectId(miqaatID),
            "miqaats.arazCityID": toObjectId(arazCityID),
            $or: [
              { hierarchyPositionID: null },
              { hierarchyPositionID: { $exists: false } },
            ],
            gender: "F",
            active: true,
          },
        },
        {
          $lookup: {
            from: "interests",
            localField: "_id",
            foreignField: "userID",
            as: "interest",
          },
        },
        {
          $unwind: "$interest",
        },
        {
          $match: {
            "interest.interestOne.departmentID": departmentID,
            "interest.arazCityZoneID": toObjectId(zoneId),
          },
        },
        {
          $count: "total",
        },
      ]);

      const maleKGPoolCount = maleUserCountInKGPool[0]?.total || 0;
      const femaleKGPoolCount = femaleUserCountInKGPool[0]?.total || 0;

      const requiredMardoKgCount = zoneRequirement?.requiredMardoKgCount || 0;
      const requiredBairaoKgCount = zoneRequirement?.requiredBairaoKgCount || 0;

      return {
        arazCityZoneID: zoneId,
        arazCityZoneName: zone.name,
        requiredMardoKgCount,
        requiredBairaoKgCount,
        alreadyAssignedMardo: maleUserCount || 0,
        alreadyAssignedBairao: femaleUserCount || 0,
        presentInKgPoolMardo: maleKGPoolCount,
        presentInKgPoolBairao: femaleKGPoolCount,
        remainingMardo: requiredMardoKgCount - (maleUserCount || 0),
        remainingBairao: requiredBairaoKgCount - (femaleUserCount || 0),
      };
    })
  );

  // Sort zones by name
  zoneWithRequirements.sort((a, b) => a.arazCityZoneName.localeCompare(b.arazCityZoneName));

  const maleUserCount = await KGUser.countDocuments({
    gender: "M",
    miqaats: {
      $elemMatch: {
        isActive: true,
        miqaatID,
        arazCityID,
        arazCityZoneID: null,
        departmentID: departmentID,
      },
    },
  });

  const femaleUserCount = await KGUser.countDocuments({
    gender: "F",
    miqaats: {
      $elemMatch: {
        isActive: true,
        miqaatID,
        arazCityID,
        arazCityZoneID: null,
        departmentID: departmentID,
      },
    },
  });

  // Count users in KG Pool with this department as their first priority for this zone
  const maleUserCountInKGPool = await KGUser.aggregate([
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        $or: [
          { hierarchyPositionID: null },
          { hierarchyPositionID: { $exists: false } },
        ],
        gender: "M",
        active: true,
      },
    },
    {
      $lookup: {
        from: "interests",
        localField: "_id",
        foreignField: "userID",
        as: "interest",
      },
    },
    {
      $unwind: "$interest",
    },
    {
      $match: {
        "interest.interestOne.departmentID": departmentID,
        $or: [{ "interest.interestOne.zoneID": null }, { "interest.interestOne.zoneID": { $exists: false } }],
      },
    },
    {
      $count: "total",
    },
  ]);

  const femaleUserCountInKGPool = await KGUser.aggregate([
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        $or: [
          { hierarchyPositionID: null },
          { hierarchyPositionID: { $exists: false } },
        ],
        gender: "F",
        active: true,
      },
    },
    {
      $lookup: {
        from: "interests",
        localField: "_id",
        foreignField: "userID",
        as: "interest",
      },
    },
    {
      $unwind: "$interest",
    },
    {
      $match: {
        "interest.interestOne.departmentID": departmentID,
        $or: [{ "interest.interestOne.zoneID": null }, { "interest.interestOne.zoneID": { $exists: false } }],
      },
    },
    {
      $count: "total",
    },
  ]);

  const maleKGPoolCount = maleUserCountInKGPool[0]?.total || 0;
  const femaleKGPoolCount = femaleUserCountInKGPool[0]?.total || 0;

  const centralTeam = {
    arazCityZoneID: null,
    arazCityZoneName: "Central Team",
    requiredMardoKgCount: 0,
    requiredBairaoKgCount: 0,
    alreadyAssignedMardo: maleUserCount || 0,
    alreadyAssignedBairao: femaleUserCount || 0,
    presentInKgPoolMardo: maleKGPoolCount,
    presentInKgPoolBairao: femaleKGPoolCount,
    remainingMardo: 0,
    remainingBairao: 0,
  };

  zoneWithRequirements.push(centralTeam);

  return apiResponse(
    FETCH,
    "Zone-wise KG Requisitions",
    zoneWithRequirements,
    res
  );
});

const addKgRequisitionByZone = apiHandler(async (req, res) => {
  const { departmentID, miqaatID, arazCityID, zones } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const convertedMiqaatID = toObjectId(miqaatID);
  const convertedArazCityID = toObjectId(arazCityID);
  const convertedDepartmentID = toObjectId(departmentID);

  // Validate input
  if (!zones || !Array.isArray(zones) || zones.length === 0) {
    return apiError(
      CUSTOM_ERROR,
      "Invalid zones data",
      "Zones array is required and should not be empty",
      res
    );
  }

  // Prepare bulk operations for efficiency
  const bulkOps = zones.map((zone) => {
    // Validate each zone entry
    if (
      !zone.zoneID ||
      zone.requiredMardoKgCount === undefined ||
      zone.requiredBairaoKgCount === undefined
    ) {
      return apiError(
        CUSTOM_ERROR,
        "Invalid zone entry",
        {
          zoneID: zone.zoneID,
          requiredMardoKgCount: zone.requiredMardoKgCount,
          requiredBairaoKgCount: zone.requiredBairaoKgCount,
        },
        res
      );
    }

    return {
      updateOne: {
        filter: {
          departmentID: convertedDepartmentID,
          miqaatID: convertedMiqaatID,
          arazCityID: convertedArazCityID,
          arazCityZoneID: toObjectId(zone.zoneID),
        },
        update: {
          $set: {
            departmentID: convertedDepartmentID,
            miqaatID: convertedMiqaatID,
            arazCityID: convertedArazCityID,
            arazCityZoneID: toObjectId(zone.zoneID),
            requiredMardoKgCount: parseInt(zone.requiredMardoKgCount) || 0,
            requiredBairaoKgCount: parseInt(zone.requiredBairaoKgCount) || 0,
            updatedAt: new Date(),
          },
          $setOnInsert: {
            createdAt: new Date(),
          },
        },
        upsert: true,
      },
    };
  });

  const result = await KgRequisitionByDepartment.bulkWrite(bulkOps);
  await clearCacheByPattern(`${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.REPORTS}:*`,miqaatID, arazCityID);
    return apiResponse(
      CUSTOM_SUCCESS,
      "KG Requisitions By Department Updated Successfully",
      {
        upsertedCount: result.upsertedCount,
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount,
      },
      res
    );
  
});

const getArazCityDepartments = apiHandler(async (req, res) => {
  const { arazCityID } = req.query; 

  const arazCity = await ArazCity.findOne({ _id: arazCityID , status: true }).populate({
    path: "departments.departmentID",
    select: "_id name",
  });
  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }

  const departments = arazCity.departments.map((dept) => ({
    _id: dept.departmentID._id,
    name: dept.departmentID.name,
  }));

  return apiResponse(FETCH, "Araz City Departments", departments, res);
});

module.exports = {
  getArazCityZoneById,
  getKgRequisitionByDepartment,
  addKgRequisitionByDepartment,
  getKgRequisitionByZone,
  addKgRequisitionByZone,
  getArazCityDepartments
};
