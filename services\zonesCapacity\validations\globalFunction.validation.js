const Joi = require("joi");
const {
  idValidation,
  booleanValidation,
  numberValidation,
  stringValidation,
} = require("../../../utils/validator.util");

const addFunctionSchema = Joi.object({
  name: stringValidation,
  priority: numberValidation,
});

const getSingleFunctionSchema = Joi.object({
  id: idValidation,
});

const editFunctionSchema = Joi.object({
  id: idValidation,
  name: Joi.string().trim().messages({
    "string.empty": "Name cannot be empty",
  }),
  priority: numberValidation,
  status: booleanValidation,
});

const deleteFunctionSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addFunctionSchema,
  getSingleFunctionSchema,
  editFunctionSchema,
  deleteFunctionSchema,
};
