const {
  a<PERSON><PERSON><PERSON><PERSON>,
  api<PERSON><PERSON>r,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  CUSTOM_ERROR,
  CUSTOM_SUCCESS,
} = require("../../../utils/message.util");

const {
  ArazCity,
  ArazCityZone,
  KGUser,
  Department,
  ArazCityZoneFile,
} = require("../models");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
} = require("../../../utils/misc.util");
const { generateHierarchy } = require("./hierarchy.controller");
const { WaazVenue } = require("../../zonesCapacity/models");
const { ZoneMapping } = require("../../zonesCapacity/models/zoneMapping.model");
const { MawaidVenue } = require("../../zonesCapacity/models/mawaidVenue.model");
const constants = require("../../../constants");
const {
  generatePreSignedURLs,
  uploadFileToS3,
  generateGetPreSignedURL,
} = require("../../../utils/aws.util");
const {
  getCache,
  setCache,
  redisCacheKeys,
} = require("../../../utils/redis.cache");
const {
  addEventLog,
  EventActions,
  Modules,
} = require("../../../utils/eventLogs.util");
const {
  isActiveMiqaatAndArazCity,
} = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getAllArazCityZones = apiHandler(async (req, res) => {
  const arazCityZones = await ArazCityZone.aggregate([
    {
      $lookup: {
        from: "arazcities",
        localField: "arazCityID",
        foreignField: "_id",
        as: "arazCity",
      },
    },
    {
      $unwind: {
        path: "$arazCity",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiat",
      },
    },
    {
      $unwind: {
        path: "$jamiat",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaat",
      },
    },
    {
      $unwind: {
        path: "$jamaat",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        "arazCity._id": 1,
        "arazCity.name": 1,
        jamaat: {
          _id: { $arrayElemAt: ["$jamaat._id", 0] },
          name: { $arrayElemAt: ["$jamaat.name", 0] },
        },
        jamiat: {
          _id: { $arrayElemAt: ["$jamiat._id", 0] },
          name: { $arrayElemAt: ["$jamiat.name", 0] },
        },
      },
    },
    {
      $sort: {
        _id: -1,
      },
    },
  ]);

  return apiResponse(FETCH, "Araz City Zones", arazCityZones, res);
});

const addArazCityZone = apiHandler(async (req, res) => {
  const {
    name,
    LDName,
    alias,
    priority,
    ITSID,
    ITSName,
    arazCityID,
    jamiatID,
    jamaatID,
    latitude,
    longitude,
    radius,
    googleMapsLink,
  } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(null, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const uniqueName = getUniqueName(name);

  const existingArazCityZone = await ArazCityZone.findOne({
    uniqueName,
    arazCityID,
  });
  if (!isEmpty(existingArazCityZone)) {
    return apiError(
      CUSTOM_ERROR,
      "Araz City Zone with this name already exists",
      null,
      res
    );
  }

  const newArazCityZone = new ArazCityZone({
    name,
    LDName,
    uniqueName,
    alias,
    ITSID,
    priority,
    ITSName,
    createdBy: req.user._id,
    arazCityID: !isEmpty(arazCityID) ? toObjectId(arazCityID) : null,
    jamiatID: !isEmpty(jamiatID) ? toObjectId(jamiatID) : null,
    jamaatID: !isEmpty(jamaatID) ? toObjectId(jamaatID) : null,
    latitude,
    longitude,
    radius,
    googleMapsLink,
  });

  const arazCityZoneData = await newArazCityZone.save();

  apiResponse(ADD_SUCCESS, "Araz City Zone", null, res);

  const arazCityData = await ArazCity.findById(arazCityID)
    .select("departments")
    .populate({
      path: "departments",
      populate: [{ path: "departmentID" }],
    });

  if (!isEmpty(arazCityID)) {
    const arazCityData = await ArazCity.findByIdAndUpdate(
      toObjectId(arazCityID),
      {
        $addToSet: { arazCityZones: arazCityZoneData._id },
      },
      {
        new: true,
        lean: true,
      }
    );

    if (arazCityData?.miqaatID) {
      await addEventLog(
        EventActions.UPDATE,
        Modules.Hierarchy,
        "Hierarchy from Araz City Zone",
        constants.AMS_SYSTEMID
      );
      await generateHierarchy(arazCityData?.miqaatID, arazCityID);
    }
  }
});

const getSingleArazCityZone = apiHandler(async (req, res) => {
  const { id } = req.params;

  const arazCityZoneData = await ArazCityZone.aggregate([
    {
      $match: {
        _id: toObjectId(id),
      },
    },
    {
      $lookup: {
        from: "arazcities",
        localField: "arazCityID",
        foreignField: "_id",
        as: "arazCity",
        pipeline: [{ $match: { status: true } }],
      },
    },
    {
      $unwind: {
        path: "$arazCity",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        LDName: 1,
        alias: 1,
        ITSID: 1,
        ITSName: 1,
        priority: 1,
        "arazCity._id": 1,
        "arazCity.name": 1,
        jamiatID: 1,
        jamaatID: 1,
        latitude: 1,
        longitude: 1,
        googleMapsLink: 1,
      },
    },
  ]);

  if (isEmpty(arazCityZoneData)) {
    return apiError(NOT_FOUND, "Araz City Zone", null, res);
  }

  const zoneData = arazCityZoneData[0];

  const mawaidVenues = await MawaidVenue.find({ arazCityZoneID: id })
    .populate("mawaidVenueTypeID")
    .populate("mawaidVenueSuitabilityID")
    .lean();

  const waazVenues = await WaazVenue.find({ arazCityZoneID: id })
    .populate("waazVenueTypeID")
    .populate("waazVenueSuitabilityID")
    .populate("waazVenuePriorityID")
    .lean();
  const formattedMawaidVenues = mawaidVenues.map((venue) => ({
    _id: venue._id,
    status: venue.status,
    venueName: venue.name,
    venueType: venue.mawaidVenueTypeID?.name || "Unknown",
    venueSuitability: venue.mawaidVenueSuitabilityID?.name || "Unknown",
    noOfThok: venue.numberOfThoks || 0,
    thalPerThok: venue.plannedThoks || 0,
    capacity: venue.finalCapacity || venue.estimatedCapacity || 0,
  }));

  const formattedWaazVenues = waazVenues.map((venue) => ({
    _id: venue._id,
    status: venue.status,
    approvalStatus: venue.approvalStatus,
    venueName: venue.name,
    venueType: venue.waazVenueTypeID?.name || "Unknown",
    venueSuitability: venue.waazVenueSuitabilityID?.name || "Unknown",
    venuePriority: venue.waazVenuePriorityID?.name || "Unknown",
    capacity:
      venue.finalizedWaazSeatingCapacity || venue.waazSeatingCapacity || 0,
  }));

  const zoneMappings = await ZoneMapping.find({
    arazCityZoneID: toObjectId(id),
  }).lean();

  const muqimeenCount = zoneMappings.reduce(
    (sum, mapping) =>
      sum +
      (mapping?.gents || 0) +
      (mapping?.ladies || 0) +
      (mapping?.childrenDikrao || 0) +
      (mapping?.childrenDikrio || 0),
    0
  );

  const capacity = waazVenues.reduce(
    (sum, venue) =>
      sum +
      (venue.finalizedWaazSeatingCapacity || venue.waazSeatingCapacity || 0),
    0
  );

  const extendedCapacity = waazVenues.reduce(
    (sum, venue) => sum + (venue.extendedCapacity || 0),
    0
  );

  const mehmaanCount = capacity - muqimeenCount;
  const remainingCapacity = capacity - muqimeenCount;

  const arazCityZoneFile = await ArazCityZoneFile.findOne({
    arazCityZoneID: toObjectId(id),
    venueType: "ZONES",
    status: "APPROVED",
  })
    .sort({ createdAt: -1 })
    .limit(1);

  const mardoCount = zoneMappings.reduce(
    (sum, mapping) => sum + (mapping.gents || 0),
    0
  );
  const bairaoCount = zoneMappings.reduce(
    (sum, mapping) => sum + (mapping.ladies || 0),
    0
  );
  const gairBalighDikrao = zoneMappings.reduce(
    (sum, mapping) => sum + (mapping.childrenDikrao || 0),
    0
  );
  const gairBalighDikrio = zoneMappings.reduce(
    (sum, mapping) => sum + (mapping.childrenDikrio || 0),
    0
  );

  const completeZoneData = {
    ...zoneData,
    mawaidVenues: formattedMawaidVenues,
    waazVenues: formattedWaazVenues,
    stats: {
      existingCapacity: capacity,
      extendedCapacity,
      muqimeenCount,
      mehmaanCount,
      remainingCapacity,
      categoryBreakdown: {
        mardoCount,
        bairaoCount,
        gairBalighDikrao,
        gairBalighDikrio,
        total: muqimeenCount,
      },
    },
    isDrawing: !!arazCityZoneFile,
  };

  return apiResponse(FETCH, "Araz City Zone", completeZoneData, res);
});

const editArazCityZone = apiHandler(async (req, res) => {
  const {
    id,
    name,
    LDName,
    alias,
    priority,
    ITSID,
    ITSName,
    arazCityID,
    jamiatID,
    jamaatID,
    latitude,
    longitude,
    radius,
    googleMapsLink,
  } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(null, arazCityID,req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const uniqueName = getUniqueName(name);

  const existingArazCityZone = await ArazCityZone.findOne({
    uniqueName,
    _id: { $ne: id },
    arazCityID,
  });

  if (!isEmpty(existingArazCityZone)) {
    return apiError(
      CUSTOM_ERROR,
      "Araz City Zone with this name already exists",
      null,
      res
    );
  }

  const updateData = {
    name,
    LDName,
    uniqueName,
    alias,
    ITSID,
    priority,
    ITSName,
    updatedBy: req.user._id,
    arazCityID: !isEmpty(arazCityID) ? toObjectId(arazCityID) : null,
    jamiatID: !isEmpty(jamiatID) ? toObjectId(jamiatID) : null,
    jamaatID: !isEmpty(jamaatID) ? toObjectId(jamaatID) : null,
    latitude,
    longitude,
    radius,
    googleMapsLink,
  };

  const arazCityZoneData = await ArazCityZone.findByIdAndUpdate(
    id,
    updateData,
    {
      new: true,
      runValidators: true,
    }
  );

  if (isEmpty(arazCityZoneData)) {
    return apiError(NOT_FOUND, "Araz City Zone", null, res);
  }

  apiResponse(UPDATE_SUCCESS, "Araz City Zone", arazCityZoneData, res);

  if (!isEmpty(arazCityID)) {
    const arazCityData = await ArazCity.findOne({ _id: arazCityID }).lean();
    if (arazCityData?.miqaatID) {
      await addEventLog(
        EventActions.UPDATE,
        Modules.Hierarchy,
        "Hierarchy from Araz City Zone",
        constants.AMS_SYSTEMID
      );
      await generateHierarchy(arazCityData.miqaatID, arazCityID);
    }
  }
});

const deleteArazCityZone = apiHandler(async (req, res) => {
  const { id, arazCityID } = req.body;
  const checkActiveStatus = await isActiveMiqaatAndArazCity(null, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCityZoneData = await ArazCityZone.findOneAndDelete({ _id: id });

  if (isEmpty(arazCityZoneData)) {
    return apiError(NOT_FOUND, "Araz City Zone", null, res);
  }

  const arazCityData = await ArazCity.findByIdAndUpdate(
    toObjectId(arazCityID),
    {
      $pull: { arazCityZones: arazCityZoneData._id },
    },
    { new: true }
  ).lean();

  apiResponse(DELETE_SUCCESS, "Araz City Zone", null, res);

  if (arazCityData?.miqaatID) {
    await addEventLog(
      EventActions.UPDATE,
      Modules.Hierarchy,
      "Hierarchy from Araz City Zone",
      constants.AMS_SYSTEMID
    );

    await generateHierarchy(arazCityData.miqaatID, arazCityID);
  }
});

const getArazCityZoneByArazCity = apiHandler(async (req, res) => {
  const { id } = req.params;
  const checkActiveStatus = await isActiveMiqaatAndArazCity(null, id, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const zones = await ArazCityZone.aggregate([
    {
      $match: {
        $or: [{ arazCityID: toObjectId(id) }, { arazCityID: null }],
      },
    },
    {
      $lookup: {
        from: "arazcities",
        localField: "arazCityID",
        foreignField: "_id",
        as: "arazCityDetails",
      },
    },
    {
      $unwind: {
        path: "$arazCityDetails",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        uniqueName: 1,
        isSynchingZoneMapping: 1,
        lastSynchedDate: 1,
        arazCity: {
          _id: "$arazCityDetails._id",
          name: "$arazCityDetails.name",
          uniqueName: "$arazCityDetails.uniqueName",
        },
      },
    },
  ]);

  if (isEmpty(zones)) {
    return apiError(NOT_FOUND, "Araz City Zones", null, res);
  }

  const zonesWithCounts = await Promise.all(
    zones.map(async (zone) => {
      const waazVenues = await WaazVenue.find({
        arazCityZoneID: zone._id,
        status: "active",
        approvalStatus: "approved",
      }).lean();

      const zoneMappings = await ZoneMapping.find({
        arazCityZoneID: zone._id,
        arazCityID: toObjectId(id),
      }).lean();

      const muqimeenCount = zoneMappings.reduce(
        (sum, mapping) =>
          sum +
          mapping.gents +
          mapping.ladies +
          mapping.childrenDikrao +
          mapping.childrenDikrio,
        0
      );

      const capacity = waazVenues.reduce(
        (sum, venue) =>
          sum +
          (venue.finalizedWaazSeatingCapacity ||
            venue.waazSeatingCapacity ||
            0),
        0
      );

      const extendedCapacity = waazVenues.reduce(
        (sum, venue) => sum + (venue.extendedCapacity || 0),
        0
      );

      const mehmaanCount = capacity - muqimeenCount;

      const arazCityZoneFile = await ArazCityZoneFile.findOne({
        arazCityZoneID: zone._id,
        venueType: "ZONES",
        status: "APPROVED",
      })
        .sort({ createdAt: -1 })
        .limit(1);

      const isDrawing = !!arazCityZoneFile;

      return {
        ...zone,
        capacity,
        extendedCapacity,
        muqimeenCount,
        mehmaanCount,
        isDrawing,
      };
    })
  );

  return apiResponse(FETCH, "Araz City Zones", zonesWithCounts, res);
});

const sortUsersByPriorityAndName = (users) => {
  return users.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }

    return (a.name || "").localeCompare(b.name || "");
  });
};

const addUserToZoneHead = (user, zoneHeads, validKgTypes, allowPhone,allowFemalePhoto) => {
  const { foundMiqaat } = user;

  if (
    foundMiqaat?.hierarchyPosition?.id.toString() ===
      constants.HIERARCHY_POSITIONS.ZONE_HEAD[0].toString() &&
    validKgTypes.includes(foundMiqaat?.kgType?.uniqueName)
  ) {
    const userData = {
      name: user.name,
      LDName: user.LDName,
      logo: user.gender === "M" ? user.logo : allowFemalePhoto ? user.logo : undefined,
      ITSID: user?.ITSID,
      priority: foundMiqaat?.kgType?.priority,
      ...(user.gender === "M"
        ? { phone: user.phone, whatsapp: user.whatsapp }
        : allowPhone
        ? { phone: user.phone, whatsapp: user.whatsapp }
        : {}),
    };

    const newZoneHead = {
      position:
        foundMiqaat?.kgType.uniqueName === "local_kg"
          ? foundMiqaat.arazCity.showPositionAlias
            ? foundMiqaat?.hierarchyPosition.alias
            : foundMiqaat?.hierarchyPosition.name
          : foundMiqaat.kgType.name,
      users: [userData],
      priority: foundMiqaat?.kgType?.priority,
      bgColor: foundMiqaat?.kgType?.color,
    };

    const existingZoneHead = zoneHeads.find(
      (item) => item.position === newZoneHead.position
    );

    if (existingZoneHead) {
      existingZoneHead.users.push(userData);
    } else {
      zoneHeads.push(newZoneHead);
    }
  }
};

const addUserToDepartment = (user, departmentMap, validKgTypes, allowPhone,allowFemalePhoto) => {
  const { foundMiqaat } = user;

  if (
    foundMiqaat?.hierarchyPosition?.id.toString() ===
      constants.HIERARCHY_POSITIONS.ZONE_LEAD[0].toString() &&
    validKgTypes.includes(foundMiqaat?.kgType?.uniqueName)
  ) {
    const userData = {
      _id: user._id,
      name: user.name,
      LDName: user.LDName,
      logo: user.gender === "M" ? user.logo : allowFemalePhoto ? user.logo : undefined,
      ITSID: user.ITSID,
      priority: foundMiqaat?.kgType?.priority,
      position:
        foundMiqaat?.kgType.uniqueName === "local_kg"
          ? foundMiqaat.arazCity.showPositionAlias
            ? foundMiqaat?.hierarchyPosition.alias
            : foundMiqaat?.hierarchyPosition.name
          : foundMiqaat.kgType.name,
      ...(user.gender === "M"
        ? { phone: user.phone, whatsapp: user.whatsapp }
        : allowPhone
        ? { phone: user.phone, whatsapp: user.whatsapp }
        : {}),
    };

    const deptId = foundMiqaat.department.id.toString();
    if (departmentMap[deptId]) {
      departmentMap[deptId].users.push(userData);
    }
  }
};

const sortByPriorityAndName = (items, nameField) => {
  const sortedItems = items.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }

    return (a[nameField] || "").localeCompare(b[nameField] || "");
  });

  sortedItems.forEach((item) => {
    if (item.users && Array.isArray(item.users)) {
      item.users = sortUsersByPriorityAndName(item.users);
    }
  });

  return sortedItems;
};

const getArazCityZoneReport = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    arazCityZoneID,
    kgTypes = [constants.KG_TYPES.LOCAL_KG[1]],
  } = req.body;

  const validKgTypes = Array.isArray(kgTypes) ? kgTypes : [kgTypes];

  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${
    redisCacheKeys.ARAZ_CITY_ZONE_REPORT
  }:${miqaatID}:${arazCityID}:${arazCityZoneID}:${validKgTypes
    .sort()
    .join(":")}:${req.allowPhone}`;

  let data = await getCache(cacheKey, miqaatID, arazCityID);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Araz City Zone Report", data, res, true);
  }
  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let users = await KGUser.aggregate([
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: true } },
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.arazCityZoneID": toObjectId(arazCityZoneID),
        "miqaats.isActive": true,
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "arazCityZone",
      },
    },
    { $unwind: { path: "$arazCityZone", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "arazcities",
        localField: "miqaats.arazCityID",
        foreignField: "_id",
        as: "arazCity",
      },
    },
    { $unwind: { path: "$arazCity", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "department",
      },
    },
    { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "functions",
        localField: "miqaats.functionID",
        foreignField: "_id",
        as: "function",
      },
    },
    { $unwind: { path: "$function", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaats.miqaatID",
        foreignField: "_id",
        as: "miqaat",
      },
    },
    { $unwind: { path: "$miqaat", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $unwind: { path: "$hierarchyPosition", preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    { $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaats.kgGroupID",
        foreignField: "_id",
        as: "kgGroup",
      },
    },
    { $unwind: { path: "$kgGroup", preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        "miqaats.miqaat": {
          id: "$miqaat._id",
          name: "$miqaat.name",
        },
        "miqaats.arazCity": {
          id: "$arazCity._id",
          name: "$arazCity.name",
          showPositionAlias: "$arazCity.showPositionAlias",
        },
        "miqaats.arazCityZone": {
          id: "$arazCityZone._id",
          name: "$arazCityZone.name",
          priority: "$arazCityZone.priority",
        },
        "miqaats.department": {
          id: "$department._id",
          name: "$department.name",
          priority: "$department.priority",
        },
        "miqaats.function": {
          id: "$function._id",
          name: "$function.name",
          priority: "$function.priority",
        },
        "miqaats.kgType": {
          id: "$kgType._id",
          name: "$kgType.name",
          uniqueName: "$kgType.uniqueName",
          priority: "$kgType.priority",
          color: "$kgType.color",
        },
        "miqaats.kgGroup": {
          id: "$kgGroup._id",
          name: "$kgGroup.name",
        },
        "miqaats.hierarchyPosition": {
          id: "$hierarchyPosition._id",
          name: "$hierarchyPosition.name",
          alias: "$hierarchyPosition.alias",
          uniqueName: "$hierarchyPosition.uniqueName",
        },
      },
    },
    {
      $group: {
        _id: "$_id",
        name: { $first: "$name" },
        LDName: { $first: "$LDName" },
        ITSID: { $first: "$ITSID" },
        phone: { $first: "$phone" },
        whatsapp: { $first: "$whatsapp" },
        logo: { $first: "$logo" },
        gender: { $first: "$gender" },
        miqaats: { $push: "$miqaats" },
      },
    },
    {
      $addFields: {
        foundMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                as: "miqaat",
                cond: {
                  $and: [
                    { $eq: ["$$miqaat.miqaat.id", toObjectId(miqaatID)] },
                    { $eq: ["$$miqaat.arazCity.id", toObjectId(arazCityID)] },
                    {
                      $eq: [
                        "$$miqaat.arazCityZone.id",
                        toObjectId(arazCityZoneID),
                      ],
                    },
                    { $eq: ["$$miqaat.isActive", true] },
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        LDName: 1,
        logo: 1,
        phone: 1,
        whatsapp: 1,
        ITSID: 1,
        gender: 1,
        foundMiqaat: 1,
      },
    },
  ]);

  users.sort((a, b) => (a.name || "").localeCompare(b.name || ""));

  const arazCity = await ArazCity.findById(arazCityID)
    .populate("departments.departmentID")
    .lean();

  const departmentsData = arazCity.departments.filter(
    (dept) => dept.status === "active" && dept.departmentID
  );

  const departmentMap = {};
  departmentsData.forEach((dept) => {
    departmentMap[dept.departmentID._id.toString()] = {
      _id: dept.departmentID._id,
      name: dept.departmentID.name,
      LDName: dept.departmentID.LDName || "",
      priority: dept.departmentID.priority || 0,
      users: [],
    };
  });

  let zoneHeads = [];

  users.forEach((user) => {
    addUserToZoneHead(user, zoneHeads, validKgTypes, req.allowPhone, req.allowFemalePhoto);
    addUserToDepartment(user, departmentMap, validKgTypes, req.allowPhone, req.allowFemalePhoto);
  });

  const departments = Object.values(departmentMap);

  const response = {
    zoneHeads: sortByPriorityAndName(zoneHeads, "position"),
    departments: sortByPriorityAndName(departments, "name"),
  };

  apiResponse(FETCH, "Araz City Zone Report", response, res);

  await setCache(cacheKey, response,miqaatID,arazCityID);
});

const getZoneWisHODReport = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    kgTypes = [constants.KG_TYPES.LOCAL_KG[1]],
  } = req.body;

  
  const validKgTypes = Array.isArray(kgTypes) ? kgTypes : [kgTypes];
  
  const arazCity = await ArazCity.findById(arazCityID)
  .populate("arazCityZones")
  .lean();
  
  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City not found", null, res);
  }
  
  const cacheKey = `${redisCacheKeys.HIERARCHY}:${
    redisCacheKeys.ARAZ_CITY_ZONE_REPORT
  }:${miqaatID}:${arazCityID}:${kgTypes.sort().join(":")}`;

  let data = await getCache(cacheKey,miqaatID,arazCityID);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Araz City Zone Report", data, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const zoneIDs = arazCity.arazCityZones.map((zone) => zone._id);

  let hodsData = await KGUser.aggregate([
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: true } },
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.isActive": true,
        "miqaats.arazCityZoneID": { $in: zoneIDs },
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "arazCityZone",
      },
    },
    { $unwind: { path: "$arazCityZone", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "department",
      },
    },
    { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $unwind: { path: "$hierarchyPosition", preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    { $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true } },
    {
      $match: {
        "hierarchyPosition.uniqueName":
          constants.HIERARCHY_POSITIONS.ZONE_HEAD[1],
        "kgType.uniqueName": {
          $in: validKgTypes,
        },
      },
    },
    {
      $project: {
        name: 1,
        LDName: 1,
        ITSID: 1,
        phone: 1,
        whatsapp: 1,
        logo: 1,
        gender: 1,
        zoneName: "$arazCityZone.name",
        zoneID: "$arazCityZone._id",
        zonePriority: "$arazCityZone.priority",
        departmentName: "$department.name",
        departmentID: "$department._id",
        departmentPriority: "$department.priority",
        hierarchyPositionPriority: "$hierarchyPosition.priority",
        hierarchyPositionName: "$hierarchyPosition.name",
        hierarchyPositionNameAlias: "$hierarchyPosition.alias",
        position: "$kgType.name",
        positionColor: "$kgType.color",
        kgTypeID: "$kgType._id",
        kgTypeUniqueName: "$kgType.uniqueName",
        kgTypePriority: "$kgType.priority",
      },
    },
  ]);
  hodsData = hodsData.filter((hod) => {
    if (validKgTypes.includes(hod.kgTypeUniqueName)) {
      return hod;
    }
  });
  const zoneMap = new Map();
  arazCity.arazCityZones.forEach((zone) => {
    zoneMap.set(zone._id.toString(), {
      zoneID: zone._id,
      zoneName: zone.name,
      zonePriority: zone.priority || 0,
      hods: [],
    });
  });

  hodsData.forEach((hod) => {
    if (hod.zoneID) {
      const zoneId = hod.zoneID.toString();
      if (zoneMap.has(zoneId)) {
        const zoneData = zoneMap.get(zoneId);
        zoneData.hods.push({
          _id: hod._id,
          name: hod.name,
          LDName: hod.LDName,
          ITSID: hod.ITSID,
          ...(hod.gender === "M"
            ? { phone: hod.phone, whatsapp: hod.whatsapp }
            : req.allowPhone
            ? { phone: hod.phone, whatsapp: hod.whatsapp }
            : {}),
          logo: hod.logo,
          departmentName: hod.departmentName,
          departmentID: hod.departmentID,
          position:
            hod.kgTypeUniqueName === "local_kg"
              ? arazCity.showPositionAlias
                ? hod.hierarchyPositionNameAlias
                : hod.hierarchyPositionName
              : hod.position,
          positionColor: hod.positionColor,
          priority: hod.kgTypePriority,
        });
      }
    }
  });

  zoneMap.forEach((zoneData) => {
    zoneData.hods.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      return a.name.localeCompare(b.name);
    });
  });

  const result = Array.from(zoneMap.values()).sort(
    (a, b) =>
      a.zonePriority - b.zonePriority || a.zoneName.localeCompare(b.zoneName)
  );

  apiResponse(FETCH, "Zone-wise HOD Report", result, res);

  await setCache(cacheKey, result,miqaatID,arazCityID);
});

const statusTypes = {
  DRAFT: "DRAFT",
  APPROVED: "APPROVED",
  UNLOCKED: "UNLOCKED",
};

const venueTypes = {
  ZONES: "ZONES",
  WAAZ_VENUE: "WAAZ_VENUE",
};

const handleS3Upload = async (file, arazCityZoneID) => {
  const fileDetails = {
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  };

  const preSignedURLs = await generatePreSignedURLs(
    "araz_city_zone_files",
    arazCityZoneID,
    [fileDetails]
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return null;
  }

  const { fileKey, preSignedURL } = preSignedURLs[0];
  const uploadResult = await uploadFileToS3(file, preSignedURL, fileKey);

  if (!uploadResult) {
    return null;
  }

  return { fileKey, fileDetails };
};

const buildFileQuery = (arazCityZoneID, miqaatID, arazCityID) => {
  return {
    arazCityZoneID,
    venueType: venueTypes.ZONES,
    miqaatID,
    arazCityID,
  };
};

const validateZoneExists = async (arazCityZoneID) => {
  if (!arazCityZoneID) {
    return false;
  }

  const arazCityData = await ArazCityZone.findById(arazCityZoneID);
  return !!arazCityData;
};

const createFileDocument = (data, userId) => {
  return {
    arazCityZoneID: data.arazCityZoneID,
    miqaatID: data.miqaatID,
    arazCityID: data.arazCityID,
    fileData: {
      fileName: data.fileDetails.fileName,
      fileType: data.fileDetails.fileType,
      fileSize: data.fileDetails.fileSize,
      fileKey: data.fileKey,
    },
    venueType: venueTypes.ZONES,
    userID: userId,
    status: data.status || statusTypes.DRAFT,
  };
};

const getRecentFile = async (query) => {
  return await ArazCityZoneFile.findOne({
    ...query,
  }).sort({ createdAt: -1 });
};

const uploadFile = apiHandler(async (req, res) => {
  try {
    if (!req.file) {
      return apiError(CUSTOM_ERROR, "No file uploaded", null, res);
    }

    const { arazCityZoneID, miqaatID, arazCityID } = req.body;
    const checkActiveStatus = await isActiveMiqaatAndArazCity(
      miqaatID,
      arazCityID,
      req
    );
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }
    if (!(await validateZoneExists(arazCityZoneID))) {
      return apiResponse(CUSTOM_ERROR, "Invalid arazCityZoneID", {}, res);
    }

    const query = buildFileQuery(arazCityZoneID, miqaatID, arazCityID);
    const recentFile = await getRecentFile(query);

    if (recentFile?.status === statusTypes.APPROVED) {
      return apiResponse(
        CUSTOM_ERROR,
        "An approved file already exists. Please unlock it before uploading a new file.",
        { fileId: recentFile._id },
        res
      );
    }

    const uploadResult = await handleS3Upload(req.file, arazCityZoneID);
    if (!uploadResult) {
      return apiResponse(CUSTOM_ERROR, "Failed to upload file", {}, res);
    }

    const { fileKey, fileDetails } = uploadResult;

    let fileDocument;
    if (recentFile?.status === statusTypes.DRAFT) {
      recentFile.fileData = {
        fileName: fileDetails.fileName,
        fileType: fileDetails.fileType,
        fileSize: fileDetails.fileSize,
        fileKey: fileKey,
      };
      recentFile.userID = req.user._id;

      fileDocument = await recentFile.save();
    } else {
      const newFileData = createFileDocument(
        {
          arazCityZoneID,
          fileDetails,
          fileKey,
          miqaatID,
          arazCityID,
        },
        req.user._id
      );

      fileDocument = await ArazCityZoneFile.create(newFileData);
    }

    return apiResponse(
      CUSTOM_SUCCESS,
      "File Uploaded Successfully",
      fileDocument,
      res
    );
  } catch (error) {
    console.error("Error uploading file:", error);
    return apiResponse(CUSTOM_ERROR, "Failed to process file upload", {}, res);
  }
});

const updateFileStatus = apiHandler(async (req, res) => {
  try {
    const { fileId, status } = req.body;

    if (!fileId || !status) {
      return apiResponse(
        CUSTOM_ERROR,
        "FileId and status are required",
        {},
        res
      );
    }

    if (!Object.values(statusTypes).includes(status)) {
      return apiResponse(CUSTOM_ERROR, "Invalid status value", {}, res);
    }

    const existingFile = await ArazCityZoneFile.findById(fileId);
    if (!existingFile) {
      return apiResponse(CUSTOM_ERROR, "File not found", {}, res);
    }

    if (existingFile.venueType !== venueTypes.ZONES) {
      return apiResponse(
        CUSTOM_ERROR,
        "Only ZONES type files can be handled",
        {},
        res
      );
    }

    if (existingFile.status === status) {
      return apiResponse(
        CUSTOM_ERROR,
        `File is already in ${status} status`,
        {},
        res
      );
    }

    if (
      status === statusTypes.UNLOCKED &&
      existingFile.status !== statusTypes.APPROVED
    ) {
      return apiResponse(
        CUSTOM_ERROR,
        "Only files with 'approved' status can be changed to 'unlocked'",
        {},
        res
      );
    }

    if (status === statusTypes.APPROVED) {
      const query = {
        arazCityZoneID: existingFile.arazCityZoneID,
        functionID: existingFile.functionID,
        venueID: existingFile.venueID,
        venueType: venueTypes.WAAZ_VENUE,
        miqaatID: existingFile.miqaatID,
        arazCityID: existingFile.arazCityID,
      };

      const recentFile = await ArazCityZoneFile.findOne({
        ...query,
        _id: { $ne: fileId },
      }).sort({ createdAt: -1 });

      if (recentFile && recentFile.status === statusTypes.APPROVED) {
        return apiResponse(
          CUSTOM_ERROR,
          "An approved file already exists. Please unlock it first.",
          { fileId: recentFile._id },
          res
        );
      }
    }

    const newFileData = createFileDocument(
      {
        arazCityZoneID: existingFile.arazCityZoneID,
        miqaatID: existingFile.miqaatID,
        arazCityID: existingFile.arazCityID,
        fileDetails: {
          fileName: existingFile.fileData.fileName,
          fileType: existingFile.fileData.fileType,
          fileSize: existingFile.fileData.fileSize,
        },
        fileKey: existingFile.fileData.fileKey,
        status: status,
      },
      req.user._id
    );

    const newFileDocument = await ArazCityZoneFile.create(newFileData);

    return apiResponse(
      CUSTOM_SUCCESS,
      `File status updated to ${status}`,
      { fileDocument: newFileDocument },
      res
    );
  } catch (error) {
    console.error("Error updating file status:", error);
    return apiResponse(CUSTOM_ERROR, "Failed to update file status", {}, res);
  }
});

const getFiles = apiHandler(async (req, res) => {
  try {
    const { arazCityZoneID, miqaatID, arazCityID } = req.body;
    const checkActiveStatus = await isActiveMiqaatAndArazCity(
      miqaatID,
      arazCityID,
      req
    );
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    const files = await ArazCityZoneFile.find({
      arazCityZoneID,
      miqaatID,
      arazCityID,
      venueType: venueTypes.ZONES,
    })
      .sort({ createdAt: 1 })
      .populate("userID", "name email");

    const fileGroups = {};
    files.forEach((file) => {
      if (!fileGroups[file.fileData.fileKey]) {
        fileGroups[file.fileData.fileKey] = [];
      }
      fileGroups[file.fileData.fileKey].push(file);
    });

    return apiResponse(
      CUSTOM_SUCCESS,
      "Files retrieved successfully",
      { files },
      res
    );
  } catch (error) {
    console.error("Error retrieving files:", error);
    return apiResponse(CUSTOM_ERROR, "Failed to retrieve files", {}, res);
  }
});

const getMasterFiles = apiHandler(async (req, res) => {
  try {
    const { arazCityZoneID, miqaatID, arazCityID } = req.body;

    const checkActiveStatus = await isActiveMiqaatAndArazCity(
      miqaatID,
      arazCityID,
      req
    );
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    const query = {};
    query.status = statusTypes.APPROVED;
    query.arazCityZoneID = arazCityZoneID;
    query.miqaatID = miqaatID;
    query.arazCityID = arazCityID;
    query.venueType = venueTypes.ZONES;

    const masterFiles = await ArazCityZoneFile.findOne(query)
      .sort({ updatedAt: -1 })
      .populate("userID", "name email");
    if (isEmpty(masterFiles)) {
      return apiError(NOT_FOUND, "Master file", null, res);
    }

    return apiResponse(
      CUSTOM_SUCCESS,
      "Master files retrieved successfully",
      { masterFiles },
      res
    );
  } catch (error) {
    console.error("Error retrieving master files:", error);
    return apiError(CUSTOM_ERROR, "Failed to retrieve master files", {}, res);
  }
});

const getDownloadURL = apiHandler(async (req, res) => {
  const { fileKey } = req.body;
  const preSignedURL = await generateGetPreSignedURL(fileKey);
  return apiResponse(FETCH, "Download URL", { preSignedURL }, res);
});

const deleteArazCityFile = apiHandler(async (req, res) => {
  const { id } = req.params;
  const currentUserId = req.user._id;

  const file = await ArazCityZoneFile.findById(id);

  if (!file) {
    return apiError(NOT_FOUND, "File", null, res);
  }

  if (file.userID.toString() !== currentUserId.toString()) {
    return apiResponse(
      CUSTOM_ERROR,
      "You can only delete files that you have uploaded",
      null,
      res
    );
  }

  const fileData = await ArazCityZoneFile.findByIdAndDelete(id);

  return apiResponse(DELETE_SUCCESS, "File", null, res);
});

module.exports = {
  getAllArazCityZones,
  addArazCityZone,
  getSingleArazCityZone,
  editArazCityZone,
  deleteArazCityZone,
  getArazCityZoneByArazCity,
  getArazCityZoneReport,
  getZoneWisHODReport,
  uploadFile,
  updateFileStatus,
  getMasterFiles,
  getDownloadURL,
  deleteArazCityFile,
  getFiles,
};
