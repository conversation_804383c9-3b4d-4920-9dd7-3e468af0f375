const {
  getAllGuides,
  addEditGuide,
  getSingleGuide,
  deleteGuide,
  uploadFiles,
  addUser<PERSON>lick,
  getUserClickReport,
  getDownloadURL,
} = require("../controllers/guide.controller");
const {
  addGuideSchema,
  getSingleGuideSchema,
  editGuideSchema,
  deleteGuideSchema,
  addUserClickSchema,
  getUserClickReportSchema,
  getDownloadURLSchema,
} = require("../validations/guide.validation");
const { validate } = require("../../../middlewares/validation.middleware");
const { upload, uploadV2 } = require("../../../middlewares/multer.middleware");

const router = require("express").Router();

router.get("/get/all", getAllGuides);

router.post(
  "/add/upload-file",
  upload("ashara_guide").array("files"),
  uploadFiles
);

router.post("/add", validate(addGuideSchema, "body"), addEditGuide);

router.get(
  "/get/:id",
  validate(getSingleGuideSchema, "params"),
  getSingleGuide
);

router.put("/edit", validate(editGuideSchema, "body"), addEditGuide);

router.delete(
  "/delete/:id",
  validate(deleteGuideSchema, "params"),
  deleteGuide
);

router.patch("/add/click", validate(addUserClickSchema, "body"), addUserClick);

router.post(
  "/get/user-click-report/:guideID",
  validate(getUserClickReportSchema, "params"),
  getUserClickReport
);

router.post(
  "/get/download-url",
  validate(getDownloadURLSchema, "body"),
  getDownloadURL
);

module.exports = router;
