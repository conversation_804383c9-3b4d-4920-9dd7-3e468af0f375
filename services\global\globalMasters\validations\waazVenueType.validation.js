const Joi = require("joi");
const {
  stringValidation,
  idValidation,
} = require("../../../utils/validator.util");

const addWaazVenueTypeSchema = Joi.object({
  name: stringValidation,
});

const getSingleWaazVenueTypeSchema = Joi.object({
  id: idValidation,
});

const editWaazVenueTypeSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
});

const deleteWaazVenueTypeSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addWaazVenueTypeSchema,
  getSingleWaazVenueTypeSchema,
  editWaazVenueTypeSchema,
  deleteWaazVenueTypeSchema,
};
