const express = require("express");
const { validate } = require("../../../middlewares/validation.middleware");
const router = express.Router();
const { getArazCityDepartments, getKgRequisitionByZone, addKgRequisitionByZone } = require("../controllers/kgRequisitionDepartment.controller");
const { getArazCityZoneByIdSchema, getKgRequisitionByZoneSchema } = require("../validations/kgRequisitionByDepartment.validation");

router.get("/get/araz-city-departments:arazCityID",validate(getArazCityZoneByIdSchema, "query"), getArazCityDepartments);
router.post("/get/kg-requisition-by-zone",validate(getKgRequisitionByZoneSchema, "body"), getKgRequisitionByZone);
router.put("/add/kg-requisition-by-zone", addKgRequisitionByZone);

module.exports = router;
