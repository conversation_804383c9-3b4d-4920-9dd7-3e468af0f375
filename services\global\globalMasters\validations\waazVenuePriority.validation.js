const Joi = require("joi");
const {
  stringValidation,
  idValidation,
} = require("../../../utils/validator.util");

const addWaazVenuePrioritySchema = Joi.object({
  name: stringValidation,
});

const getSingleWaazVenuePrioritySchema = Joi.object({
  id: idValidation,
});

const editWaazVenuePrioritySchema = Joi.object({
  id: idValidation,
  name: stringValidation,
});

const deleteWaazVenuePrioritySchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addWaazVenuePrioritySchema,
  getSingleWaazVenuePrioritySchema,
  editWaazVenuePrioritySchema,
  deleteWaazVenuePrioritySchema,
};
