const { ref } = require("joi");
const { Schema, model } = require("mongoose");

const zoneMapping = new Schema(
  {
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: true,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: true,
    },
    HOFITSID: {
      type: String,
      required: false,
      trim: true,
    },
    HOFName: {
      type: String,
      required: false,
      trim: true,
    },
    jamiatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamiat",
      required: false,
      trim: true,
    },
    jamaatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamaat",
      required: false,
      trim: true,
    },
    sector: {
      type: String,
      required: false,
      trim: true,
    },
    subSector: {
      type: String,
      required: false,
      trim: true,
    },
    ladies: {
      type: Number,
      required: true,
      default: 0,
    },
    gents: {
      type: Number,
      required: true,
      default: 0,
    },
    childrenDikrao: {
      type: Number,
      required: true,
      default: 0,
    },
    childrenDikrio: {
      type: Number,
      required: true,
      default: 0,
    },
    addedByHof: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

const ZoneMapping = model("zoneMapping", zoneMapping);

module.exports = { ZoneMapping };
