const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
  DELETE_ERROR,
} = require("../../../utils/message.util");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
} = require("../../../utils/misc.util");
const { WaazVenuePriority, WaazVenue } = require("../../zonesCapacity/models");

const getAllWaazVenuePriorities = apiHandler(async (req, res) => {
  const waazVenuePriorities = await WaazVenuePriority.find().sort({
    _id: -1,
  });
  return apiResponse(FETCH, "Waaz Venue Priorities", waazVenuePriorities, res);
});

const addEditWaazVenuePriority = apiHandler(async (req, res) => {
  const { id } = req.body;
  let data = req.body;

  data.uniqueName = getUniqueName(data.name);

  const uniqueNameQueryObject = { uniqueName: data.uniqueName };

  if (id) uniqueNameQueryObject._id = { $ne: id };

  const existingWaazVenuePriority = await WaazVenuePriority.findOne(
    uniqueNameQueryObject
  );

  if (existingWaazVenuePriority) {
    return apiError(
      CUSTOM_ERROR,
      "Waaz Venue Priority with this name already exists",
      null,
      res
    );
  }

  if (!id) {
    data.createdBy = req.user._id;

    const newWaazVenuePriority = new WaazVenuePriority(data);

    let savedWaazVenuePriorityData = await newWaazVenuePriority.save();

    return apiResponse(
      ADD_SUCCESS,
      "Waaz Venue Priority",
      savedWaazVenuePriorityData,
      res
    );
  } else {
    data.updatedBy = req.user._id;

    let updatedWaazVenuePriorityData =
      await WaazVenuePriority.findByIdAndUpdate(id, data, {
        new: true,
        runValidators: true,
      });

    if (!updatedWaazVenuePriorityData) {
      return apiError(NOT_FOUND, "Waaz Venue Priority", null, res);
    }

    return apiResponse(
      UPDATE_SUCCESS,
      "Waaz Venue Priority",
      updatedWaazVenuePriorityData,
      res
    );
  }
});

const getSingleWaazVenuePriority = apiHandler(async (req, res) => {
  const { id } = req.params;
  const waazVenuePriorityData = await WaazVenuePriority.findById(id);

  if (!waazVenuePriorityData)
    return apiError(NOT_FOUND, "Waaz Venue Priority", null, res);

  return apiResponse(FETCH, "Waaz Venue Priority", waazVenuePriorityData, res);
});

const deleteWaazVenuePriority = apiHandler(async (req, res) => {
  const { id } = req.params;

  const isUsedInSeating = await WaazVenue.findOne({
    waazVenuePriorityID: toObjectId(id),
  });
  if (isUsedInSeating) {
    return apiError(DELETE_ERROR, "Waaz Venue Priority is used in Waaz Seating", null, res);
  }

  const waazVenuePriorityData = await WaazVenuePriority.findOneAndDelete({
    _id: id,
  });

  if (!waazVenuePriorityData)
    return apiError(NOT_FOUND, "Waaz Venue Priority", null, res);

  return apiResponse(DELETE_SUCCESS, "Waaz Venue Priority", null, res);
});

module.exports = {
  getAllWaazVenuePriorities,
  addEditWaazVenuePriority,
  getSingleWaazVenuePriority,
  deleteWaazVenuePriority,
};
