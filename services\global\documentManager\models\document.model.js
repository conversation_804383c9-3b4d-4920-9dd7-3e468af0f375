const { Schema, model } = require("mongoose");


const attachmentSchema = new Schema(
  {
    fileName: {
      type: String,
      required: false,
    },
    fileType: {
      type: String,
      required: false,
    },
    fileSize: {
      type: Number,
      required: false,
    },
    fileKey: {
      type: String,
      required: false,
    },
    uploadDate: {
      type: Date,
      required: false,
    }
  },
  { timestamps: true, required: false }
);

const documentSchema = new Schema(
  {
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    documentTitle: {
      type: String,
      required: true,
      trim: true,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityIDs: [
      {
        type: Schema.Types.ObjectId,
        ref: "ArazCity",
        required: true,
      },
    ],
    departments: [
      {
        type: Schema.Types.ObjectId,
        ref: "Department",
        required: true,
      },
    ],
    positions: [
      {
        type: Schema.Types.ObjectId,
        ref: "HierarchyPosition",
        required: true,
      },
    ],
    systemRoleIDs: [
      {
        type: Schema.Types.ObjectId,
        ref: "SystemRole",
        required: true,
      },
    ],
    documentTypeID: {
      type: Schema.Types.ObjectId,
      ref: "DocumentType",
      required: true,
    },
    attachments: [attachmentSchema],
  },
  { timestamps: true }
);

const Document = model("Document", documentSchema);

module.exports = { Document };
