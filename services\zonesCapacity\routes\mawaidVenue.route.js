const {
  getAllMawaidVenues,
  addEditMawaidVenue,
  getSingleMawaidVenue,
  deleteMawaidVenue,
  activateMawaidVenue,
  finalCapacityMawaidVenue,
  deleteMawaidVenueFile,
  getMawaidVenueFileDownloadURL,
  getMawaidVenueMasterFiles,
  updateMawaidVenueFileStatus,
  uploadMawaidVenueFile,
  getMawaidVenueFiles,
} = require("../controllers/mawaidVenue.controller");
const {
  addMawaidVenueSchema,
  getSingleMawaidVenueSchema,
  editMawaidVenueSchema,
  deleteMawaidVenueSchema,
  getAllMawaidVenuesSchema,
  activateMawaidVenueSchema,
  finalCapacityMawaidVenueSchema,
  uploadFileSchema,
  deleteArazCityZoneMawaidVenueFileSchema,
  getFilesSchema,
  updateFileStatusSchema,
  getMasterFilesSchema,
  getDownloadURLSchema,
} = require("../validations/mawaidVenue.validation");
const { validate } = require("../../../middlewares/validation.middleware");
const { upload } = require("../../../middlewares/multer.middleware");

const router = require("express").Router();

router.post(
  "/get",
  validate(getAllMawaidVenuesSchema, "body"),
  getAllMawaidVenues
);

router.post("/add", validate(addMawaidVenueSchema, "body"), addEditMawaidVenue);

router.get(
  "/get/:id",
  validate(getSingleMawaidVenueSchema, "params"),
  getSingleMawaidVenue
);

router.put(
  "/edit",
  validate(editMawaidVenueSchema, "body"),
  addEditMawaidVenue
);

router.delete(
  "/delete/:id",
  validate(deleteMawaidVenueSchema, "params"),
  deleteMawaidVenue
);

router.patch(
  "/activate-mawaid-venue",
  validate(activateMawaidVenueSchema, "body"),
  activateMawaidVenue
);
router.patch(
  "/final-capacity",
  validate(finalCapacityMawaidVenueSchema, "body"),
  finalCapacityMawaidVenue
);

const fileUpload = upload("araz_city_mawaid_venue_files");

router.post(
  "/upload/:function",
  fileUpload.single("file"),
  validate(uploadFileSchema, "body"),
  uploadMawaidVenueFile
);

router.put(
  "/approve/:fucntion",
  validate(updateFileStatusSchema, "body"),
  updateMawaidVenueFileStatus
);

router.post(
  "/upload/:function/get",
  validate(getFilesSchema, "body"),
  getMawaidVenueFiles
);

router.post(
  "/upload/file/get-master-files",
  validate(getMasterFilesSchema, "body"),
  getMawaidVenueMasterFiles
);

router.post(
  "/get/download-url",
  validate(getDownloadURLSchema, "body"),
  getMawaidVenueFileDownloadURL
);

router.delete(
  "/upload/:function/delete/:id",
  validate(deleteArazCityZoneMawaidVenueFileSchema, "params"),
  deleteMawaidVenueFile
);

module.exports = router;
