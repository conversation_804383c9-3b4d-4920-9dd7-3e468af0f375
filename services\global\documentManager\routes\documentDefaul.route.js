const express = require('express');
const { getDownloadURLSchema, getSingleDocumentSchema, getAllDocumentSchema } = require('../validations/document.validation');
const { getDocumentType, getDocumentURL, getSingleDocument, getDocuments, fetchAllDocuments } = require('../controllers/document.controller');
const { validate } = require('../../../../middlewares/validation.middleware');
const router = express.Router();

router.post("/get",validate(getAllDocumentSchema, "body"), getDocuments);
router.get("/get/document-type", getDocumentType);
router.post("/get/document-url", validate(getDownloadURLSchema, "body"), getDocumentURL);
router.get("/get/:id", validate(getSingleDocumentSchema, "params"), getSingleDocument);
router.post("/get/all-documents", validate(getAllDocumentSchema, "body"), fetchAllDocuments);

module.exports = router;
