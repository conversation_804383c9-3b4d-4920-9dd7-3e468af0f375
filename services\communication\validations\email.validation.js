const Joi = require("joi");
const {
  idValidation,
  numberValidation,
} = require("../../../utils/validator.util");

const getEmailsSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  page: numberValidation.optional(),
  limit: numberValidation.optional(),
});

const getEmailSchema = Joi.object({
  id: idValidation,
});

const getEmailRecipientsSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  getEmailsSchema,
  getEmailSchema,
  getEmailRecipientsSchema,
};
