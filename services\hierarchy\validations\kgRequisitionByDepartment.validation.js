const Joi = require("joi");
const { idValidation } = require("../../../utils/validator.util");

const getArazCityZoneByIdSchema = Joi.object({
  arazCityID: idValidation
});

const getKgRequisitionByDepartmentSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  arazCityZoneID: idValidation,
});
const getKgRequisitionByZoneSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  departmentID: idValidation,
});

module.exports = {
  getArazCityZoneByIdSchema,
  getKgRequisitionByDepartmentSchema,
  getKgRequisitionByZoneSchema,
};
