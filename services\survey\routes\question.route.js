const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");

const { getAllQuestions, getSingleQuestion, deleteQuestion, addEditQuestion, getMyQuestions, activateQuestion, getSimilarQuestions } = require("../controllers/question.controller");

const { getAllQuestionsSchema, getSingleQuestionSchema, deleteQuestionSchema, addEditQuestionSchema } = require("../validations/question.validation");


router.get("/get", validate(getAllQuestionsSchema, "query"), getMyQuestions);

router.post("/add", validate(addEditQuestionSchema, "body"), addEditQuestion);

router.post("/get/similar", getSimilarQuestions);

router.get("/get/:id", validate(getSingleQuestionSchema, "params"), getSingleQuestion);

router.put("/edit/:id", validate(addEditQuestionSchema, "body"), addEditQuestion);

router.post("/add/activate/:id", validate(deleteQuestionSchema, "params"), activateQuestion);

router.delete("/delete/:id", validate(deleteQuestionSchema, "params"), deleteQuestion);

module.exports = router;

