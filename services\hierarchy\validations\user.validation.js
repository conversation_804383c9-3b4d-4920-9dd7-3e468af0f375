const Joi = require("joi");
const {
  stringValidation,
  idValidation,
  emailValidation,
  numberValidation,
  booleanValidation,
} = require("../../../utils/validator.util");

const getITSUserSchema = Joi.object({
  id: stringValidation,
});

const syncITSUserSchema = Joi.object({
  id: idValidation,
  ITSID: stringValidation
}).unknown();

const updateKGConsentStatusSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  consentAccepted: booleanValidation,
  declineReason: stringValidation.optional().allow(""),
});

const getUserDetailByITSSchema = Joi.object({
  params: {
    id: stringValidation,
  },
  query: {
    miqaatID: idValidation,
    arazCityID: idValidation,
  },
});
const getUserByITSIDSchema = Joi.object({
  arazCityID: idValidation.optional().allow(""),
  miqaatID: idValidation.optional().allow(""),
  ITS: Joi.array().items(stringValidation).min(1),
});
module.exports = {
  getITSUserSchema,
  syncITSUserSchema,
  updateKGConsentStatusSchema,
  getUserDetailByITSSchema,
  getUserByITSIDSchema,
};
