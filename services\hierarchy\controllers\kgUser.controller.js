const {
  a<PERSON><PERSON><PERSON><PERSON>,
  api<PERSON><PERSON>r,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  CUSTOM_SUCCESS,
  ADD_ERROR,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const {
  ArazCity,
  KGUser,
  HierarchyPosition,
  SystemRole,
  Jamaat,
  Department,
  ArazCityZone,
  Function,
} = require("../models");
const path = require("path");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const {
  generateHierarchy,
  updateHierarchy,
} = require("./hierarchy.controller");
const { getITSUserData } = require("../../../utils/ITSHelper.util");
const { ITSID_ALI, ITSID_HB, SYSTEM_ROLES } = require("../../../constants");
const { deviceMeta } = require("../models/kgUser.model");
const { Interest } = require("../models/interest.model");
const { assignUserToGroup } = require("../../../utils/openProject");
const {
  getITSUsersAggregation,
} = require("../aggregations/kgUser.aggregation");
const { debug } = require("console");
const {
  checkFasalDate,
} = require("../../globalMasters/controllers/arazCity.controller");
const sendEmail = require("../../../utils/email.util");
const { welcomeEmail } = require("../../../utils/emailTemplates/welcomeEmail");
const {
  getCache,
  setCache,
  redisCacheKeys,
} = require("../../../utils/redis.cache");
const {
  addEventLog,
  EventActions,
  Modules,
} = require("../../../utils/eventLogs.util");
const constants = require("../../../constants");
const {
  buildKGUsersPipeline,
  buildKGUsersPipelineV2,
} = require("../aggregations/kgList.aggregation");
const { extractPermission } = require("../../../middlewares/guard.middleware");
const { RazaMapping } = require("../../zonesCapacity/models");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const checkUserJamaat = async (arazCityData, userJamaatID, ITSID) => {
  try {
    const jamaats = arazCityData.jamaats;
    const jamiats = arazCityData.jamiats;
    const jamaatFilter =
      jamaats[0] === "all"
        ? { jamiatID: { $in: jamiats } }
        : { _id: { $in: jamaats } };

    let existingJamaats = await Jamaat.find(jamaatFilter).select("_id");
    existingJamaats = existingJamaats.map((jamaat) => jamaat._id.toString());
    if (existingJamaats.includes(userJamaatID.toString())) {
      return true;
    }
    const razaMapping = await RazaMapping.findOne({
      arazCityID: arazCityData._id,
      miqaatID: arazCityData.miqaatID,
      ITSID,
      RazaStatus: "Has Raza",
    });

    if (!isEmpty(razaMapping)) {
      return true;
    }

    return false;
  } catch (error) {
    return null;
  }
};

const getKGUsers = apiHandler(async (req, res) => {
  const { arazCity, miqaat, kgIDs } = req.body;
  const loggedInUser = req.user;

  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${
    redisCacheKeys.KG_LIST
  }:${arazCity}:${miqaat}:${kgIDs && kgIDs.sort().join(":")}`;

  let data = await getCache(cachekey,miqaat,arazCity);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "KG Users", data, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaat, arazCity, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const pipeline = [
    {
      $addFields: {
        filteredMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                as: "miqaat",
                cond: {
                  $and: [
                    { $eq: ["$$miqaat.miqaatID", toObjectId(miqaat)] },
                    { $ne: ["$$miqaat.status", "DELETED"] },
                    { $eq: ["$$miqaat.arazCityID", toObjectId(arazCity)] },
                    {
                      $ne: [
                        "$$miqaat.cityRoleID",
                        toObjectId(SYSTEM_ROLES.NOT_ASSIGNED[0]),
                      ],
                    },
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $match: {
        filteredMiqaat: { $ne: null },
      },
    },
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiat",
      },
    },
    { $unwind: { path: "$jamiat", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaat",
      },
    },
    { $unwind: { path: "$jamaat", preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        miqaat: "$filteredMiqaat",
      },
    },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaat.kgGroupID",
        foreignField: "_id",
        as: "kgGroupDetails",
      },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaat.kgTypeID",
        foreignField: "_id",
        as: "kgTypeDetails",
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaat.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPositionDetails",
      },
    },
    {
      $lookup: {
        from: "departments",
        localField: "miqaat.departmentID",
        foreignField: "_id",
        as: "departmentDetails",
      },
    },
    {
      $lookup: {
        from: "functions",
        localField: "miqaat.functionID",
        foreignField: "_id",
        as: "functionDetails",
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaat.arazCityZoneID",
        foreignField: "_id",
        as: "arazCityZoneDetails",
      },
    },
    {
      $lookup: {
        from: "systemroles",
        localField: "miqaat.cityRoleID",
        foreignField: "_id",
        as: "cityRoleDetails",
      },
    },
    {
      $lookup: {
        from: "kgusers",
        localField: "createdBy",
        foreignField: "_id",
        as: "createdByUser",
      },
    },
    {
      $lookup: {
        from: "kgusers",
        localField: "updatedBy",
        foreignField: "_id",
        as: "updatedByUser",
      },
    },
    {
      $project: {
        _id: 1,
        ITSID: 1,
        name: 1,
        LDName: 1,
        logo: 1,
        age: 1,
        email: 1,
        phone: 1,
        whatsapp: 1,
        systemRoleID: 1,
        jamiat: { id: "$jamiat._id", name: "$jamiat.name" },
        jamaat: { id: "$jamaat._id", name: "$jamaat.name" },
        isInternationalPlugin: "$miqaat.isInternationalPlugin",
        gender: 1,
        maritialStatus: 1,
        occupation: 1,
        qualification: 1,
        organization: 1,
        prefix: 1,
        misaq: 1,
        idara: 1,
        category: 1,
        address: 1,
        city: 1,
        country: 1,
        nationality: 1,
        vatan: 1,
        hierarchyPosition: {
          id: { $arrayElemAt: ["$hierarchyPositionDetails._id", 0] },
          name: { $arrayElemAt: ["$hierarchyPositionDetails.name", 0] },
          alias: { $arrayElemAt: ["$hierarchyPositionDetails.alias", 0] },
        },
        kgType: {
          id: { $arrayElemAt: ["$kgTypeDetails._id", 0] },
          name: { $arrayElemAt: ["$kgTypeDetails.name", 0] },
          color: { $arrayElemAt: ["$kgTypeDetails.color", 0] },
          priority: { $arrayElemAt: ["$kgTypeDetails.priority", 0] },
        },
        kgGroup: {
          id: { $arrayElemAt: ["$kgGroupDetails._id", 0] },
          name: { $arrayElemAt: ["$kgGroupDetails.name", 0] },
        },
        arazCityZone: {
          id: { $arrayElemAt: ["$arazCityZoneDetails._id", 0] },
          name: { $arrayElemAt: ["$arazCityZoneDetails.name", 0] },
          priority: { $arrayElemAt: ["$arazCityZoneDetails.priority", 0] },
        },
        department: {
          id: { $arrayElemAt: ["$departmentDetails._id", 0] },
          name: { $arrayElemAt: ["$departmentDetails.name", 0] },
          priority: { $arrayElemAt: ["$departmentDetails.priority", 0] },
        },
        function: {
          id: { $arrayElemAt: ["$functionDetails._id", 0] },
          name: { $arrayElemAt: ["$functionDetails.name", 0] },
          priority: { $arrayElemAt: ["$functionDetails.priority", 0] },
        },
        cityRole: {
          id: { $arrayElemAt: ["$cityRoleDetails._id", 0] },
          name: { $arrayElemAt: ["$cityRoleDetails.name", 0] },
          uniqueName: { $arrayElemAt: ["$cityRoleDetails.uniqueName", 0] },
        },
        status: "$miqaat.status",
        isActive: "$miqaat.isActive",
        declineReason: "$miqaat.declineReason",
        otherFunction: "$miqaat.otherFunction",
        createdAt: 1,
        updatedAt: 1,
        createdBy: { $arrayElemAt: ["$createdByUser.name", 0] },
        updatedBy: { $arrayElemAt: ["$updatedByUser.name", 0] },
      },
    },
    { $sort: { _id: -1 } },
  ];

  if (!isEmpty(kgIDs)) {
    pipeline.unshift({
      $match: { _id: { $in: toObjectId(kgIDs) } },
    });
  }

  const kgUsersData = await KGUser.aggregate(pipeline);

  if (isEmpty(kgUsersData)) {
    return apiError(NOT_FOUND, "Active KG Users in Miqaat and City", null, res);
  }

  let allowPhone = req.allowPhone;

  // const LADIES_RULE = constants.LADIES_PHONE_VALDIATION.find(
  //   (r) => r.arazCityID === arazCity.toString()
  // );

  // if (LADIES_RULE) {
  //   if (loggedInUser.systemRoleID) {
  //     allowPhone = LADIES_RULE.permission.includes(
  //       loggedInUser.systemRoleID.toString()
  //     );
  //   } else {
  //     const matchedMiqaat = loggedInUser.miqaats.find(
  //       (m) =>
  //         m.arazCityID?.toString() === arazCity.toString() &&
  //         LADIES_RULE.permission.includes(m.cityRoleID?.toString())
  //     );
  //     if (matchedMiqaat) {
  //       allowPhone = true;
  //     }
  //   }
  // }

  const updatedKGUsers = kgUsersData.map((user, index) => {
    if (user.gender === "M") {
      return user;
    }

    if (!allowPhone) {
      user.phone = undefined;
      user.whatsapp = undefined;
      return user;
    }

    return user;
  });

  apiResponse(FETCH, "KG Users", updatedKGUsers, res);
  await setCache(cachekey, updatedKGUsers, miqaat, arazCity);
});

const getAllKGUsers = apiHandler(async (req, res) => {
  const cacheKey = `${redisCacheKeys.HIERARCHY}:${redisCacheKeys.KG_LIST}:${redisCacheKeys.KG_USERS_LIST}`;

  const cachedData = await getCache(cacheKey);
  if (cachedData) {
    return apiResponse(FETCH, "All KG Users", cachedData, res, true);
  }
  const kgUsersData = await KGUser.aggregate([
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiat",
      },
    },
    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaat",
      },
    },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaats.miqaatID",
        foreignField: "_id",
        as: "miqaatDetails",
      },
    },
    {
      $lookup: {
        from: "arazcities",
        localField: "miqaats.arazCityID",
        foreignField: "_id",
        as: "arazCityDetails",
      },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgTypeDetails",
      },
    },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaats.kgGroupID",
        foreignField: "_id",
        as: "kgGroupDetails",
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPositionDetails",
      },
    },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "departmentDetails",
      },
    },
    {
      $lookup: {
        from: "functions",
        localField: "miqaats.functionID",
        foreignField: "_id",
        as: "functionDetails",
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "arazCityZoneDetails",
      },
    },
    {
      $addFields: {
        "miqaats.miqaat": {
          id: { $arrayElemAt: ["$miqaatDetails._id", 0] },
          name: { $arrayElemAt: ["$miqaatDetails.name", 0] },
        },
        "miqaats.arazCity": {
          id: { $arrayElemAt: ["$arazCityDetails._id", 0] },
          name: { $arrayElemAt: ["$arazCityDetails.name", 0] },
        },
        "miqaats.kgType": {
          id: { $arrayElemAt: ["$kgTypeDetails._id", 0] },
          name: { $arrayElemAt: ["$kgTypeDetails.name", 0] },
        },
        "miqaats.kgGroup": {
          id: { $arrayElemAt: ["$kgGroupDetails._id", 0] },
          name: { $arrayElemAt: ["$kgGroupDetails.name", 0] },
        },
        "miqaats.hierarchyPosition": {
          id: { $arrayElemAt: ["$hierarchyPositionDetails._id", 0] },
          name: { $arrayElemAt: ["$hierarchyPositionDetails.name", 0] },
          alias: { $arrayElemAt: ["$hierarchyPositionDetails.alias", 0] },
        },
        "miqaats.department": {
          id: { $arrayElemAt: ["$departmentDetails._id", 0] },
          name: { $arrayElemAt: ["$departmentDetails.name", 0] },
        },
        "miqaats.function": {
          id: { $arrayElemAt: ["$functionDetails._id", 0] },
          name: { $arrayElemAt: ["$functionDetails.name", 0] },
        },
        "miqaats.arazCityZone": {
          id: { $arrayElemAt: ["$arazCityZoneDetails._id", 0] },
          name: { $arrayElemAt: ["$arazCityZoneDetails.name", 0] },
        },
      },
    },
    {
      $group: {
        _id: "$_id",
        name: { $first: "$name" },
        ITSID: { $first: "$ITSID" },
        phone: { $first: "$phone" },
        whatsapp: { $first: "$whatsapp" },
        logo: { $first: "$logo" },
        jamiat: { $first: "$jamiat" },
        jamaat: { $first: "$jamaat" },
        miqaats: { $push: "$miqaats" },
      },
    },
    {
      $project: {
        _id: 1,
        logo: 1,
        phone: 1,
        whatsapp: 1,
        name: 1,
        LDName: 1,
        ITSID: 1,
        email: 1,
        status: 1,
        createdAt: 1,
        updatedAt: 1,
        jamiat: {
          id: { $arrayElemAt: ["$jamiat._id", 0] },
          name: { $arrayElemAt: ["$jamiat.name", 0] },
        },
        jamaat: {
          id: { $arrayElemAt: ["$jamaat._id", 0] },
          name: { $arrayElemAt: ["$jamaat.name", 0] },
        },
        "miqaats.isActive": 1,
        "miqaats.consentRequired": 1,
        "miqaats.consentAccepted": 1,
        "miqaats.isInternationalPlugin": 1,
        "miqaats._id": 1,
        "miqaats.miqaat": 1,
        "miqaats.arazCity": 1,
        "miqaats.kgType": 1,
        "miqaats.kgGroup": 1,
        "miqaats.hierarchyPosition": 1,
        "miqaats.arazCityZone": 1,
        "miqaats.department": 1,
        "miqaats.function": 1,
        "miqaats.otherFunction": 1,
      },
    },

    { $sort: { _id: -1 } },
  ]);

  if (isEmpty(kgUsersData)) {
    return apiError(NOT_FOUND, "KG Users", null, res);
  }
  apiResponse(FETCH, "All KG Users", kgUsersData, res);

  await setCache(cacheKey, kgUsersData);
});

// const isAuthorized = async (req) => {
//   try {
//     const { hierarchyPositionID, arazCityID, arazCityZoneID, miqaatID, id } =
//       req.body;
//     const loggedInUser = req.user;

//     if (!loggedInUser || !loggedInUser.miqaats) return false;

//     // SUPER ADMIN has full access
//     if (
//       loggedInUser.systemRoleID &&
//       loggedInUser.systemRoleID.toString() ===
//         SYSTEM_ROLES.SUPER_ADMIN[0].toString()
//     ) {
//       return true;
//     }

//     // Match logged-in user's miqaat
//     const userMiqaat = loggedInUser.miqaats.find(
//       (miqaat) =>
//         miqaat &&
//         miqaat.miqaatID?.toString() === miqaatID?.toString() &&
//         miqaat.arazCityID?.toString() === arazCityID?.toString() &&
//         miqaat.isActive
//     );

//     if (!userMiqaat) return false;

//     if (!userMiqaat.arazCityZoneID) {
//       return true;
//     }
//     if (
//       hierarchyPositionID &&
//       hierarchyPositionID.toString() !==
//         constants.HIERARCHY_POSITIONS.ZONE_TEAM[0].toString()
//     ) {
//       return false;
//     }

//     if (
//       !arazCityZoneID ||
//       arazCityZoneID.toString() !== userMiqaat.arazCityZoneID.toString()
//     ) {
//       return false;
//     }

//     if (!isEmpty(id)) {
//       const userToUpdate = await KGUser.findById(id);

//       if (!userToUpdate) return false;

//       // If target user has no miqaats, assume core – allow
//       if (
//         !Array.isArray(userToUpdate.miqaats) ||
//         userToUpdate.miqaats.length === 0
//       ) {
//         return true;
//       }

//       // Find miqaat of user being updated
//       const userToUpdateMiqaat = userToUpdate.miqaats.find(
//         (miqaat) =>
//           miqaat &&
//           miqaat.miqaatID?.toString() === miqaatID?.toString() &&
//           miqaat.arazCityID?.toString() === arazCityID?.toString() &&
//           miqaat.isActive
//       );

//       // If no miqaat matched, assume core – allow
//       if (!userToUpdateMiqaat) return true;

//       if (
//         userToUpdateMiqaat.arazCityZoneID &&
//         userToUpdateMiqaat.arazCityZoneID.toString() !==
//           userMiqaat.arazCityZoneID.toString()
//       ) {
//         return false;
//       }
//     }

//     return true;
//   } catch (error) {
//     console.error("Authorization check failed:", error);
//     return false;
//   }
// };
const isAuthorized = async (req) => {
  try {
    const { hierarchyPositionID, arazCityID, arazCityZoneID, miqaatID, id } =
      req.body;
    const zoneTeam = constants.HIERARCHY_POSITIONS.ZONE_TEAM[0].toString();
    const loggedInUser = req.user;
    // if (
    //   loggedInUser.systemRoleID &&
    //   loggedInUser.systemRoleID.toString() ===
    //     SYSTEM_ROLES.SUPER_ADMIN[0].toString()
    // ) {
    //   return true;
    // }
    if (!isEmpty(loggedInUser.systemRoleID)) {
      return true;
    }
    const loggedInUserMiqaat = loggedInUser.miqaats.find(
      (miqaat) =>
        miqaat &&
        miqaat.miqaatID?.toString() === miqaatID?.toString() &&
        miqaat.arazCityID?.toString() === arazCityID?.toString() &&
        miqaat.isActive &&
        miqaat.status !== "DELETED"
    );

    if (!loggedInUserMiqaat) {
      return false;
    }

    if (loggedInUserMiqaat.arazCityZoneID) {
      if (
        !arazCityZoneID ||
        loggedInUserMiqaat.arazCityZoneID.toString() !== arazCityZoneID
      ) {
        return false;
      }
      if (isEmpty(id)) {
        if (hierarchyPositionID !== zoneTeam) {
          return false;
        } else {
          return true;
        }
      } else {
        const userToUpdate = await KGUser.findById(id);

        const oldData = userToUpdate.miqaats.find(
          (miqaat) =>
            miqaat &&
            miqaat.miqaatID?.toString() === miqaatID?.toString() &&
            miqaat.arazCityID?.toString() === arazCityID?.toString() &&
            miqaat.status !== "DELETED" &&
            miqaat.isActive == true
        );
        if (!oldData || !oldData.hierarchyPositionID) {
          if (hierarchyPositionID !== zoneTeam) {
            return false;
          } else {
            return true;
          }
        }
        if (oldData.hierarchyPositionID.toString() !== hierarchyPositionID) {
          return false;
        } else if (hierarchyPositionID !== zoneTeam) {
          return false;
        } else if (
          oldData.arazCityZoneID.toString() !== arazCityZoneID.toString()
        ) {
          return false;
        } else {
          return true;
        }
      }
    } else {
      return true;
    }
  } catch (error) {
    console.error("Authorization check failed:", error);
    return false;
  }
};

const addEditKGUserFunc = async (req, res = null) => {
  const {
    id,
    name,
    LDName,
    ITSID,
    logo,
    email,
    phone,
    whatsapp,
    status,
    consentRequired,
    consentAccepted,
    hierarchyPositionID,
    hierarchyPositionName,
    kgGroupID,
    kgGroup,
    kgTypeID,
    kgType,
    kgTypeColor,
    jamiatID,
    jamaatID,
    arazCityID,
    arazCityZoneID,
    arazCityZoneName,
    miqaatID,
    departmentID,
    departmentName,
    functionID,
    otherFunction,
    cityRoleID,
    isInternationalPlugin,
    isAddedFromRazaList,
    age,
    gender,
    maritialStatus,
    prefix,
    misaq,
    occupation,
    qualification,
    idara,
    category,
    organization,
    address,
    city,
    country,
    nationality,
    vatan,
  } = req.body;
  let KGID = id;

  const authorized = await isAuthorized(req);
  if (!authorized) {
    return apiError(CUSTOM_ERROR, "You are not authorized", null, res);
  }

  const arazCityData = await ArazCity.findOne({ _id: arazCityID }).select(
    "fasalDate status jamiats jamaats name showPositionAlias addToOpenProject miqaatID"
  );

  if (!arazCityData?.status) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  if (!req.user.systemRoleID) {
    const isFasalDatePassed = await checkFasalDate(arazCityData);
    // if (isEmpty(isFasalDatePassed) || !isFasalDatePassed) {
    const isJamaatUser = await checkUserJamaat(arazCityData, jamaatID, ITSID);

    if (!isJamaatUser || isEmpty(isJamaatUser)) {
      return apiError(
        CUSTOM_ERROR,
        "User does not belong to Jamiat",
        null,
        res
      );
    }
    // }
  }

  const newMiqaat = {
    miqaatID,
    arazCityID,
    kgTypeID: kgTypeID || null,
    kgGroupID: kgGroupID || null,
    hierarchyPositionID: hierarchyPositionID || null,
    arazCityZoneID: arazCityZoneID || null,
    departmentID: departmentID || null,
    functionID: functionID || null,
    otherFunction: otherFunction || null,
    cityRoleID: cityRoleID || SYSTEM_ROLES.DEFAULT[0],
    consentRequired,
    consentAccepted,
    status: consentRequired ? "PENDING" : "ACCEPTED",
    isInternationalPlugin,
    updatedAt: new Date(),
    isAddedFromRazaList,
  };

  const razaStatus = await RazaMapping.findOne({
    miqaatID,
    arazCityID,
    ITSID,
  });
  if (!isEmpty(razaStatus)) {
    newMiqaat.miqaatHR = {
      RazaStatus: razaStatus.RazaStatus,
      MiqaatZone: razaStatus.MiqaatZone,
    };
  }

  let data = {
    name,
    LDName,
    ITSID,
    logo,
    email,
    phone,
    whatsapp,
    status,
    jamiatID,
    jamaatID,
    age,
    gender,
    maritialStatus,
    prefix,
    misaq,
    occupation,
    qualification,
    idara,
    category,
    organization,
    address,
    city,
    country,
    nationality,
    vatan,
  };

  let defaultITSUsers = [ITSID_ALI, ITSID_HB];
  // let assignPMO = false;

  // if (!defaultITSUsers.includes(ITSID.toString())) {
  //   if (!isEmpty(hierarchyPositionID)) {
  //     const hierarchyPosition = await HierarchyPosition.findOne(
  //       { _id: toObjectId(hierarchyPositionID) },
  //       "uniqueName"
  //     );

  //     if (
  //       hierarchyPosition?.uniqueName?.toLowerCase() === SYSTEM_ROLES.PMO[1]
  //     ) {
  //       assignPMO = true;
  //     }
  //   }

  //   if (assignPMO) {
  //     cityRoleID = SYSTEM_ROLES.PMO[0].toString();
  //   } else {
  //     cityRoleID = SYSTEM_ROLES.DEFAULT[0].toString();
  //   }
  // }

  if (!isEmpty(id)) {
    data["updatedBy"] = req.user._id;
    let kgUserData = await KGUser.findByIdAndUpdate(
      id,
      {
        $set: data,
      },
      {
        new: true,
        runValidators: true,
      }
    );
    if (
      arazCityData.addToOpenProject &&
      (hierarchyPositionID || departmentID || arazCityZoneID)
    ) {
      await assignUserToGroup(
        kgUserData,
        arazCityID,
        miqaatID,
        hierarchyPositionID,
        departmentID,
        arazCityZoneID
      );
    }

    if (isEmpty(kgUserData)) {
      return apiError(NOT_FOUND, "KG User", null, res);
    }

    await KGUser.findByIdAndUpdate(
      id,
      {
        $pull: {
          miqaats: {
            miqaatID: toObjectId(newMiqaat.miqaatID),
            arazCityID: toObjectId(newMiqaat.arazCityID),
          },
        },
      },
      {
        new: true,
      }
    );

    kgUserData = await KGUser.findByIdAndUpdate(
      id,
      {
        $addToSet: { miqaats: newMiqaat },
      },
      {
        new: true,
      }
    );
    await addEventLog(
      EventActions.UPDATE,
      Modules.Hierarchy,
      "KG User",
      req.user._id
    );

    apiResponse(UPDATE_SUCCESS, "KG User", kgUserData, res);
  } else {
    data["createdBy"] = req.user._id;
    const newKGUser = new KGUser(data);

    if (data.email) {
      const emailData = {
        name: data.name,
        cityName: arazCityData?.name || "",
      };

      if (!isEmpty(hierarchyPositionID)) {
        const hierarchyPosition = await HierarchyPosition.findById(
          hierarchyPositionID
        );

        emailData.hierarchyPositionName = arazCityData?.showPositionAlias
          ? hierarchyPosition?.alias
          : hierarchyPosition?.name;
      }

      if (!isEmpty(departmentID)) {
        const department = await Department.findById(departmentID);
        emailData.departmentName = department?.name || "";
      }

      await sendEmail.sendTemplatedEmail(data.email, welcomeEmail, emailData);
    }

    if (
      arazCityData.addToOpenProject &&
      (hierarchyPositionID || departmentID || arazCityZoneID)
    ) {
      await assignUserToGroup(
        newKGUser,
        arazCityID,
        miqaatID,
        hierarchyPositionID,
        departmentID,
        arazCityZoneID
      );
    }
    let userData = await newKGUser.save();
    KGID = userData._id;

    userData = await KGUser.findByIdAndUpdate(
      userData.id,
      {
        $addToSet: { miqaats: newMiqaat },
      },
      {
        new: true,
      }
    );
    await addEventLog(
      EventActions.CREATE,
      Modules.Hierarchy,
      "KG User",
      req.user._id
    );
    apiResponse(ADD_SUCCESS, "KG User", userData, res);
  }

  await Interest.findOneAndUpdate(
    {
      userID: id,
      miqaatID: toObjectId(miqaatID),
      arazCityID: toObjectId(arazCityID),
    },
    {
      $set: {
        status: "assigned",
      },
    }
  );

  const userDataForHierarchy = {
    KGType: kgType || null,
    KGTypeID: kgTypeID || null,
    KGTypeColor: kgTypeColor || null,
    KGGroup: kgGroup || null,
    KGGroupID: kgGroupID || null,
    logo: logo,
    name: name || "",
    LDName: LDName || "",
    ITSID: ITSID || "",
    email: email || "",
    phone: phone || "",
    whatsapp: whatsapp || "",
    age: age || "",
    gender: gender || "",
    KGID,
    departmentID: departmentID || null,
    departmentName: departmentName || null,
    arazCityZoneID: arazCityZoneID || null,
    arazCityZoneName: arazCityZoneName || null,
    hierarchyPositionID: hierarchyPositionID || null,
    hierarchyPositionName: hierarchyPositionName || null,
    isInternationalPlugin: isInternationalPlugin || false,
  };
  if (!isEmpty(hierarchyPositionID)) {
    await updateHierarchy(
      miqaatID,
      arazCityID,
      [userDataForHierarchy],
      "add-edit"
    );
    await addEventLog(
      EventActions.UPDATE,
      Modules.Hierarchy,
      "Hierarchy for KG User Updated",
      constants.AMS_SYSTEMID
    );
  }
};

const addEditKGUser = apiHandler(async (req, res) => {
  await addEditKGUserFunc(req, res);
});

const assignKGUserV2 = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, existsInDB, notExistsInDB } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let results = { success: [], failed: [] };
  const usersDataForHierarchy = [];

  const arazCityData = await fetchArazCityData(arazCityID);
  if (!arazCityData)
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);

  const isFasalDatePassed = await checkFasalDate(arazCityData);

  let bulkOps = { pull: [], add: [], upsert: [] };

  await processUsers(
    existsInDB,
    true,
    results,
    bulkOps,
    arazCityData,
    isFasalDatePassed,
    miqaatID,
    arazCityID,
    usersDataForHierarchy,
    req
  );
  await processUsers(
    notExistsInDB,
    false,
    results,
    bulkOps,
    arazCityData,
    isFasalDatePassed,
    miqaatID,
    arazCityID,
    usersDataForHierarchy,
    req
  );

  await executeBulkOperations(bulkOps);

  apiResponse(UPDATE_SUCCESS, "KG User Assignment", results, res);
  await updateHierarchy(
    miqaatID,
    arazCityID,
    usersDataForHierarchy,
    "add-edit"
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.Hierarchy,
    "Hierarchy for Assign KG User",
    constants.AMS_SYSTEMID
  );
  // await generateHierarchy(miqaatID, arazCityID);
});

const getKgTypeID = (arazCityJamaats, ITSID, gender, jamaatID) => {
  const localKgMardoID = "67cb50094db9413c913188bc";
  const localKgBairoID = "684e5b0ed7129dca6db8f016";
  const intlKgMardoID = "684246cad21636599c8b6178";
  const intlKgBairoID = "684e6029d7129dca6db92abc";
  const qasreAaliID = "679b2a4ec6b35bcef2928d29";

  let kgTypeID = null;
  if (ITSID.startsWith("78652")) {
    kgTypeID = qasreAaliID;
  } else if (gender === "M" && arazCityJamaats.includes(jamaatID.toString())) {
    kgTypeID = localKgMardoID;
  } else if (gender === "M" && !arazCityJamaats.includes(jamaatID.toString())) {
    kgTypeID = intlKgMardoID;
  } else if (gender === "F" && arazCityJamaats.includes(jamaatID.toString())) {
    kgTypeID = localKgBairoID;
  } else if (gender === "F" && !arazCityJamaats.includes(jamaatID.toString())) {
    kgTypeID = intlKgBairoID;
  }

  return kgTypeID;
};

const getKgGroupID = (hierarchyPositionID) => {
  const positionsMap = {
    "678cb1454a83be4c233903b6": "678915d672fa3ee5bb648dc1", // zoneTeam
    "678cafae4a83be4c2339039d": "678915d672fa3ee5bb648dc1", // zoneLead
    "681b0354a892ac1747e332a2": "678915d672fa3ee5bb648dc0", // hodTeam
    "681b52ff085b902c11eb6b52": "678915d672fa3ee5bb648dc0", // pmoTeam
    "678ca8d64a83be4c233902ac": "678915d672fa3ee5bb648dbf", // hod
    "678ca84a4a83be4c2339028e": "678915d672fa3ee5bb648dbf", // pmo
  };

  return positionsMap[hierarchyPositionID] || null;
};

const uploadKGUser = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    users,
    overwriteRecords,
    overwriteKGGroup,
    overwriteKGType,
    overwritePermission,
  } = req.body;


  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const departments = await Department.find({}).select("name cityRoleID");
  const zones = await ArazCityZone.find({
    $or: [{ arazCityID: toObjectId(arazCityID) }, { uniqueName: "cmz" }],
  }).select("name");
  const designations = await HierarchyPosition.find({}).select("name");
  const functions = await Function.find({}).select("name");

  let results = { success: [], failed: [], ITSIssue: [] };
  const usersDataForHierarchy = [];

  const arazCityData = await fetchArazCityData(arazCityID);
  if (!arazCityData) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCityJamaats = arazCityData.jamaats;
  const isFasalDatePassed = await checkFasalDate(arazCityData);

  const existsInDB = [];
  const notExistsInDB = [];
  const existingUsers = await KGUser.aggregate([
    {
      $project: {
        ITSID: 1,
        miqaats: 1,
        logo: 1,
        name: 1,
        LDName: 1,
        email: 1,
        phone: 1,
        whatsapp: 1,
        age: 1,
        gender: 1,
        jamaatID: 1,
      },
    },
  ]);

  for (const user of users) {
    const foundUser = existingUsers.find((u) => u.ITSID === user.ITSID);
    const userData = {
      ITSID: user.ITSID.trim(),
    };
    let departmentID;
    let functionID;
    let cityRoleID;
    let arazCityZoneID;
    let hierarchyPositionID;
    try {
      userData.departmentName = user.department.trim();
      userData.arazCityZoneName = user.zone.trim();
      userData.hierarchyPositionName = user.designation.trim();
      userData.functionName = user.function.trim();

      const foundDepartment = departments.find(
        (d) => d.name.toLowerCase() === user.department.trim().toLowerCase()
      );
      departmentID = foundDepartment ? foundDepartment._id : null;
      cityRoleID = foundDepartment
        ? foundDepartment.cityRoleID
        : SYSTEM_ROLES.NOT_ASSIGNED[0];

      const foundZone = zones.find(
        (z) => z.name.toLowerCase() === user.zone.trim().toLowerCase()
      );
      arazCityZoneID = foundZone ? foundZone._id : null;

      const foundDesignation = designations.find(
        (d) => d.name.toLowerCase() === user.designation.trim().toLowerCase()
      );
      hierarchyPositionID = foundDesignation ? foundDesignation._id : null;

      const foundFunction = functions.find(
        (d) => d.name?.toLowerCase() === user?.function?.trim().toLowerCase()
      );
      functionID = foundFunction ? foundFunction._id : null;
    } catch (error) {
      userData.error = error.message;
    }

    if (foundUser) {
      userData.id = foundUser._id;
      userData.KGID = foundUser._id;
      userData.name = foundUser.name;
      userData.LDName = foundUser.LDName;
      userData.email = foundUser.email;
      userData.phone = foundUser.phone;
      userData.whatsapp = foundUser.whatsapp;
      userData.age = foundUser.age;
      userData.gender = foundUser.gender;
      userData.logo = foundUser.logo;

      const miqaat = foundUser.miqaats.find(
        (m) => m.arazCityID.toString() === arazCityID
      );

      if (miqaat) {
        existsInDB.push({
          ...userData,
          ...miqaat,
          hierarchyPositionID: overwriteRecords
            ? hierarchyPositionID
            : miqaat.hierarchyPositionID,
          functionID: overwriteRecords ? functionID : miqaat.functionID,
          arazCityZoneID: overwriteRecords
            ? arazCityZoneID
            : miqaat.arazCityZoneID,
          departmentID: overwriteRecords ? departmentID : miqaat.departmentID,
          cityRoleID:
            overwritePermission ||
            miqaat.cityRoleID?.toString() ===
              SYSTEM_ROLES.NOT_ASSIGNED[0].toString() ||
            isEmpty(miqaat.cityRoleID)
              ? cityRoleID
              : miqaat.cityRoleID,
          kgTypeID:
            overwriteKGType ||
            isEmpty(miqaat.kgTypeID) ||
            user.ITSID.startsWith("78652")
              ? getKgTypeID(
                  arazCityJamaats,
                  user.ITSID,
                  foundUser.gender,
                  foundUser.jamaatID
                )
              : miqaat.kgTypeID,
          kgGroupID:
            overwriteKGGroup || isEmpty(miqaat.kgGroupID)
              ? getKgGroupID(hierarchyPositionID?.toString())
              : miqaat.kgGroupID,
        });
      } else {
        existsInDB.push({
          ...userData,
          hierarchyPositionID,
          functionID,
          arazCityZoneID,
          departmentID,
          cityRoleID,
          kgTypeID: getKgTypeID(
            arazCityJamaats,
            user.ITSID,
            foundUser.gender,
            foundUser.jamaatID
          ),
          kgGroupID: getKgGroupID(hierarchyPositionID?.toString()),
          consentRequired: false,
          consentAccepted: true,
          otherFunction: "",
          status: "ACCEPTED",
          isInternationalPlugin: false,
        });
      }
    } else {
      try {
        const ITSUserData = await getITSUserData(user.ITSID);
        if (isEmpty(ITSUserData)) {
          userData.notFoundFromITS = true;
        }

        notExistsInDB.push({
          ...ITSUserData,
          ...userData,
          hierarchyPositionID,
          functionID,
          arazCityZoneID,
          departmentID,
          cityRoleID,
          kgTypeID: getKgTypeID(
            arazCityJamaats,
            user.ITSID,
            ITSUserData?.gender,
            ITSUserData?.jamaatID
          ),
          kgGroupID: getKgGroupID(hierarchyPositionID),
          consentRequired: false,
          consentAccepted: true,
          otherFunction: "",
          status: "ACCEPTED",
          isInternationalPlugin: false,
        });
      } catch (error) {
        results.ITSIssue.push({ ITSID: user.ITSID, reason: error.message });
        continue;
      }
    }
  }

  let bulkOps = { pull: [], add: [], upsert: [] };

  await processUsers(
    existsInDB,
    true,
    results,
    bulkOps,
    arazCityData,
    isFasalDatePassed,
    miqaatID,
    arazCityID,
    usersDataForHierarchy,
    req,
    true
  );
  await processUsers(
    notExistsInDB,
    false,
    results,
    bulkOps,
    arazCityData,
    isFasalDatePassed,
    miqaatID,
    arazCityID,
    usersDataForHierarchy,
    req,
    true
  );

  await executeBulkOperations(bulkOps);

  apiResponse(UPDATE_SUCCESS, "KG User Assignment", results, res);

  try {
    await updateHierarchy(
      miqaatID,
      arazCityID,
      usersDataForHierarchy,
      "add-edit"
    );
  } catch (error) {
    console.log("error in update hierarchy", error.message);
  }

  try {
    await addEventLog(
      EventActions.UPDATE,
      Modules.Hierarchy,
      "Hierarchy for Assign KG User",
      constants.AMS_SYSTEMID
    );
  } catch (error) {
    console.log("error in event log", error.message);
  }
  // await generateHierarchy(miqaatID, arazCityID);
});

const processUsers = async (
  users,
  isExisting,
  results,
  bulkOps,
  arazCityData,
  isFasalDatePassed,
  miqaatID,
  arazCityID,
  usersDataForHierarchy,
  req,
  isBypassCheck = false
) => {
  await Promise.all(
    users.map(async (user) => {
      if (user.error) {
        results.failed.push({
          ITSID: user.ITSID,
          reason: user.error,
        });
        return;
      }
      if (!user.hierarchyPositionID) {
        results.failed.push({
          ITSID: user.ITSID,
          reason: "Hiearchy Position is Required",
        });
        return;
      }
      if (user.notFoundFromITS) {
        results.failed.push({
          ITSID: user.ITSID,
          reason: "User not found from ITS",
        });
        return;
      }

      if (!isBypassCheck) {
        const authReqBody = {
          hierarchyPositionID: user.hierarchyPositionID,
          arazCityID: arazCityID,
          arazCityZoneID: user.arazCityZoneID,
          miqaatID: miqaatID,
          id: user.id,
        };

        const tempReq = { ...req, body: authReqBody };
        const authorized = await isAuthorized(tempReq);
        if (!authorized) {
          results.failed.push({
            ITSID: user.ITSID,
            reason: "Insufficient Permissions",
          });
          return;
        }
      }

      usersDataForHierarchy.push({
        KGType: user.kgType || null,
        KGTypeID: user.kgTypeID || null,
        KGTypeColor: user.kgTypeColor || null,
        KGGroup: user.kgGroup || null,
        KGGroupID: user.kgGroupID || null,
        logo: user.logo,
        name: user.name || "",
        LDName: user.LDName || "",
        ITSID: user.ITSID || "",
        email: user.email || "",
        phone: user.phone || "",
        whatsapp: user.whatsapp || "",
        age: user.age || "",
        gender: user.gender || "",
        KGID: user.id,
        departmentID: user.departmentID || null,
        departmentName: user.departmentName || null,
        arazCityZoneID: user.arazCityZoneID || null,
        arazCityZoneName: user.arazCityZoneName || null,
        hierarchyPositionID: user.hierarchyPositionID || null,
        hierarchyPositionName: user.hierarchyPositionName || null,
        isInternationalPlugin: user.isInternationalPlugin || false,
      });

      if (!isBypassCheck) {
        if (
          !(await validateUser(
            req.user,
            user,
            arazCityData,
            isFasalDatePassed,
            results
          ))
        )
          return;
      }

      const newMiqaat = createNewMiqaat(user, miqaatID, arazCityID);

      const razaStatus = await RazaMapping.findOne({
        miqaatID,
        arazCityID,
        ITSID: user.ITSID,
      });
      if (!isEmpty(razaStatus)) {
        newMiqaat.miqaatHR = {
          RazaStatus: razaStatus.RazaStatus,
          MiqaatZone: razaStatus.MiqaatZone,
        };
      }

      const { hierarchyPositionID, departmentID, arazCityZoneID } = newMiqaat;
      if (
        arazCityData.addToOpenProject &&
        (hierarchyPositionID || departmentID || arazCityZoneID)
      ) {
        await assignUserToGroup(
          user,
          arazCityID,
          miqaatID,
          hierarchyPositionID,
          departmentID,
          arazCityZoneID
        );
      }

      if (isExisting) {
        bulkOps.pull.push({
          updateOne: {
            filter: { _id: toObjectId(user.id) },
            update: {
              $pull: {
                miqaats: {
                  miqaatID: toObjectId(miqaatID),
                  arazCityID: toObjectId(arazCityID),
                },
              },
            },
          },
        });

        bulkOps.add.push({
          updateOne: {
            filter: { _id: toObjectId(user.id) },
            update: {
              $addToSet: { miqaats: newMiqaat },
              $set: { updatedBy: req.user._id },
            },
          },
        });

        results.success.push({ ITSID: user.ITSID, status: "Updated" });
      } else {
        bulkOps.upsert.push({
          updateOne: {
            filter: { ITSID: user.ITSID },
            update: {
              $setOnInsert: {
                ...user,
                ITSID: user.ITSID,
                miqaats: [newMiqaat],
                updatedBy: req.user._id,
              },
            },
            upsert: true,
          },
        });

        results.success.push({ ITSID: user.ITSID, status: "Added" });
      }

      await Interest.findOneAndUpdate(
        { ITSID: user.ITSID, miqaatID: miqaatID, arazCityID: arazCityID },
        {
          $set: {
            status: "assigned",
          },
        }
      );
    })
  );
};

const deleteKGUser = apiHandler(async (req, res) => {
  try {
    const { id, arazCity, miqaat } = req.body;

    // Input validation
    if (!id || !arazCity || !miqaat) {
      return apiError(CUSTOM_ERROR, "Required fields are missing", null, res);
    }

    const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
      miqaat,
      arazCity,
      req
    )
    if (!checkActiveMiqaatAndArazcityStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    const userToDelete = await KGUser.findOne({ _id: toObjectId(id) }).select(
      "miqaats ITSID"
    );

    if (isEmpty(userToDelete)) {
      return apiError(NOT_FOUND, "KG User", null, res);
    }

    // Find the specific miqaat for the user to be deleted
    const userMiqaatToDelete = userToDelete.miqaats.find(
      (miqaat_item) =>
        miqaat_item &&
        miqaat_item.arazCityID &&
        miqaat_item.arazCityID.toString() === arazCity.toString() &&
        miqaat_item.miqaatID &&
        miqaat_item.miqaatID.toString() === miqaat.toString() &&
        miqaat_item.isActive
    );

    if (!userMiqaatToDelete) {
      return apiError(NOT_FOUND, "KG User in this Miqaat/City", null, res);
    }

    // Check authorization using the same logic as add/edit
    const loggedInUser = req.user;

    // Zone access control validation for delete operation (FIXED LOGIC)
    if (
      req.user.systemRoleID &&
      req.user.systemRoleID.toString() !==
        SYSTEM_ROLES.SUPER_ADMIN[0].toString()
    ) {
      // Find the specific miqaat for the logged-in user that matches the request
      const userMiqaat = loggedInUser.miqaats.find(
        (miqaat_item) =>
          miqaat_item &&
          miqaat_item.miqaatID &&
          miqaat_item.miqaatID.toString() === miqaat.toString() &&
          miqaat_item.arazCityID &&
          miqaat_item.arazCityID.toString() === arazCity.toString() &&
          miqaat_item.isActive
      );

      if (!userMiqaat) {
        return apiError(
          CUSTOM_ERROR,
          "You are not authorized to delete users in this miqaat/city",
          null,
          res
        );
      }

      // Check if logged-in user is a zone member (has arazCityZoneID)
      if (userMiqaat.arazCityZoneID) {
        // User is a zone member, they can only delete users in their zone
        if (
          !userMiqaatToDelete.hierarchyPositionID ||
          userMiqaatToDelete.hierarchyPositionID.toString() !==
            constants.HIERARCHY_POSITIONS.ZONE_TEAM[0].toString()
        ) {
          return apiError(
            CUSTOM_ERROR,
            "You can only delete Zone Team Members",
            null,
            res
          );
        }

        // Check if the target user belongs to the same zone
        if (
          !userMiqaatToDelete.arazCityZoneID ||
          userMiqaatToDelete.arazCityZoneID.toString() !==
            userMiqaat.arazCityZoneID.toString()
        ) {
          return apiError(
            CUSTOM_ERROR,
            "You can only delete users within your zone",
            null,
            res
          );
        }
      }
    }

    const kgUserData = await KGUser.findOneAndUpdate(
      { _id: toObjectId(id) },
      [
        {
          $set: {
            miqaats: {
              $map: {
                input: "$miqaats",
                as: "miqaat",
                in: {
                  $cond: {
                    if: {
                      $and: [
                        { $eq: ["$$miqaat.arazCityID", toObjectId(arazCity)] },
                        { $eq: ["$$miqaat.miqaatID", toObjectId(miqaat)] },
                      ],
                    },
                    then: {
                      miqaatID: "$$miqaat.miqaatID",
                      arazCityID: "$$miqaat.arazCityID",
                      isActive: true,
                      cityRoleID: toObjectId(
                        constants.SYSTEM_ROLES.NOT_ASSIGNED[0].toString()
                      ),
                      status: "DELETED",
                      updatedAt: new Date(),
                    },
                    else: "$$miqaat",
                  },
                },
              },
            },
            updatedBy: req.user._id,
          },
        },
      ],
      { new: true }
    );

    const updateInterest = await Interest.findOneAndUpdate(
      {
        userID: toObjectId(id),
        miqaatID: toObjectId(miqaat),
        arazCityID: toObjectId(arazCity),
      },
      { $set: { status: "not-assigned" } },
      { new: true }
    );

    if (isEmpty(kgUserData)) {
      return apiError(NOT_FOUND, "KG User in Araz City", null, res);
    }

    await addEventLog(
      EventActions.DELETE,
      Modules.GlobalMasters,
      "KG User",
      req.user._id
    );

    apiResponse(CUSTOM_SUCCESS, "KG User deactivated Successfully", null, res);

    const userDataForHierarchy = {
      ITSID: kgUserData.ITSID,
      miqaatID: miqaat,
      arazCityID: arazCity,
    };

    try {
      await updateHierarchy(miqaat, arazCity, [userDataForHierarchy], "delete");
      await addEventLog(
        EventActions.UPDATE,
        Modules.Hierarchy,
        "Hierarchy for KG User",
        constants.AMS_SYSTEMID
      );
    } catch (hierarchyError) {
      console.error("Hierarchy update failed:", hierarchyError);
      // Non-critical error, continue
    }
  } catch (error) {
    console.error("deleteKGUser error:", error);
    return apiError(CUSTOM_ERROR, "Internal server error", null, res);
  }
});

const getSingleKGUser = apiHandler(async (req, res) => {
  const { id, arazCityID, miqaatID } = req.body;

  const cacheKey = `${redisCacheKeys.HIERARCHY}:${redisCacheKeys.KG_USER}:${id}:${arazCityID}:${miqaatID}`;

  let data = await getCache(cacheKey,miqaatID, arazCityID);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "KG User", data, res, true);
  }
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const kgUserData = await KGUser.aggregate([
    { $match: { _id: toObjectId(id) } },
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiat",
      },
    },
    { $unwind: { path: "$jamiat", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaat",
      },
    },
    { $unwind: { path: "$jamaat", preserveNullAndEmptyArrays: true } },
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "zone",
      },
    },
    { $unwind: { path: "$zone", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "arazcities",
        localField: "miqaats.arazCityID",
        foreignField: "_id",
        as: "arazCity",
      },
    },
    { $unwind: { path: "$arazCity", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "department",
      },
    },
    { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "functions",
        localField: "miqaats.functionID",
        foreignField: "_id",
        as: "function",
      },
    },
    { $unwind: { path: "$function", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaats.miqaatID",
        foreignField: "_id",
        as: "miqaat",
      },
    },
    { $unwind: { path: "$miqaat", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $unwind: { path: "$hierarchyPosition", preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    { $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaats.kgGroupID",
        foreignField: "_id",
        as: "kgGroup",
      },
    },
    { $unwind: { path: "$kgGroup", preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        "miqaats.miqaat": {
          id: "$miqaat._id",
          name: "$miqaat.name",
        },
        "miqaats.arazCity": {
          id: "$arazCity._id",
          name: "$arazCity.name",
        },
        "miqaats.zone": {
          id: "$zone._id",
          name: "$zone.name",
        },
        "miqaats.department": {
          id: "$department._id",
          name: "$department.name",
        },
        "miqaats.function": {
          id: "$function._id",
          name: "$function.name",
        },
        "miqaats.kgType": {
          id: "$kgType._id",
          name: "$kgType.name",
        },
        "miqaats.kgGroup": {
          id: "$kgGroup._id",
          name: "$kgGroup.name",
        },
        "miqaats.hierarchyPosition": {
          id: "$hierarchyPosition._id",
          name: "$hierarchyPosition.name",
          alias: "$hierarchyPosition.alias",
        },
      },
    },
    {
      $group: {
        _id: "$_id",
        name: { $first: "$name" },
        LDName: { $first: "$LDName" },
        jamiat: { $first: "$jamiat" },
        jamaat: { $first: "$jamaat" },
        logo: { $first: "$logo" },
        phone: { $first: "$phone" },
        whatsapp: { $first: "$whatsapp" },
        ITSID: { $first: "$ITSID" },
        email: { $first: "$email" },
        miqaats: { $push: "$miqaats" },
        gender: { $first: "$gender" },
        maritialStatus: { $first: "$maritialStatus" },
        occupation: { $first: "$occupation" },
        qualification: { $first: "$qualification" },
        organization: { $first: "$organization" },
        prefix: { $first: "$prefix" },
        misaq: { $first: "$misaq" },
        occupation: { $first: "$occupation" },
        qualification: { $first: "$qualification" },
        idara: { $first: "$idara" },
        category: { $first: "$category" },
        organization: { $first: "$organization" },
        address: { $first: "$address" },
        city: { $first: "$city" },
        country: { $first: "$country" },
        nationality: { $first: "$nationality" },
        vatan: { $first: "$vatan" },
      },
    },
    {
      $addFields: {
        foundMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                as: "miqaat",
                cond: {
                  $and: [
                    { $eq: ["$$miqaat.miqaat.id", toObjectId(miqaatID)] },
                    { $eq: ["$$miqaat.arazCity.id", toObjectId(arazCityID)] },
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $project: {
        _id: 1,
        logo: 1,
        phone: 1,
        whatsapp: 1,
        name: 1,
        LDName: 1,
        ITSID: 1,
        email: 1,
        status: 1,
        gender: 1,
        maritialStatus: 1,
        occupation: 1,
        qualification: 1,
        organization: 1,
        prefix: 1,
        misaq: 1,
        occupation: 1,
        qualification: 1,
        idara: 1,
        category: 1,
        organization: 1,
        address: 1,
        city: 1,
        country: 1,
        nationality: 1,
        vatan: 1,
        createdAt: 1,
        updatedAt: 1,
        jamiat: { id: "$jamiat._id", name: "$jamiat.name" },
        jamaat: { id: "$jamaat._id", name: "$jamaat.name" },
        "miqaats.isActive": 1,
        "miqaats.consentRequired": 1,
        "miqaats.consentAccepted": 1,
        "miqaats.isInternationalPlugin": 1,
        "miqaats._id": 1,
        "miqaats.miqaat": 1,
        "miqaats.arazCity": 1,
        "miqaats.kgType": 1,
        "miqaats.kgGroup": 1,
        "miqaats.hierarchyPosition": 1,
        "miqaats.zone": 1,
        "miqaats.department": 1,
        "miqaats.function": 1,
        "miqaats.cityRoleID": 1,
        "miqaats.otherFunction": 1,
        foundMiqaat: 1,
      },
    },
  ]);

  if (isEmpty(kgUserData)) {
    return apiError(NOT_FOUND, "KG User", null, res);
  }

  let userData = kgUserData[0];
  userData.sync = true;

  apiResponse(FETCH, "KG User", userData, res);

  await setCache(cacheKey, userData, miqaatID, arazCityID);
});

const importITSUsersHelper = async (req, res = null) => {
  const { ITS_IDs, id: compileListID } = req.body;
  const uniqueITSIDs = [...new Set(ITS_IDs)];

  // let existingUsers = await KGUser.find({ ITSID: { $in: uniqueITSIDs } })
  //   .populate([
  //     { path: "jamaatID", select: "id name" },
  //     { path: "jamiatID", select: "id name" },
  //     { path: "smeRecommendation.recommendedBy", select: "name" },
  //   ])
  let existingUsers = await KGUser.aggregate(
    getITSUsersAggregation(ITS_IDs, compileListID)
  );

  const existingITSIDs = new Set(existingUsers.map((user) => user.ITSID));
  const missingITSIDs = uniqueITSIDs.filter((id) => !existingITSIDs.has(id));

  let newUsers = [];
  if (missingITSIDs.length) {
    const usersDataResults = await Promise.allSettled(
      missingITSIDs.map((ITSID) => getITSUserData(ITSID))
    );

    usersDataResults.forEach((result, index) => {
      if (result.status === "fulfilled" && result.value) {
        newUsers.push({
          ...result.value,
        });
      }
    });

    if (newUsers.length) {
      await KGUser.insertMany(newUsers);

      // const populatedInsertedUsers = await KGUser.find({
      //   ITSID: { $in: newUsers.map((user) => user.ITSID) },
      // })
      //   .populate("jamaatID", "id name")
      //   .populate("jamiatID", "id name");
      const populatedInsertedUsers = await KGUser.aggregate(
        getITSUsersAggregation(
          newUsers.map((user) => user.ITSID),
          compileListID
        )
      );

      existingUsers.push(...populatedInsertedUsers);
    }
  }

  const foundITSIDs = new Set(existingUsers.map((user) => user.ITSID));
  const notFoundData = missingITSIDs
    .filter((id) => !foundITSIDs.has(id))
    .map((id) => ({
      ITSID: id,
      error: true,
      errorMessage: "ITS User Not Found",
    }));

  let users = [...existingUsers, ...notFoundData];

  const usersMap = new Map(users.map((user) => [user.ITSID, user]));

  users = uniqueITSIDs.map((id) => usersMap.get(id)).filter(Boolean);

  return apiResponse(FETCH, "ITS Users", users, res ? res : null);
};

const importITSUsers = apiHandler(async (req, res) => {
  return importITSUsersHelper(req, res);
});

const assignKGUser = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, existsInDB, notExistsInDB } = req.body;
  let results = { success: [], failed: [] };

  const arazCityData = await ArazCity.findOne({ _id: arazCityID }).select(
    "fasalDate status jamiats jamaats miqaatID"
  );

  if (!arazCityData?.status) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const isFasalDatePassed = await checkFasalDate(arazCityData);

  let bulkPullOps = [];
  let bulkAddOps = [];
  let bulkOps = [];
  const usersDataForHierarchy = [];

  const processUser = async (user, isExisting) => {
    const {
      id,
      name,
      LDName,
      logo,
      email,
      phone,
      whatsapp,
      age,
      gender,
      ITSID,
      hierarchyPositionID,
      hierarchyPositionName,
      kgGroupID,
      kgGroupName,
      kgTypeID,
      kgTypeName,
      kgTypeColor,
      jamiatID,
      jamaatID,
      arazCityZoneID,
      arazCityZoneName,
      departmentID,
      departmentName,
      functionID,
      cityRoleID,
      consentRequired,
      consentAccepted,
      otherFunction,
      isInternationalPlugin,
      ...userData
    } = user;

    const isAuthorized = await isAuthorized(req, user);

    usersDataForHierarchy.push({
      KGType: kgTypeName || null,
      KGTypeID: kgTypeID || null,
      KGTypeColor: kgTypeColor || null,
      KGGroup: kgGroupName || null,
      KGGroupID: kgGroupID || null,
      logo: logo,
      name: name || "",
      LDName: LDName || "",
      ITSID: ITSID || "",
      email: email || "",
      phone: phone || "",
      whatsapp: whatsapp || "",
      age: age || "",
      gender: gender || "",
      KGID: id,
      departmentID: departmentID || null,
      departmentName: departmentName || null,
      arazCityZoneID: arazCityZoneID || null,
      arazCityZoneName: arazCityZoneName || null,
      hierarchyPositionID: hierarchyPositionID || null,
      hierarchyPositionName: hierarchyPositionName || null,
      isInternationalPlugin: isInternationalPlugin || false,
    });

    if (!ITSID) {
      results.failed.push({ ITSID, reason: "Invalid ITSID" });
      return;
    }

    if (!req.user.systemRoleID) {
      if (!isFasalDatePassed) {
        const isJamaatUser = await checkUserJamaat(
          arazCityData,
          jamaatID,
          ITSID
        );

        if (!isJamaatUser) {
          results.failed.push({
            ITSID,
            reason: "User does not belong to Jamaat",
          });
          return;
        }
      }
    }

    let newMiqaat = {
      miqaatID,
      arazCityID,
      kgTypeID: kgTypeID || null,
      kgGroupID: kgGroupID || null,
      hierarchyPositionID: hierarchyPositionID || null,
      arazCityZoneID: arazCityZoneID || null,
      departmentID: departmentID || null,
      functionID: functionID || null,
      consentRequired,
      consentAccepted,
      otherFunction,
      status: consentRequired ? "PENDING" : "ACCEPTED",
      isInternationalPlugin,
      updatedAt: new Date(),
      cityRoleID: cityRoleID || SYSTEM_ROLES.DEFAULT[0],
    };

    if (isExisting) {
      bulkPullOps.push({
        updateOne: {
          filter: { _id: toObjectId(id) },
          update: {
            $pull: {
              miqaats: {
                miqaatID: toObjectId(miqaatID),
                arazCityID: toObjectId(arazCityID),
              },
            },
          },
        },
      });

      bulkAddOps.push({
        updateOne: {
          filter: { _id: toObjectId(id) },
          update: {
            $addToSet: { miqaats: newMiqaat },
            updatedBy: req.user._id,
          },
        },
      });

      results.success.push({ ITSID, status: "Updated" });
    } else {
      bulkOps.push({
        updateOne: {
          filter: { ITSID },
          update: {
            $setOnInsert: {
              ...userData,
              ITSID,
              miqaats: [newMiqaat],
              updatedBy: req.user._id,
            },
          },
          upsert: true,
        },
      });

      results.success.push({ ITSID, status: "Added" });
    }
  };

  await Promise.all([
    ...existsInDB.map((user) => processUser(user, true)),
    ...notExistsInDB.map((user) => processUser(user, false)),
  ]);

  const [pullResult, addResult, upsertResult] = await Promise.all([
    bulkPullOps.length ? KGUser.bulkWrite(bulkPullOps) : null,
    bulkAddOps.length ? KGUser.bulkWrite(bulkAddOps) : null,
    bulkOps.length ? KGUser.bulkWrite(bulkOps) : null,
  ]);

  apiResponse(UPDATE_SUCCESS, "KG User Assignment", results, res);
  await updateHierarchy(
    miqaatID,
    arazCityID,
    usersDataForHierarchy,
    "add-edit"
  );

  // await generateHierarchy(miqaatID, arazCityID);
  await addEventLog(
    EventActions.UPDATE,
    Modules.Hierarchy,
    "Hierarchy for Assign KG User",
    constants.AMS_SYSTEMID
  );
});

const fetchArazCityData = async (arazCityID) => {
  const arazCityData = await ArazCity.findOne({ _id: arazCityID }).select(
    "fasalDate status jamiats jamaats addToOpenProject miqaatID"
  );
  return arazCityData?.status ? arazCityData : null;
};

const validateUser = async (
  loggedInUser,
  user,
  arazCityData,
  isFasalDatePassed,
  results
) => {
  if (!user.ITSID) {
    results.failed.push({ ITSID: user.ITSID, reason: "Invalid ITSID" });
    return false;
  }

  if (!loggedInUser.systemRoleID) {
    // if (!isFasalDatePassed) {
    const isJamaatUser = await checkUserJamaat(
      arazCityData,
      user.jamaatID,
      user.ITSID
    );
    if (!isJamaatUser) {
      results.failed.push({
        ITSID: user.ITSID,
        reason: "User does not belong to Jamiat",
      });
      return false;
    }
    // }
  }

  return true;
};

const createNewMiqaat = (user, miqaatID, arazCityID) => ({
  miqaatID,
  arazCityID,
  kgTypeID: user.kgTypeID || null,
  kgGroupID: user.kgGroupID || null,
  hierarchyPositionID: user.hierarchyPositionID || null,
  arazCityZoneID: user.arazCityZoneID || null,
  departmentID: user.departmentID || null,
  functionID: user.functionID || null,
  consentRequired: user.consentRequired,
  consentAccepted: user.consentAccepted,
  otherFunction: user.otherFunction,
  status: user.consentRequired ? "PENDING" : "ACCEPTED",
  isInternationalPlugin: user.isInternationalPlugin,
  updatedAt: new Date(),
  cityRoleID: user.cityRoleID || SYSTEM_ROLES.DEFAULT[0],
});

const executeBulkOperations = async (bulkOps) => {
  if (bulkOps.pull.length) await KGUser.bulkWrite(bulkOps.pull);
  if (bulkOps.add.length) await KGUser.bulkWrite(bulkOps.add);
  if (bulkOps.upsert.length) await KGUser.bulkWrite(bulkOps.upsert);
};

const addOneSignalDevice = apiHandler(async (req, res) => {
  const {
    deviceID,
    deviceType,
    appVersion,
    appOSVersion,
    webCMSVersion,
    webBrowserType,
  } = req.body;
  const userID = req.user._id;
  const deviceData = deviceMeta.appTypes.includes(deviceType)
    ? {
        appDetails: {
          deviceID,
          deviceType,
          OSVersion: appOSVersion,
          version: appVersion,
        },
      }
    : {
        webDetails: {
          deviceID,
          CMSVersion: webCMSVersion,
          browserType: webBrowserType,
        },
      };

  const updatedUser = await KGUser.findByIdAndUpdate(userID, {
    $set: deviceData,
  });
  if (!updatedUser) {
    return apiError(ADD_ERROR, "Device", null, res);
  }

  return apiResponse(ADD_SUCCESS, "Device", null, res);
});

// const getKGUsersByPagination = apiHandler(async (req, res) => {
//   const { arazCity, miqaat, kgIDs } = req.body;
//   const loggedInUser = req.user;

//   // Extract query parameters
//   const {
//     search = "",
//     page,
//     limit,
//     sortBy = "name",
//     sortOrder = "asc",
//     zone = [],
//     hierarchyPosition = [],
//     department = [],
//     kgGroup = [],
//     kgType = [],
//     function: functionFilter = [],
//     cityRole = [],
//     status = "",
//     isActive = "",
//     gender = "",
//     jamiat = [],
//     jamaat = [],
//     deviceType = [],
//     misaq = "",
//     category = "",
//     consentAccepted = "",
//   } = req.body;

//   const isPaginationProvided =
//     page !== undefined &&
//     page !== null &&
//     limit !== undefined &&
//     limit !== null;

//   const pageNum = isPaginationProvided ? parseInt(page) : 1;
//   const limitNum = isPaginationProvided ? parseInt(limit) : 0; // 0 means no limit
//   const skip = isPaginationProvided ? (pageNum - 1) * limitNum : 0;

//   // Generate cache key
//   const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${
//     redisCacheKeys.KG_LIST
//   }:${arazCity}:${miqaat}:${kgIDs && kgIDs.sort().join(":")}:${JSON.stringify(
//     req.body
//   )}`;

//   // Check cache
//   // let data = await getCache(cachekey);
//   // if (!isEmpty(data)) {
//   //   return apiResponse(FETCH, "KG Users", data, res);
//   // }

//   try {
//     // Build pipeline parameters
//     const pipelineParams = {
//       arazCity,
//       miqaat,
//       kgIDs,
//       search,
//       zone,
//       hierarchyPosition,
//       department,
//       kgGroup,
//       kgType,
//       functionFilter,
//       cityRole,
//       status,
//       isActive,
//       gender,
//       jamiat,
//       jamaat,
//       deviceType,
//       misaq,
//       category,
//       consentAccepted,
//       sortBy,
//       sortOrder,
//       skip,
//       limit: limitNum,
//     };

//     if (!arazCity || !miqaat) {
//       return apiError(
//         CUSTOM_ERROR,
//         "Please provide arazCityID and miqaatID",
//         null,
//         res
//       );
//     }

//     // Build and execute pipeline
//     const pipeline = buildKGUsersPipeline(pipelineParams);
//     // console.log(JSON.stringify(pipeline))
//     const result = await KGUser.aggregate(pipeline);

//     // Extract results
//     const users = isPaginationProvided ? result[0].data : result || [];
//     const totalCount = isPaginationProvided
//       ? result[0].count.length > 0
//         ? result[0].count[0].total
//         : 0
//       : 0;

//     // Handle empty results
//     if (isEmpty(users)) {
//       return apiError(
//         NOT_FOUND,
//         "Active KG Users in Miqaat and City",
//         pipeline,
//         res
//       );
//     }

//     // Apply phone visibility rules for ladies
//     const updatedKGUsers = await applyPhoneVisibilityRules(
//       users,
//       arazCity,
//       req
//     );

//     // Prepare response
//     const responseData = {
//       users: updatedKGUsers,
//       ...(isPaginationProvided && {
//         pagination: {
//           currentPage: pageNum,
//           totalPages: Math.ceil(totalCount / limitNum),
//           totalCount: totalCount,
//           limit: limitNum,
//           hasNextPage: pageNum < Math.ceil(totalCount / limitNum),
//           hasPrevPage: pageNum > 1,
//           showing: `${skip + 1}-${Math.min(
//             skip + limitNum,
//             totalCount
//           )} of ${totalCount}`,
//         },
//       }),
//       filters: {
//         search,
//         zone,
//         hierarchyPosition,
//         department,
//         kgGroup,
//         kgType,
//         function: functionFilter,
//         cityRole,
//         status,
//         isActive,
//         gender,
//         jamiat,
//         jamaat,
//         misaq,
//         category,
//         consentAccepted,
//       },
//       sort: {
//         sortBy,
//         sortOrder,
//       },
//       pipeline,
//     };

//     // Cache and return response
//     await setCache(cachekey, responseData, 300); // Cache for 5 minutes
//     return apiResponse(FETCH, "KG Users", responseData, res);
//   } catch (error) {
//     console.error("Error in getKGUsers:", error);
//     return apiError(CUSTOM_ERROR, "Failed to fetch KG Users", null, res);
//   }
// });

const getKGUsersByPaginationV2 = apiHandler(async (req, res) => {
  const { arazCity, miqaat, kgIDs } = req.body;

  // Extract query parameters
  const {
    search = "",
    page = 1,
    limit = 0,
    sortBy = "name",
    sortOrder = "asc",
    zone = [],
    hierarchyPosition = [],
    department = [],
    kgGroup = [],
    kgType = [],
    function: functionFilter = [],
    cityRole = [],
    status = "",
    isActive = "",
    gender = "",
    jamiat = [],
    jamaat = [],
    deviceType = [],
    misaq = "",
    category = "",
    consentAccepted = "",
    razaStatus = [],
    miqaatZone = "",
  } = req.body;

  // Generate cache key
  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${
    redisCacheKeys.KG_LIST
  }:${arazCity}:${miqaat}:${kgIDs && kgIDs.sort().join(":")}:${JSON.stringify(
    req.body
  )}`;

  // Check cache
  let data = await getCache(cachekey, miqaat, arazCity);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "KG Users", data, res, true);
  }

  try {
    if (!arazCity || !miqaat) {
      return apiError(
        CUSTOM_ERROR,
        "Please provide arazCityID and miqaatID",
        null,
        res
      );
    }

    const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaat, arazCity, req);
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    // Build pipeline parameters
    const pipelineParams = {
      arazCity,
      miqaat,
      kgIDs,
      search,
      zone,
      hierarchyPosition,
      department,
      kgGroup,
      kgType,
      functionFilter,
      cityRole,
      status,
      isActive,
      gender,
      jamiat,
      jamaat,
      deviceType,
      misaq,
      category,
      consentAccepted,
      sortBy,
      sortOrder,
      page,
      limit,
      razaStatus,
      miqaatZone,
    };

    const pipeline = buildKGUsersPipelineV2(pipelineParams);

    // Set aggregation options
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      customLabels: {
        docs: "users",
        totalDocs: "totalCount",
      },
    };

    // Execute aggregation with pagination
    let result = await KGUser.aggregate(pipeline);

    if (isEmpty(result)) {
      return apiError(
        NOT_FOUND,
        "Active KG Users in Miqaat and City",
        pipeline,
        res
      );
    }
    result = result[0];

    // Apply phone visibility rules for ladies
    const updatedKGUsers = await applyPhoneVisibilityRules(
      result.users,
      arazCity,
      req
    );
    result.users = updatedKGUsers;

    // Prepare response
    const responseData = {
      users: result.users,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(result.total[0].total / limit),
        totalCount: result.total[0].total,
        limit: limit,
        hasNextPage: true,
        hasPrevPage: false,
        // showing: `${result.offset + 1}-${Math.min(
        //   result.offset + result.limit,
        //   result.totalCount
        // )} of ${result.totalCount}`,
      },
      filters: {
        search,
        zone,
        hierarchyPosition,
        department,
        kgGroup,
        kgType,
        function: functionFilter,
        cityRole,
        status,
        isActive,
        gender,
        jamiat,
        jamaat,
        misaq,
        category,
        consentAccepted,
      },
      sort: {
        sortBy,
        sortOrder,
      },
      // pipeline,
    };

    // Cache and return response
    await setCache(cachekey, responseData, miqaat, arazCity); 
    return apiResponse(FETCH, "KG Users", responseData, res);
  } catch (error) {
    console.error("Error in getKGUsers:", error);
    return apiError(CUSTOM_ERROR, "Failed to fetch KG Users", null, res);
  }
});

const applyPhoneVisibilityRules = async (users, arazCity, req) => {
  // Apply phone visibility rules
  return users.map((user) => {
    if (user.gender === "M") {
      return user;
    }

    return {
      ...user,
      phone: req.allowPhone ? user.phone : undefined,
      whatsapp: req.allowPhone ? user.whatsapp : undefined,
      logo: req.allowFemalePhoto ? user.logo : undefined,
    };

    return user;
  });
};

const deleteKGUsersBulk = apiHandler(async (req, res) => {
  try {
    const { ITSIDs, arazCityID, miqaatID } = req.body;

    if (!Array.isArray(ITSIDs) || !arazCityID || !miqaatID) {
      return apiError(
        CUSTOM_ERROR,
        "ITSIDs, arazCityID, and miqaatID are required",
        null,
        res
      );
    }

    const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    const loggedInUser = req.user;
    const isSuperAdmin =
      loggedInUser.systemRoleID?.toString() ===
      SYSTEM_ROLES.SUPER_ADMIN[0].toString();

    const kgUsers = await KGUser.find({ ITSID: { $in: ITSIDs } }).select(
      "_id ITSID miqaats hierarchyPositionID arazCityZoneID"
    );

    const updateOps = [];
    const interestOps = [];
    const hierarchyData = [];

    const results = {
      success: [],
      failed: [],
    };

    for (const user of kgUsers) {
      const { ITSID } = user;

      const userMiqaatToDelete = user.miqaats.find(
        (m) =>
          m?.miqaatID?.toString() === miqaatID &&
          m?.arazCityID?.toString() === arazCityID &&
          m?.isActive
      );

      if (!userMiqaatToDelete) {
        results.failed.push({
          ITSID,
          reason: "User not active in this miqaat/city",
        });
        continue;
      }

      // Authorization check
      if (!isSuperAdmin) {
        const userMiqaat = loggedInUser.miqaats.find(
          (m) =>
            m?.miqaatID?.toString() === miqaatID &&
            m?.arazCityID?.toString() === arazCityID &&
            m?.isActive
        );

        if (!userMiqaat) {
          results.failed.push({
            ITSID,
            reason: "You have no access to this miqaat/city",
          });
          continue;
        }

        const isZoneMember = userMiqaat.arazCityZoneID;

        if (isZoneMember) {
          // Logged-in user is a zone member
          const userToDeleteInZone =
            userMiqaatToDelete.arazCityZoneID?.toString() ===
            userMiqaat.arazCityZoneID?.toString();
          const userToDeleteHasZoneRole =
            user.hierarchyPositionID?.toString() ===
            constants.HIERARCHY_POSITIONS.ZONE_TEAM[0].toString();

          if (!userToDeleteHasZoneRole) {
            results.failed.push({
              ITSID,
              reason: "Only Zone Team Members can be deleted by Zone Member",
            });
            continue;
          }

          if (!userToDeleteInZone) {
            results.failed.push({ ITSID, reason: "User is not in your zone" });
            continue;
          }
        }
      }

      updateOps.push({
        updateOne: {
          filter: { _id: user._id },
          update: [
            {
              $set: {
                miqaats: {
                  $map: {
                    input: "$miqaats",
                    as: "miqaat",
                    in: {
                      $cond: [
                        {
                          $and: [
                            {
                              $eq: ["$$miqaat.miqaatID", toObjectId(miqaatID)],
                            },
                            {
                              $eq: [
                                "$$miqaat.arazCityID",
                                toObjectId(arazCityID),
                              ],
                            },
                          ],
                        },
                        {
                          miqaatID: "$$miqaat.miqaatID",
                          arazCityID: "$$miqaat.arazCityID",
                          isActive: true,
                          cityRoleID: toObjectId(
                            constants.SYSTEM_ROLES.NOT_ASSIGNED[0]
                          ),
                          status: "DELETED",
                          updatedAt: new Date(),
                        },
                        "$$miqaat",
                      ],
                    },
                  },
                },
                updatedBy: loggedInUser._id,
              },
            },
          ],
        },
      });

      interestOps.push({
        updateOne: {
          filter: {
            userID: user._id,
            miqaatID: toObjectId(miqaatID),
            arazCityID: toObjectId(arazCityID),
          },
          update: { $set: { status: "not-assigned" } },
          upsert: false,
        },
      });

      hierarchyData.push({ ITSID, miqaatID, arazCityID });

      results.success.push({ ITSID, reason: "Marked as deleted" });
    }

    if (updateOps.length) {
      await KGUser.bulkWrite(updateOps);
      await Interest.bulkWrite(interestOps);
      await updateHierarchy(miqaatID, arazCityID, hierarchyData, "delete");

      await addEventLog(
        EventActions.DELETE,
        Modules.GlobalMasters,
        "KG User Bulk",
        loggedInUser._id
      );

      await addEventLog(
        EventActions.UPDATE,
        Modules.Hierarchy,
        "Hierarchy Update for KG User Bulk",
        constants.AMS_SYSTEMID
      );
    }

    return apiResponse(
      CUSTOM_SUCCESS,
      `${results.success.length} users deleted, ${results.failed.length} failed`,
      { results },
      res
    );
  } catch (error) {
    console.error("deleteKGUsersBulk error:", error);
    return apiError(CUSTOM_ERROR, "Internal server error", null, res);
  }
});

module.exports = {
  getKGUsers,
  getAllKGUsers,
  getSingleKGUser,
  addEditKGUser,
  addEditKGUserFunc,
  deleteKGUser,
  importITSUsers,
  importITSUsersHelper,
  assignKGUser,
  assignKGUserV2,
  uploadKGUser,
  addOneSignalDevice,
  // getKGUsersByPagination,
  getKGUsersByPaginationV2,
  deleteKGUsersBulk,
};
