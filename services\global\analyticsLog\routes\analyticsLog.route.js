const express = require('express');
const { validate } = require('../../../../middlewares/validation.middleware');
const { addAnalyticsLogSchema, getUserAnalyticsSchema } = require('../validations/analyticsLog.validation');
const { addAnalytics, getUserAnalyticsLog } = require('../controllers/analyticsLog.controller');
const router = express.Router();

router.post("/add", validate(addAnalyticsLogSchema, "body"), addAnalytics);
router.post("/get", validate(getUserAnalyticsSchema, "body"), getUserAnalyticsLog);

module.exports = router;
