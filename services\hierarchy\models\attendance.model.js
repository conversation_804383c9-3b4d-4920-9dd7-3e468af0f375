const { Schema, model } = require("mongoose");

const attendanceSchema = new Schema(
  {
    userID: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    latitude: {
      type: String,
      required: false,
      trim: true,
    },
    longitude: {
      type: String,
      required: false,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

const Attendance = model("Attendance", attendanceSchema);

module.exports = { Attendance };

