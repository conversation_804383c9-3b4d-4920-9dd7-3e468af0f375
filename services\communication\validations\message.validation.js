const Joi = require("joi");
const {
  stringValidation,
  idValidation,
  numberValidation,
  arrayStringValidation,
  arrayIdValidation,
  dateValidation,
  booleanValidation,
} = require("../../../utils/validator.util");
const { recipientMeta } = require("../models");

const addEditMessageSchema = Joi.object({
  isEmail: booleanValidation,
  isNotification: booleanValidation,
  messageID: idValidation.optional(),
  awsGroupID: stringValidation.optional(),
  miqaatID: idValidation,
  arazCityID: idValidation,
  subject: stringValidation,
  body: stringValidation,
  // for recipients
  recipientType: stringValidation
    .valid(...recipientMeta.allTypes)
    .optional()
    .allow(null, ""),
  departmentIDs: arrayIdValidation.optional(),
  zoneIDs: arrayIdValidation.optional(),
  positionIDs: arrayIdValidation.optional(),
  kgTypeIDs: arrayIdValidation.optional(),
  kgGroupIDs: arrayIdValidation.optional(),
  ITSIDs: arrayStringValidation.optional(),
  systemRoleID: arrayIdValidation.optional(),
  ///////////////////
  // for CC recipients - completely optional section
  CCrecipientType: stringValidation.valid(...recipientMeta.allTypes).optional(),
  CCsystemRoleID: arrayIdValidation.optional(),
  // Make CC fields optional by default, and only required in specific conditions
  CCdepartmentIDs: arrayIdValidation.optional(),

  CCzoneIDs: arrayIdValidation.optional(),

  CCITSIDs: arrayStringValidation.optional(),

  CCpositionIDs: arrayIdValidation.optional(),
  CCkgTypeIDs: arrayIdValidation.optional(),
  CCkgGroupIDs: arrayIdValidation.optional(),
  ///////////////////
  isAllowReply: booleanValidation.optional(),
  isTopPriority: booleanValidation.optional(),
  messageType: idValidation.optional(),
  scheduledAt: dateValidation.optional(),
  attachments: Joi.array()
    .optional()
    .allow(Joi.array())
    .items(
      Joi.object({
        fileName: stringValidation,
        fileType: stringValidation,
        fileSize: numberValidation.positive(),
        fileKey: stringValidation,
      }).unknown()
    ),
});
const getRecipientsSchema = Joi.object({
  miqaatID: idValidation.required(),
  arazCityID: idValidation.required(),
  recipientType: stringValidation
    .valid(...recipientMeta.allTypes)
    .optional()
    .allow(null, ""),
  zoneIDs: arrayIdValidation.optional(),
  departmentIDs: arrayIdValidation.optional(),
  ITSIDs: arrayStringValidation.optional(),
  positionIDs: arrayIdValidation.optional(),
  kgTypeIDs: arrayIdValidation.optional(),
  kgGroupIDs: arrayIdValidation.optional(),
  systemRoleID: arrayIdValidation.optional(),
});

const getMessagesSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  page: numberValidation.optional(),
  limit: numberValidation.optional(),
  filters: Joi.array()
    .optional()
    .items(
      Joi.object({
        type: stringValidation,
        key: stringValidation.optional().allow(""),
        value: stringValidation.optional().allow(""),
        options: arrayIdValidation.optional(),
      })
    ),
});

const getMessageSchema = Joi.object({
  params: {
    id: idValidation,
  },
  body: {
    miqaatID: idValidation,
    arazCityID: idValidation,
  },
});

const addMessageReplySchema = Joi.object({
  params: {
    id: idValidation,
  },
  body: {
    miqaatID: idValidation,
    arazCityID: idValidation,
    body: stringValidation,
    awsGroupID: stringValidation.optional().allow(""),
    attachments: Joi.array()
      .optional()
      .allow(Joi.array())
      .items(
        Joi.object({
          fileName: stringValidation,
          fileType: stringValidation,
          fileSize: numberValidation.positive(),
          fileKey: stringValidation,
        }).unknown()
      ),
  },
});

const deleteMessageSchema = Joi.object({
  params: {
    id: idValidation,
  },
  body: {
    miqaatID: idValidation,
    arazCityID: idValidation,
  },
});

const getDashboardSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
});

const getEmailReportSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  page: numberValidation.optional(),
  limit: numberValidation.optional(),
  filters: Joi.array()
    .optional()
    .items(
      Joi.object({
        type: stringValidation,
        key: stringValidation.optional().allow(""),
        value: stringValidation.optional().allow(""),
        options: arrayIdValidation.optional(),
      })
    ),
});

const getAttachmentSchema = Joi.object({
  fileKey: stringValidation,
});

module.exports = {
  addEditMessageSchema,
  getMessagesSchema,
  getMessageSchema,
  addMessageReplySchema,
  deleteMessageSchema,
  getDashboardSchema,
  getEmailReportSchema,
  getAttachmentSchema,
  getRecipientsSchema,
};
