const Joi = require("joi");
const {
  stringValidation,
  idValidation,
  emailValidation,
  numberValidation,
  booleanValidation,
} = require("../../../utils/validator.util");

const addEditSystemUserSchema = Joi.object({
  // id: idValidation.optional().allow(""),
  isExists: booleanValidation.optional(),
  name: stringValidation,
  // LDName: stringValidation.optional().allow(""),
  ITSID: stringValidation,
  // email: emailValidation,
  // phone: stringValidation.optional().allow(""),
  // whatsapp: stringValidation.optional().allow(""),
  status: stringValidation,
  jamiatID: idValidation,
  jamaatID: idValidation,
  systemRoleID: idValidation.optional().allow(""),
}).unknown();

const getSingleSystemUserSchema = Joi.object({
  id: idValidation,
});

const deleteSystemUserSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addEditSystemUserSchema,
  getSingleSystemUserSchema,
  deleteSystemUserSchema,
};
