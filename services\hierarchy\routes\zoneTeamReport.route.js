const { validate } = require("../../../middlewares/validation.middleware");
const { getZoneTeamReport } = require("../controllers/zoneTeamReport.controller");
const { getZoneTeamReportSchema } = require("../validations/zoneTeamReportValidation");

const router = require("express").Router();

router.post(
  "/get",
  validate(getZoneTeamReportSchema, "body"),
  getZoneTeamReport
);

module.exports = router;
