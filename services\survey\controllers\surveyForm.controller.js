const {
  SurveyForm,
  SurveyResponse,
  surveyFormFrequencyList,
} = require("../models");

const {
  getCache,
  redisCacheKeys,
  setCache,
  clearCacheByPattern,
} = require("../../../utils/redis.cache");

const {
  apiResponse,
  apiError,
  apiHandler,
} = require("../../../utils/api.util");
const {
  CUSTOM_ERROR,
  FETCH,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  NOT_FOUND,
  ADD_ERROR,
  CUSTOM_SUCCESS,
} = require("../../../utils/message.util");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const { Question } = require("../models/question.model");
const constants = require("../../../constants");
const { KGUser } = require("../../hierarchy/models");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getAllSurveys = async (req, res) => {
  const surveys = await SurveyForm.find()
    .populate("questions createdBy updatedBy department")
    .sort({ createdAt: -1 })
    .lean();
  if (isEmpty(surveys)) {
    return apiResponse(NOT_FOUND, "No surveys found", null, res);
  }
  return apiResponse(FETCH, "Surveys", surveys, res);
};

const getMySurveys = async (req, res) => {
  const surveys = await SurveyForm.find({ createdBy: req.user._id })
    .populate("questions createdBy updatedBy department")
    .sort({ createdAt: -1 })
    .lean();
  if (isEmpty(surveys)) {
    return apiResponse(NOT_FOUND, "No surveys found", null, res);
  }
  return apiResponse(FETCH, "Surveys", surveys, res);
};

const addEditSurvey = apiHandler(async (req, res) => {
  const { id } = req.params;

  await clearCacheByPattern(
    `${redisCacheKeys.HIERARCHY}:${redisCacheKeys.SURVEY}:*`
  );

  try {
    if (id) {
      const updatedSurvey = await SurveyForm.findByIdAndUpdate(
        id,
        { ...req.body, updatedBy: req.user._id },
        {
          new: true,
          runValidators: true,
        }
      );

      if (!updatedSurvey) {
        return apiResponse(NOT_FOUND, "Survey", null, res);
      }

      return apiResponse(UPDATE_SUCCESS, "Survey", updatedSurvey, res);
    } else {
      const newSurvey = new SurveyForm({
        ...req.body,
        createdBy: req.user._id,
      });
      const savedSurvey = await newSurvey.save();

      if (!savedSurvey) {
        return apiResponse(ADD_ERROR, "Survey", null, res);
      }

      return apiResponse(ADD_SUCCESS, "Survey", savedSurvey, res);
    }
  } catch (error) {
    console.error("Error in addEditSurvey:", error);
    return apiResponse(CUSTOM_ERROR, "Survey", null, res);
  }
});

const getSingleSurvey = async (req, res) => {
  const { id } = req.params;
  const survey = await SurveyForm.findById(id).populate([
    {
      path: "createdBy",
      select: "name ITSID _id logo",
    },
    {
      path: "updatedBy",
      select: "name ITSID _id logo",
    },
    {
      path: "questions",
      populate: [
        {
          path: "department",
          select: "name",
        },
      ],
    },
    {
      path: "department",
      select: "name _id",
    },
    // Populate recipients criteria
    {
      path: "recipients.criteria.miqaatID",
      select: "name _id",
    },
    {
      path: "recipients.criteria.arazCityID",
      select: "name _id",
    },
    {
      path: "recipients.criteria.departmentIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.functionIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.zoneIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.positionIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.kgGroupIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.kgTypeIDs",
      select: "name _id",
    },
    {
      path: "recipients.systemRoleIDs",
      select: "name _id",
    },
    {
      path: "recipients.systemUserIDs",
      select: "name _id",
    },
    // Populate report recipients criteria
    {
      path: "reportRecipients.criteria.miqaatID",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.arazCityID",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.departmentIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.functionIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.zoneIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.positionIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.kgGroupIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.kgTypeIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.systemRoleIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.systemUserIDs",
      select: "name _id",
    },
  ]);

  if (!survey) {
    return apiResponse(NOT_FOUND, "Survey", null, res);
  }

  let surveyMembers = [];
  let reportRecipientMembers = [];

  if (survey.recipients) {
    // Process criteria-based recipients
    if (survey.recipients.criteria && survey.recipients.criteria.length > 0) {
      for (const criteria of survey.recipients.criteria) {

        const checkActiveStatus = await isActiveMiqaatAndArazCity(
          criteria.miqaatID?._id,
          criteria.arazCityID?._id,
          req
        );
        if (!checkActiveStatus) {
          continue;
        }

        const memberData = await getMember({
          miqaatID: criteria.miqaatID?._id,
          arazCityID: criteria.arazCityID?._id,
          recipientType: criteria.recipientType,
          departmentIDs: criteria.departmentIDs?.map((dept) => dept._id),
          functionIDs: criteria.functionIDs?.map((func) => func._id),
          zoneIDs: criteria.zoneIDs?.map((zone) => zone._id),
          positionIDs: criteria.positionIDs?.map((pos) => pos._id),
          kgTypeIDs: criteria.kgTypeIDs?.map((type) => type._id),
          kgGroupIDs: criteria.kgGroupIDs?.map((group) => group._id),
          ITSIDs: criteria.ITSIDs,
        });

        if (
          memberData.existingUsersData &&
          memberData.existingUsersData.length > 0
        ) {
          surveyMembers.push(...memberData.existingUsersData);
        }
      }
    }

    // Process system role recipients
    if (
      survey.recipients.systemRoleIDs &&
      survey.recipients.systemRoleIDs.length > 0
    ) {
      const systemRoleMembers = await getMember({
        systemRoles: survey.recipients.systemRoleIDs.map((role) => role._id),
      });

      if (
        systemRoleMembers.existingUsersData &&
        systemRoleMembers.existingUsersData.length > 0
      ) {
        surveyMembers.push(...systemRoleMembers.existingUsersData);
      }
    }
  }

  // Process report recipients
  if (survey.reportRecipients) {
    // Process criteria-based report recipients
    if (
      survey.reportRecipients.criteria &&
      survey.reportRecipients.criteria.length > 0
    ) {
      for (const criteria of survey.reportRecipients.criteria) {

        const checkActiveStatus = await isActiveMiqaatAndArazCity(
          criteria.miqaatID?._id,
          criteria.arazCityID?._id,
          req
        );
        if (!checkActiveStatus) {
          continue;
        }

        const memberData = await getMember({
          miqaatID: criteria.miqaatID?._id,
          arazCityID: criteria.arazCityID?._id,
          recipientType: criteria.recipientType,
          departmentIDs: criteria.departmentIDs?.map((dept) => dept._id),
          functionIDs: criteria.functionIDs?.map((func) => func._id),
          zoneIDs: criteria.zoneIDs?.map((zone) => zone._id),
          positionIDs: criteria.positionIDs?.map((pos) => pos._id),
          kgTypeIDs: criteria.kgTypeIDs?.map((type) => type._id),
          kgGroupIDs: criteria.kgGroupIDs?.map((group) => group._id),
          ITSIDs: criteria.ITSIDs,
        });

        if (
          memberData.existingUsersData &&
          memberData.existingUsersData.length > 0
        ) {
          reportRecipientMembers.push(...memberData.existingUsersData);
        }
      }
    }

    // Process system role report recipients
    if (
      survey.reportRecipients.systemRoleIDs &&
      survey.reportRecipients.systemRoleIDs.length > 0
    ) {
      const systemRoleMembers = await getMember({
        systemRoles: survey.reportRecipients.systemRoleIDs.map(
          (role) => role._id
        ),
      });

      if (
        systemRoleMembers.existingUsersData &&
        systemRoleMembers.existingUsersData.length > 0
      ) {
        reportRecipientMembers.push(...systemRoleMembers.existingUsersData);
      }
    }
  }

  // Remove duplicates based on user ID
  const uniqueSurveyMembers = surveyMembers.filter(
    (member, index, self) =>
      index ===
      self.findIndex((m) => m._id.toString() === member._id.toString())
  );

  const uniqueReportMembers = reportRecipientMembers.filter(
    (member, index, self) =>
      index ===
      self.findIndex((m) => m._id.toString() === member._id.toString())
  );

  // Prepare response with survey data and member information
  const surveyWithMembers = {
    ...survey.toObject(),
    surveyRecipients: uniqueSurveyMembers,
    reportRecipientMembers: uniqueReportMembers,
  };

  return apiResponse(FETCH, "Survey", surveyWithMembers, res);
};

const getSingleSurveyWithoutMembers = async (req, res) => {
  const { id } = req.params;
  const survey = await SurveyForm.findById(id).populate([
    {
      path: "createdBy",
      select: "name ITSID _id logo",
    },
    {
      path: "updatedBy",
      select: "name ITSID _id logo",
    },
    {
      path: "questions",
      populate: [
        {
          path: "department",
          select: "name",
        },
      ],
    },
    {
      path: "department",
      select: "name _id",
    },
    // Populate recipients criteria
    {
      path: "recipients.criteria.miqaatID",
      select: "name _id",
    },
    {
      path: "recipients.criteria.arazCityID",
      select: "name _id",
    },
    {
      path: "recipients.criteria.departmentIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.functionIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.zoneIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.positionIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.kgGroupIDs",
      select: "name _id",
    },
    {
      path: "recipients.criteria.kgTypeIDs",
      select: "name _id",
    },
    {
      path: "recipients.systemRoleIDs",
      select: "name _id",
    },
    {
      path: "recipients.systemUserIDs",
      select: "name _id",
    },
    // Populate report recipients criteria
    {
      path: "reportRecipients.criteria.miqaatID",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.arazCityID",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.departmentIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.functionIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.zoneIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.positionIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.kgGroupIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.criteria.kgTypeIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.systemRoleIDs",
      select: "name _id",
    },
    {
      path: "reportRecipients.systemUserIDs",
      select: "name _id",
    },
  ]);

  if (!survey) {
    return apiResponse(NOT_FOUND, "Survey", null, res);
  }

  return apiResponse(FETCH, "Survey", survey, res);
};

const deleteSurvey = async (req, res) => {
  const { id } = req.params;
  const survey = await SurveyForm.findByIdAndDelete(id);
  if (!survey) {
    return apiResponse(NOT_FOUND, "Survey", null, res);
  }
  apiResponse(DELETE_SUCCESS, "Survey", null, res);
  await SurveyResponse.deleteMany({ surveyID: id });
};

const getQuestions = async (req, res) => {
  const { departmentIDs } = req.body;
  const query = { isActive: true };
  if (departmentIDs && departmentIDs.length > 0) {
    query.departments = { $in: toObjectId(departmentIDs) };
  }
  const questions = await Question.find(query)
    .populate("createdBy", "name ITSID logo")
    .populate("updatedBy", "name ITSID logo")
    .sort({ createdAt: -1 })
    .lean();
  if (isEmpty(questions)) {
    return apiResponse(NOT_FOUND, "No questions found", res);
  }
  return apiResponse(FETCH, "Questions", questions, res);
};

const checkQuestionUsedStatus = async (req, res) => {
   const { id, surveyID } = req.body;
  const query = {
    questions: id,
    isActive: true,
  };
  if (!isEmpty(surveyID)) {
    query._id = { $ne: surveyID };
  }

  try {
    const surveys = await SurveyForm.find(query).lean();

    const validSurveys = surveys.filter((survey) => {
      const mapped = survey.questionMapping || [];
      const isMapped = mapped.some(
        (mapping) => String(mapping.questionID.toString()) === String(id)
      );
      return !isMapped;
    });

    if (validSurveys.length > 0) {
      const question = await Question.findById(id).lean();
      return apiResponse(
        FETCH,
        "Survey",
        {
          status: true,
          activeSurvey: validSurveys[0], // return first one if needed
          question,
        },
        res
      );
    } else {
      return apiResponse(
        FETCH,
        "Survey",
        { status: false, activeSurvey: null },
        res
      );
    }
  } catch (error) {
    return apiResponse(CUSTOM_ERROR, "Survey", error.message, res);
  }
};

const toggleSurveyStatus = async (req, res) => {
  const { id } = req.params;
  const survey = await SurveyForm.findById(id);
  if (!survey) {
    return apiResponse(NOT_FOUND, "Survey", null, res);
  }
  survey.isActive = !survey.isActive;
  const updatedSurvey = await survey.save();
  if (!updatedSurvey) {
    return apiResponse(UPDATE_ERROR, "Survey", null, res);
  }
  return apiResponse(
    CUSTOM_SUCCESS,
    "Survey Status Updated",
    updatedSurvey,
    res
  );
};

const getMember = async (data) => {
  const {
    miqaatID,
    arazCityID,
    recipientType,
    departmentIDs,
    functionIDs,
    zoneIDs,
    positionIDs,
    kgTypeIDs,
    kgGroupIDs,
    ITSIDs,
    systemRoles,
  } = data;

  const cacheKey = `${redisCacheKeys.HIERARCHY}:${
    redisCacheKeys.SURVEY
  }:${JSON.stringify(data)}`;

  const cachedData = await getCache(cacheKey, miqaatID, arazCityID);
  if (cachedData) {
    return cachedData;
  }

  const query = {
    _id: { $ne: toObjectId(constants.AMS_SYSTEMID) },
  };

  const orConditions = [];

  const miqaatCondition = {};

  if (miqaatID && arazCityID) {
    miqaatCondition.miqaats = {
      $elemMatch: {
        miqaatID: toObjectId(miqaatID),
        arazCityID: toObjectId(arazCityID),
      },
    };
  }

  switch (recipientType) {
    case "ALL_IN_HIERARCHY":
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = {
        $ne: null,
      };
      break;

    case "ALL_IN_DEPARTMENT":
      miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
        $in: toObjectId(departmentIDs),
      };
      break;

    case "ALL_IN_ZONE":
      miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
        $in: toObjectId(zoneIDs),
      };
      break;

    case "ALL_DEPARTMENT_HOD":
      miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
        $in: toObjectId(departmentIDs),
      };
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.HOD[0].toString()
      );
      break;

    case "ALL_ZONE_HOD":
      miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
        $in: toObjectId(zoneIDs),
      };
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.ZONE_HEAD[0].toString()
      );
      break;

    case "BY_ITS":
      miqaatCondition["ITSID"] = { $in: ITSIDs };
      break;

    case "CUSTOM":
      if (departmentIDs && departmentIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
          $in: toObjectId(departmentIDs),
        };
      }
      if (zoneIDs && zoneIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
          $in: toObjectId(zoneIDs),
        };
      }
      if (functionIDs && functionIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["functionID"] = {
          $in: toObjectId(functionIDs),
        };
      }
      if (positionIDs && positionIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = {
          $in: toObjectId(positionIDs),
        };
      }
      if (kgTypeIDs && kgTypeIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["kgTypeID"] = {
          $in: toObjectId(kgTypeIDs),
        };
      }
      if (kgGroupIDs && kgGroupIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["kgGroupID"] = {
          $in: toObjectId(kgGroupIDs),
        };
      }
      break;

    case "PMO":
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.PMO[0].toString()
      );
      break;
  }

  if (Object.keys(miqaatCondition).length > 0) {
    orConditions.push(miqaatCondition);
  }

  if (systemRoles && systemRoles.length > 0) {
    orConditions.push({
      systemRoleID: { $in: toObjectId(systemRoles) },
    });
  }

  if (orConditions.length > 1) {
    query.$or = orConditions;
  } else if (orConditions.length === 1) {
    Object.assign(query, orConditions[0]);
  }

  const existingUsers = await KGUser.find(query)
    .select(
      "_id name ITSID miqaats logo phone whatsapp gender LDName systemRoleID"
    )
    .populate({
      path: "miqaats.hierarchyPositionID",
      select: "name alias",
    })
    .populate({
      path: "miqaats.departmentID",
      select: "name",
    })
    .populate({
      path: "miqaats.arazCityZoneID",
      select: "name",
    })
    .populate({
      path: "miqaats.arazCityID",
      select: "name",
    })
    .populate({
      path: "systemRoleID",
      select: "name",
    })
    .lean();

  const userIDs = existingUsers.map((user) => user._id);

  const existingUsersData = existingUsers.map((user) => {
    const matchedMiqaat = user.miqaats.find(
      (miqaat) =>
        miqaat?.miqaatID?.toString() === miqaatID?.toString() &&
        miqaat?.arazCityID?._id?.toString() === arazCityID?.toString()
    );

    return {
      _id: user._id,
      name: user.name,
      LDName: user.LDName,
      ITSID: user.ITSID,
      logo: user.logo,
      phone: user.phone,
      whatsappNumber: user.whatsapp,
      gender: user.gender,
      hierarchyPosition: matchedMiqaat?.hierarchyPositionID?.name || null,
      hierarchyAlias: matchedMiqaat?.hierarchyPositionID?.alias || null,
      department: matchedMiqaat?.departmentID?.name || null,
      zone: matchedMiqaat?.arazCityZoneID?.name || null,
      systemRole: user.systemRoleID?.name || null,
      arazCity: matchedMiqaat?.arazCityID?.name || null,
    };
  });

  await setCache(cacheKey, {
    userIDs,
    existingUsersData,
  }, miqaatID, arazCityID);

  return { userIDs, existingUsersData };
};

module.exports = {
  getAllSurveys,
  getSingleSurvey,
  addEditSurvey,
  deleteSurvey,
  getQuestions,
  getMySurveys,
  toggleSurveyStatus,
  getSingleSurveyWithoutMembers,
  checkQuestionUsedStatus,
  getMember,
};
