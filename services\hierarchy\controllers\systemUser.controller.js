const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON><PERSON>r,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  NOT_FOUND,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  CUSTOM_SUCCESS,
} = require("../../../utils/message.util");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const { KGUser } = require("../models");
const {
  redisCacheKeys,
  getCache,
  setCache,
  clearCacheByPattern,
} = require("../../../utils/redis.cache");
const { getJamiatJammatMasterRecord } = require("../../../utils/ITSHelper.util");

const getAllSystemUsers = apiHandler(async (req, res) => {
  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.SYSTEM_ROLES}:{all}`;
  let data = await getCache(cacheKey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "System Users", data, res, true);
  }

  const systemUsers = await KGUser.find({
    systemRoleID: {
      // $nin: [toObjectId(SYSTEM_ROLES.DEFAULT[0]), toObjectId(SYSTEM_ROLES.SME_USER[0])],
      $exists: true,
    },
  })
    .populate({ path: "systemRoleID", select: "name" })
    .populate({ path: "systemDepartmentID", select: "name" })
    .lean()
    .sort({ _id: -1 });

  filteredSystemUsers = systemUsers?.filter((user) => {
    return !isEmpty(user.systemRoleID);
  });

  if (isEmpty(filteredSystemUsers)) {
    return apiError(NOT_FOUND, "System Users", null, res);
  }

  apiResponse(FETCH, "System Users", filteredSystemUsers, res);

  await setCache(cacheKey, filteredSystemUsers);
});

const addEditSystemUser = apiHandler(async (req, res) => {
  const { id } = req.body;

  let userData = req.body;
  userData['updatedBy'] = req.user._id;

  const updateQuery = { $set: userData };

  if (userData.systemDepartmentID === "") {
    delete updateQuery.$set.systemDepartmentID;
    updateQuery.$unset = { systemDepartmentID: "" };
  }

  if (!isEmpty(id)) {
    const systemUserData = await KGUser.findByIdAndUpdate(
      id,
      updateQuery,
      {
        new: true,
        runValidators: true,
      }
    );

    if (isEmpty(systemUserData)) {
      return apiError(NOT_FOUND, "System User", null, res);
    }

    apiResponse(UPDATE_SUCCESS, "System User", systemUserData, res);
  } else {
    // Remove empty string before creating a new document
    if (userData.systemDepartmentID === "") {
      delete userData.systemDepartmentID;
    }

    const newSystemUser = new KGUser(userData);
    const savedUserData = await newSystemUser.save();

    apiResponse(ADD_SUCCESS, "System User", savedUserData, res);
  }

  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.SYSTEM_ROLES}:*`;
  await clearCacheByPattern(cacheKey);
});

const getSingleSystemUser = apiHandler(async (req, res) => {
  const { id } = req.params;
  const systemUserData = await KGUser.aggregate([
    { $match: { _id: toObjectId(id) } },

    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaatData",
      },
    },
    { $unwind: { path: "$jamaatData", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiatData",
      },
    },
    { $unwind: { path: "$jamiatData", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "systemroles",
        localField: "systemRoleID",
        foreignField: "_id",
        as: "systemRoleData",
      },
    },
    { $unwind: { path: "$systemRoleData", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "departments",
        localField: "systemDepartmentID",
        foreignField: "_id",
        as: "systemDepartmentData",
      },
    },
    { $unwind: { path: "$systemRoleData", preserveNullAndEmptyArrays: true } },
    {
      $project: {
        _id: 1,
        name: 1,
        ITSID: 1,
        LDName: 1,
        email: 1,
        phone: 1,
        whatsapp: 1,
        logo: 1,
        gender: 1,
        maritialStatus: 1,
        occupation: 1,
        qualification: 1,
        organization: 1,
        prefix: 1,
        misaq: 1,
        occupation: 1,
        qualification: 1,
        idara: 1,
        category: 1,
        organization: 1,
        address: 1,
        city: 1,
        country: 1,
        nationality: 1,
        vatan: 1,
        jamaat: { id: "$jamaatData._id", name: "$jamaatData.name" },
        jamiat: { id: "$jamiatData._id", name: "$jamiatData.name" },
        systemRole: { id: "$systemRoleData._id", name: "$systemRoleData.name" },
        systemDepartment: { $arrayElemAt: ["$systemDepartmentData", 0] },
        treatAsCityUser: 1,
      },
    },
  ]);

  if (isEmpty(systemUserData[0])) {
    return apiError(NOT_FOUND, "System User", null, res);
  }

  return apiResponse(FETCH, "System User", systemUserData[0], res);
});

const deleteSystemUser = apiHandler(async (req, res) => {
  const { id } = req.params;
  const systemUserData = await KGUser.findOneAndUpdate(
    { _id: id },
    {
      $unset: { systemRoleID: "", systemDepartmentID: "" },
    },
    { new: true }
  );

  if (!systemUserData) {
    return apiError(NOT_FOUND, "System User", null, res);
  }
  apiResponse(DELETE_SUCCESS, "System User", null, res);

  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.SYSTEM_ROLES}:*`;
  await clearCacheByPattern(cacheKey);
});

const clearAllCache = apiHandler(async (req, res) => {
  await clearCacheByPattern(`${redisCacheKeys.GLOBAL_MASTER}:*`);
  await clearCacheByPattern(`${redisCacheKeys.HIERARCHY}:*`);
  return apiResponse(CUSTOM_SUCCESS, "All Cache Cleared ", null, res);
});

const getJamaiatJamaatMasterRecord = apiHandler(async (req, res) => {
  const data = await getJamiatJammatMasterRecord();
  return apiResponse(FETCH, "Jamiat Jamaat Master Record", data, res);
})

module.exports = {
  getAllSystemUsers,
  addEditSystemUser,
  getSingleSystemUser,
  deleteSystemUser,
  clearAllCache,
  getJamaiatJamaatMasterRecord,
};
