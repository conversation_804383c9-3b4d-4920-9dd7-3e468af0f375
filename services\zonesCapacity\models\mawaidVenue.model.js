const { Schema, model } = require("mongoose");


const measurementSchema = new Schema({
  _id:false,
  value: {
    type: Number,
    required: true,
    default: 0,
  },
  unit: {
    type: String,
    required: true,
    trim: true,
    enum: ["feet", "meter"],
  },
});

const mawaidVenueSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
    },
    status: {
      type: String,
      required: true,
      trim: true,
      enum: ["active", "inactive", "cancelled"],
      default:"active"
    },
    waazVenueID: {
      type: Schema.Types.ObjectId,
      ref: "WaazVenue",
      required: false,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    mawaidVenueTypeID: {
      type: Schema.Types.ObjectId,
      ref: "MawaidVenueType",
      required: true,
    },
    mawaidVenueSuitabilityID: {
      type: Schema.Types.ObjectId,
      ref: "MawaidVenueSuitability",
      required: true,
    },
    mawaidVenueArea: {
      length: measurementSchema,
      breadth: measurementSchema,
    },
    supportingKitchenArea: {
      length: measurementSchema,
      breadth: measurementSchema,
    },
    distanceFromKitchen: {
      type: Number,
      required: false,
    },
    plannedThoks: {
      type: Number,
      required: false,
    },
    numberOfThoks: {
      type: Number,
      required: false,
    },
    estimatedCapacity: {
      type: Number,
      required: false,
    },
    finalCapacity: {
      type: Number,
      required: false,
    },
    kitchenAvailability: {
      type: String,
      required: false,
    },
    whichFloor: {
      type: Number,
      required: false,
    },
    negativeArea: {
      type: Number,
      required: false,
    },
    kitchenCapacity: {
      type: Number,
      required: false,
    },
    cookingCapacity: {
      type: Number,
      required: false,
    },
    mainCookingArea: {
      length: measurementSchema,
      breadth: measurementSchema,
    },
    remarks: {
      type: String,
      required: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const MawaidVenue = model("MawaidVenue", mawaidVenueSchema);

module.exports = { MawaidVenue };