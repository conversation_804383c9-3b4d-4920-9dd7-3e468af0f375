const { validate } = require("../../../middlewares/validation.middleware");

const {
  getAllJamiats,
  getAllJamaats,
} = require("../controllers/jamiatJamaat.controller");

const {
  getAllJamaatsSchema,
} = require("../validations/jamiatJamaat.validation");

const router = require("express").Router();

router.get("/get", getAllJamiats);

router.post("/get", validate(getAllJamaatsSchema, "body"), getAllJamaats);

module.exports = router;
