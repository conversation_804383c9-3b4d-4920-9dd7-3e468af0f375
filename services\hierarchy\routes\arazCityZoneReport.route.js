const { validate } = require("../../../middlewares/validation.middleware");

const {
  getArazCityZoneReport,
  getZoneWisHODReport,
} = require("../controllers/arazCityZone.controller");

const {
  getArazCityZoneReportSchema,
  getHODReportSchema,
} = require("../validations/arazCityZone.validation");

const router = require("express").Router();

router.post(
  "/get",
  validate(getArazCityZoneReportSchema, "body"),
  getArazCityZoneReport
);

router.post(
  "/get/department-wise-zonelead-report",
  validate(getHODReportSchema, "body"),
  getZoneWisHODReport
);

module.exports = router;
