const constants = require("../../../constants");
const { apiHandler, apiResponse } = require("../../../utils/api.util");
const { getITSUserData } = require("../../../utils/ITSHelper.util");
const { CUSTOM_SUCCESS, FETCH, DELETE_SUCCESS } = require("../../../utils/message.util");
const { toObjectId } = require("../../../utils/misc.util");
const { KGUser, Department } = require("../../hierarchy/models");

const getSmeMappingUsers = apiHandler(async (req, res) => {
  const departments = await Department
    .find()
    .select("name LDName smeUsers")
    .populate({ path: "smeUsers", select: "name LDName ITSID logo phone" })
  
  return apiResponse(FETCH, "Depertments", departments, res)
})

const addSmeMappingUser = apiHandler(async (req, res) => {
  const { departmentID, ITSIDs } = req.body

  const kgUserIDs = []
  for(const ITSID of ITSIDs) {
    const kgUser = await KGUser.findOne({ ITSID })
    if(kgUser) {
      kgUserIDs.push(kgUser._id)
    } else {
      const ITSUserData = await getITSUserData(ITSID)
      ITSUserData.createdBy = req.user._id
      const newUser = await KGUser.create(ITSUserData)
      kgUserIDs.push(newUser._id)
    }
  }

  await KGUser.updateMany(
    { _id: { $in: toObjectId(kgUserIDs) } },
    { $set: { systemRoleID: constants.SYSTEM_ROLES.SME_USER[0], systemDepartmentID: departmentID } }
  )

  await Department.findByIdAndUpdate(
    departmentID,
    { $addToSet: { smeUsers: { $each: toObjectId(kgUserIDs) } } },
  )

  return apiResponse(CUSTOM_SUCCESS, "Khidmat Guzaar added successfully", null, res)
})

const deleteSmeUser = apiHandler(async (req, res) => {
  const { departmentID, kgUserID } = req.body

  const department = await Department.findByIdAndUpdate(
    departmentID,
    { $pull: { smeUsers: toObjectId(kgUserID) } }
  )
  const smeUser = await KGUser.findByIdAndUpdate(
    kgUserID,
    { $unset: { systemRoleID: "", systemDepartmentID: "" } }
  )

  return apiResponse(DELETE_SUCCESS, "SME User", null, res)
})

module.exports = {
  getSmeMappingUsers,
  addSmeMappingUser,
  deleteSmeUser
}