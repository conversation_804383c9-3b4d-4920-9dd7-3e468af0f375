const { Schema, model } = require("mongoose");

const jamiatSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    ITSID: {
      type: String,
      required: false,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

const jamaatSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    ITSID: {
      type: String,
      required: false,
      trim: true,
    },
    ITSJamiatID: {
      type: String,
      required: false,
      trim: true,
    },
    jamiatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamiat",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

const Jamaat = model("Jamaat", jamaatSchema);
const Jamiat = model("Jamiat", jamiatSchema);

module.exports = { Jamaat, Jamiat };
