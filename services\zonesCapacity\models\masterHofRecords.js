const { Schema, model } = require("mongoose");

const hofSchema = new Schema(
  {
    jamiatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamiat",
      required: true,
    },
    jamaatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamaat",
      required: true,
    },
    HOF_ITS_ID: {
      type: String,
      required: true,
      trim: true,
    },
    totalCount: {
      type: Number,
      required: true,
    },
    mardoCount: {
      type: Number,
      required: true,
    },
    bairoCount: {
      type: Number,
      required: true,
    },
    gairBalighDikraCount: {
      type: Number,
      required: true,
    },
    gairBalighDikriCount: {
      type: Number,
      required: true,
    },
    sector: {
      type: String,
      trim: true,
    },
    subSector: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

const MasterHofRecords = model("MasterHofRecords", hofSchema);

module.exports = { MasterHofRecords };

