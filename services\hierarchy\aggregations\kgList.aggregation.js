
// const { toObjectId } = require("../../../utils/misc.util");

// const buildKGUsersPipelineV2 = ({
//   arazCity,
//   miqaat,
//   kgIDs,
//   search = "",
//   zone = [],
//   hierarchyPosition = [],
//   department = [],
//   kgGroup = [],
//   kgType = [],
//   functionFilter = [],
//   cityRole = [],
//   status = [],
//   isActive = "",
//   gender = [],
//   jamaat = [],
//   jamiat = [],
//   deviceType = [],
//   misaq = "",
//   category = "",
//   consentAccepted = "",
//   sortBy = "updatedAt",
//   sortOrder = "asc",
//   page,
//   limit,
//   razaStatus = [],
//   miqaatZone,
// }) => {
//   const pipeline = [];

//   if (search.trim()) {
//     // Remove extra spaces and normalize the search string
//     let cleanedSearch = search.trim().replace(/\s+/g, " ");

//     // Handle gender search conversion
//     if (cleanedSearch.toLowerCase() === "male") {
//       cleanedSearch = "M";
//     } else if (cleanedSearch.toLowerCase() === "female") {
//       cleanedSearch = "F";
//     }
//     function escapeRegExp(string) {
//       return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
//     }
//     const words = cleanedSearch.split(" ");
//     const regexes = words.map((word) => new RegExp(escapeRegExp(word), "i"));

//     // Helper to create AND regex match for each field
//     const matchAllWordsInField = (field) => ({
//       $and: regexes.map((regex) => ({ [field]: regex })),
//     });

//     pipeline.push({
//       $match: {
//         $or: [
//           matchAllWordsInField("name"),
//           matchAllWordsInField("ITSID"),
//           matchAllWordsInField("email"),
//           matchAllWordsInField("phone"),
//         ],
//       },
//     });
//   }

//   // 1) Optional early filter on _id
//   if (Array.isArray(kgIDs) && kgIDs.length) {
//     pipeline.push({ $match: { _id: { $in: toObjectId(kgIDs) } } });
//   }

//   // 2) Combined match filters
//   const matchConditions = {};

//   if (misaq.trim()) matchConditions.misaq = misaq;
//   if (category.trim()) matchConditions.category = category;
//   if (consentAccepted) {
//     matchConditions.consentAccepted = consentAccepted === "Accepted";
//   }

//   if (Array.isArray(gender) && gender.length) {
//     matchConditions.gender = {
//       $in: gender.map((g) => (g.toLowerCase() === "male" ? "M" : "F")),
//     };
//   }

//   if (Array.isArray(jamaat) && jamaat.length) {
//     matchConditions.jamaatID = { $in: toObjectId(jamaat) };
//   }

//   if (Array.isArray(jamiat) && jamiat.length) {
//     matchConditions.jamiatID = { $in: toObjectId(jamiat) };
//   }

//   if (Object.keys(matchConditions).length) {
//     pipeline.push({ $match: matchConditions });
//   }

//   // 3) Indexed match on miqaats embedded array
//   pipeline.push({
//     $match: {
//       miqaats: {
//         $elemMatch: {
//           miqaatID: toObjectId(miqaat),
//           arazCityID: toObjectId(arazCity),
//           hierarchyPositionID: { $ne: null },
//           status: { $ne: "DECLINED" },
//           isActive: true,
//         },
//       },
//     },
//   });

//   // 4) Extract single matching miqaat
//   pipeline.push({
//     $addFields: {
//       filteredMiqaat: {
//         $first: {
//           $filter: {
//             input: "$miqaats",
//             as: "m",
//             cond: {
//               $and: [
//                 { $eq: ["$$m.miqaatID", toObjectId(miqaat)] },
//                 { $eq: ["$$m.arazCityID", toObjectId(arazCity)] },
//                 { $ne: ["$$m.hierarchyPositionID", null] },
//                 { $ne: ["$$m.status", "DECLINED"] },
//                 { $eq: ["$$m.isActive", true] },
//               ],
//             },
//           },
//         },
//       },
//       deviceType: {
//         $cond: [
//           { $gt: [{ $type: "$appDetails.deviceType" }, "missing"] },
//           "$appDetails.deviceType",
//           "not-installed",
//         ],
//       },
//     },
//   });

//   // 5) Apply filters on filteredMiqaat
//   if (Array.isArray(status) && status.length) {
//     pipeline.push({ $match: { "filteredMiqaat.status": status[0] } });
//   }
//   if (isActive !== "" && isActive != null) {
//     pipeline.push({
//       $match: {
//         "filteredMiqaat.isActive": isActive === "true" || isActive === true,
//       },
//     });
//   }
//   if (Array.isArray(zone) && zone.length) {
//     pipeline.push({
//       $match: { "filteredMiqaat.arazCityZoneID": { $in: toObjectId(zone) } },
//     });
//   }
//   if (Array.isArray(hierarchyPosition) && hierarchyPosition.length) {
//     pipeline.push({
//       $match: {
//         "filteredMiqaat.hierarchyPositionID": {
//           $in: toObjectId(hierarchyPosition),
//         },
//       },
//     });
//   }
//   if (Array.isArray(razaStatus) && razaStatus.length) {
//     pipeline.push({
//       $match: {
//         "filteredMiqaat.miqaatHR.RazaStatus": {
//           $in: razaStatus,
//         },
//       },
//     });
//   }
//   if (Array.isArray(department) && department.length) {
//     pipeline.push({
//       $match: {
//         "filteredMiqaat.departmentID": { $in: toObjectId(department) },
//       },
//     });
//   }
//   if (Array.isArray(kgGroup) && kgGroup.length) {
//     pipeline.push({
//       $match: { "filteredMiqaat.kgGroupID": { $in: toObjectId(kgGroup) } },
//     });
//   }
//   if (Array.isArray(kgType) && kgType.length) {
//     pipeline.push({
//       $match: { "filteredMiqaat.kgTypeID": { $in: toObjectId(kgType) } },
//     });
//   }
//   if (Array.isArray(functionFilter) && functionFilter.length) {
//     pipeline.push({
//       $match: {
//         "filteredMiqaat.functionID": { $in: toObjectId(functionFilter) },
//       },
//     });
//   }
//   if (Array.isArray(cityRole) && cityRole.length) {
//     pipeline.push({
//       $match: { "filteredMiqaat.cityRoleID": { $in: toObjectId(cityRole) } },
//     });
//   }

//   // 6) MiqaatZone filter (can be done before lookups as it's on filteredMiqaat)
//   if (miqaatZone.trim()) {
//     const regex = new RegExp(miqaatZone, "i");
//     pipeline.push({
//       $match: {
//         "filteredMiqaat.miqaatHR.MiqaatZone": regex,
//       },
//     });
//   }

//   if (Array.isArray(deviceType) && deviceType.length) {
//     pipeline.push({
//       $match: {
//         deviceType: { $in: deviceType },
//       },
//     });
//   }

//   // 7) Perform all the lookups first
//   pipeline.push(
//     {
//       $lookup: {
//         from: "jamiats",
//         localField: "jamiatID",
//         foreignField: "_id",
//         as: "jamiats",
//       },
//     },
//     {
//       $lookup: {
//         from: "jamaats",
//         localField: "jamaatID",
//         foreignField: "_id",
//         as: "jamaats",
//       },
//     },
//     {
//       $lookup: {
//         from: "hierarchypositions",
//         localField: "filteredMiqaat.hierarchyPositionID",
//         foreignField: "_id",
//         as: "filteredMiqaat.HierarchyPosition",
//       },
//     },
//     {
//       $lookup: {
//         from: "departments",
//         localField: "filteredMiqaat.departmentID",
//         foreignField: "_id",
//         as: "filteredMiqaat.Department",
//       },
//     },
//     {
//       $lookup: {
//         from: "kggroups",
//         localField: "filteredMiqaat.kgGroupID",
//         foreignField: "_id",
//         as: "filteredMiqaat.KGGroup",
//       },
//     },
//     {
//       $lookup: {
//         from: "kgtypes",
//         localField: "filteredMiqaat.kgTypeID",
//         foreignField: "_id",
//         as: "filteredMiqaat.KGType",
//       },
//     },
//     {
//       $lookup: {
//         from: "arazcityzones",
//         localField: "filteredMiqaat.arazCityZoneID",
//         foreignField: "_id",
//         as: "filteredMiqaat.arazCityzone",
//       },
//     },
//     {
//       $lookup: {
//         from: "kgusers",
//         localField: "createdBy",
//         foreignField: "_id",
//         as: "addedBy",
//       },
//     },
//     {
//       $lookup: {
//         from: "kgusers",
//         localField: "updatedBy",
//         foreignField: "_id",
//         as: "updatedBy",
//       },
//     },
//     {
//       $lookup: {
//         from: "functions",
//         localField: "filteredMiqaat.functionID",
//         foreignField: "_id",
//         as: "functionDetails",
//       },
//     }
//   );

//   // 9) Use $facet for count and paginated results
//   pipeline.push({
//     $facet: {
//       total: [{ $count: "total" }],
//       users: [
//         // Additional lookups needed for the final projection
//         {
//           $lookup: {
//             from: "systemroles",
//             localField: "filteredMiqaat.cityRoleID",
//             foreignField: "_id",
//             as: "cityRoleDetails",
//           },
//         },
//         { $sort: getSortStage(sortBy, sortOrder) },
//         ...(limit
//           ? [
//               {
//                 $skip: limit * (page - 1),
//               },
//               {
//                 $limit: limit,
//               },
//             ]
//           : []),
//         {
//           $project: {
//             _id: 1,
//             ITSID: 1,
//             name: 1,
//             LDName: 1,
//             logo: 1,
//             age: 1,
//             email: 1,
//             gender: 1,
//             phone: 1,
//             whatsapp: 1,
//             systemRoleID: 1,
//             deviceType: 1,
//             appDetails: 1,
//             status: "$filteredMiqaat.status",
//             isActive: "$filteredMiqaat.isActive",
//             isInternationalPlugin: "$filteredMiqaat.isInternationalPlugin",
//             declineReason: "$filteredMiqaat.declineReason",
//             otherFunction: "$filteredMiqaat.otherFunction",
//             razaStatus: {
//               $cond: {
//                 if: {
//                   $and: [
//                     {
//                       $ifNull: ["$filteredMiqaat.miqaatHR", false],
//                     },
//                   ],
//                 },
//                 then: "$filteredMiqaat.miqaatHR",
//                 else: null,
//               },
//             },
//             jamiat: {
//               id: {
//                 $arrayElemAt: ["$jamiats._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$jamiats.name", 0],
//               },
//             },
//             jamaat: {
//               id: {
//                 $arrayElemAt: ["$jamaats._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$jamaats.name", 0],
//               },
//             },
//             hierarchyPosition: {
//               id: {
//                 $arrayElemAt: ["$filteredMiqaat.HierarchyPosition._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$filteredMiqaat.HierarchyPosition.name", 0],
//               },
//               alias: {
//                 $arrayElemAt: ["$filteredMiqaat.HierarchyPosition.alias", 0],
//               },
//             },
//             kgType: {
//               id: {
//                 $arrayElemAt: ["$filteredMiqaat.KGType._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$filteredMiqaat.KGType.name", 0],
//               },
//               color: {
//                 $arrayElemAt: ["$filteredMiqaat.KGType.color", 0],
//               },
//               priority: {
//                 $arrayElemAt: ["$filteredMiqaat.KGType.priority", 0],
//               },
//             },
//             kgGroup: {
//               id: {
//                 $arrayElemAt: ["$filteredMiqaat.KGGroup._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$filteredMiqaat.KGGroup.name", 0],
//               },
//             },
//             department: {
//               id: {
//                 $arrayElemAt: ["$filteredMiqaat.Department._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$filteredMiqaat.Department.name", 0],
//               },
//               priority: {
//                 $arrayElemAt: ["$filteredMiqaat.Department.priority", 0],
//               },
//             },
//             function: {
//               id: {
//                 $arrayElemAt: ["$functionDetails._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$functionDetails.name", 0],
//               },
//               priority: {
//                 $arrayElemAt: ["$functionDetails.priority", 0],
//               },
//             },
//             arazCityZone: {
//               id: {
//                 $arrayElemAt: ["$filteredMiqaat.arazCityzone._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$filteredMiqaat.arazCityzone.name", 0],
//               },
//               priority: {
//                 $arrayElemAt: ["$filteredMiqaat.arazCityzone.priority", 0],
//               },
//             },
//             cityRole: {
//               id: {
//                 $arrayElemAt: ["$cityRoleDetails._id", 0],
//               },
//               name: {
//                 $arrayElemAt: ["$cityRoleDetails.name", 0],
//               },
//               uniqueName: {
//                 $arrayElemAt: ["$cityRoleDetails.uniqueName", 0],
//               },
//             },
//             createdBy: {
//               $arrayElemAt: ["$addedBy.name", 0],
//             },
//             updatedBy: {
//               $arrayElemAt: ["$updatedBy.name", 0],
//             },
//             createdAt: 1,
//             updatedAt: 1,
//           },
//         },
//       ],
//     },
//   });

//   return pipeline;
// };

// // Helper function for sorting fields
// const getSortStage = (sortBy, sortOrder) => {
//   const sortDirection = sortOrder === "desc" ? -1 : 1;

//   const sortFields = {
//     name: { name: sortDirection },
//     ITSID: { ITSID: sortDirection },
//     email: { email: sortDirection },
//     phone: { phone: sortDirection },
//     age: { age: sortDirection },
//     qualification: { qualification: sortDirection },
//     occupation: { occupation: sortDirection },
//     jamiat: { "jamiats.name": sortDirection },
//     jamaat: { "jamaats.name": sortDirection },
//     hierarchyPosition: {
//       "filteredMiqaat.HierarchyPosition.name": sortDirection,
//     },
//     department: { "filteredMiqaat.Department.name": sortDirection },
//     kgType: {
//       "filteredMiqaat.KGType.priority": sortDirection,
//       "filteredMiqaat.KGType.name": sortDirection,
//     },
//     kgGroup: { "filteredMiqaat.KGGroup.name": sortDirection },
//     status: { status: sortDirection },
//     createdAt: { createdAt: sortDirection },
//     updatedAt: { updatedAt: sortDirection },
//     createdBy: { "addedBy.name": sortDirection },
//     updatedBy: { "updatedBy.name": sortDirection },
//     gender: { gender: sortDirection },
//     deviceType: { deviceType: sortDirection },
//     zone: { "filteredMiqaat.arazCityzone.name": sortDirection },
//   };

//   return sortFields[sortBy] || { _id: -1 };
// };

// module.exports = {
//   // buildKGUsersPipeline,
//   buildKGUsersPipelineV2,
// };

const { toObjectId } = require("../../../utils/misc.util");

const buildKGUsersPipelineV2 = ({
  arazCity,
  miqaat,
  kgIDs,
  search = "",
  zone = [],
  hierarchyPosition = [],
  department = [],
  kgGroup = [],
  kgType = [],
  functionFilter = [],
  cityRole = [],
  status = [],
  isActive = "",
  gender = [],
  jamaat = [],
  jamiat = [],
  deviceType = [],
  misaq = "",
  category = "",
  consentAccepted = "",
  sortBy = "updatedAt",
  sortOrder = "asc",
  page,
  limit,
  razaStatus = [],
  miqaatZone,
}) => {
  const pipeline = [];

  // 1) Build initial match conditions - combine all indexed filters first
  const initialMatchConditions = {
    // Core miqaat filter (should be indexed)
    miqaats: {
      $elemMatch: {
        miqaatID: toObjectId(miqaat),
        arazCityID: toObjectId(arazCity),
        hierarchyPositionID: { $ne: null },
        status: { $ne: "DECLINED" },
        isActive: true,
      },
    },
  };

  // Add other indexed filters to initial match
  if (Array.isArray(kgIDs) && kgIDs.length) {
    initialMatchConditions._id = { $in: toObjectId(kgIDs) };
  }

  if (misaq.trim()) initialMatchConditions.misaq = misaq;
  if (category.trim()) initialMatchConditions.category = category;
  if (consentAccepted) {
    initialMatchConditions.consentAccepted = consentAccepted === "Accepted";
  }

  if (Array.isArray(gender) && gender.length) {
    initialMatchConditions.gender = {
      $in: gender.map((g) => (g.toLowerCase() === "male" ? "M" : "F")),
    };
  }

  if (Array.isArray(jamaat) && jamaat.length) {
    initialMatchConditions.jamaatID = { $in: toObjectId(jamaat) };
  }

  if (Array.isArray(jamiat) && jamiat.length) {
    initialMatchConditions.jamiatID = { $in: toObjectId(jamiat) };
  }

  pipeline.push({ $match: initialMatchConditions });

  // 2) Handle search after initial filtering to reduce dataset
  if (search.trim()) {
    let cleanedSearch = search.trim().replace(/\s+/g, " ");

    // Handle gender search conversion
    if (cleanedSearch.toLowerCase() === "male") {
      cleanedSearch = "M";
    } else if (cleanedSearch.toLowerCase() === "female") {
      cleanedSearch = "F";
    }

    function escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    }

    const words = cleanedSearch.split(" ");
    const regexes = words.map((word) => new RegExp(escapeRegExp(word), "i"));

    const matchAllWordsInField = (field) => ({
      $and: regexes.map((regex) => ({ [field]: regex })),
    });

    pipeline.push({
      $match: {
        $or: [
          matchAllWordsInField("name"),
          matchAllWordsInField("ITSID"),
          matchAllWordsInField("email"),
          matchAllWordsInField("phone"),
        ],
      },
    });
  }

  // 3) Extract filtered miqaat and deviceType early
  pipeline.push({
    $addFields: {
      filteredMiqaat: {
        $first: {
          $filter: {
            input: "$miqaats",
            as: "m",
            cond: {
              $and: [
                { $eq: ["$$m.miqaatID", toObjectId(miqaat)] },
                { $eq: ["$$m.arazCityID", toObjectId(arazCity)] },
                { $ne: ["$$m.hierarchyPositionID", null] },
                { $ne: ["$$m.status", "DECLINED"] },
                { $eq: ["$$m.isActive", true] },
              ],
            },
          },
        },
      },
      deviceType: {
        $cond: [
          { $gt: [{ $type: "$appDetails.deviceType" }, "missing"] },
          "$appDetails.deviceType",
          "not-installed",
        ],
      },
    },
  });

  // 4) Apply filteredMiqaat filters - group related filters for efficiency
  const miqaatFilters = [];

  if (Array.isArray(status) && status.length) {
    miqaatFilters.push({ "filteredMiqaat.status": status[0] });
  }

  if (isActive !== "" && isActive != null) {
    miqaatFilters.push({
      "filteredMiqaat.isActive": isActive === "true" || isActive === true,
    });
  }

  if (Array.isArray(zone) && zone.length) {
    miqaatFilters.push({
      "filteredMiqaat.arazCityZoneID": { $in: toObjectId(zone) },
    });
  }

  if (Array.isArray(hierarchyPosition) && hierarchyPosition.length) {
    miqaatFilters.push({
      "filteredMiqaat.hierarchyPositionID": {
        $in: toObjectId(hierarchyPosition),
      },
    });
  }

  if (Array.isArray(department) && department.length) {
    miqaatFilters.push({
      "filteredMiqaat.departmentID": { $in: toObjectId(department) },
    });
  }

  if (Array.isArray(kgGroup) && kgGroup.length) {
    miqaatFilters.push({
      "filteredMiqaat.kgGroupID": { $in: toObjectId(kgGroup) },
    });
  }

  if (Array.isArray(kgType) && kgType.length) {
    miqaatFilters.push({
      "filteredMiqaat.kgTypeID": { $in: toObjectId(kgType) },
    });
  }

  if (Array.isArray(functionFilter) && functionFilter.length) {
    miqaatFilters.push({
      "filteredMiqaat.functionID": { $in: toObjectId(functionFilter) },
    });
  }

  if (Array.isArray(cityRole) && cityRole.length) {
    miqaatFilters.push({
      "filteredMiqaat.cityRoleID": { $in: toObjectId(cityRole) },
    });
  }

  if (Array.isArray(razaStatus) && razaStatus.length) {
    const shouldIncludeNoRaza = razaStatus.includes("No Raza");
    miqaatFilters.push({
      $or: [
        { "filteredMiqaat.miqaatHR.RazaStatus": { $in: razaStatus } },
        ...(shouldIncludeNoRaza ? [{ "filteredMiqaat.miqaatHR": { $exists: false } }] : [])
      ],
    });
  }

  if (miqaatZone && miqaatZone.trim()) {
    const regex = new RegExp(miqaatZone.trim(), "i");
    miqaatFilters.push({
      "filteredMiqaat.miqaatHR.MiqaatZone": regex,
    });
  }

  if (Array.isArray(deviceType) && deviceType.length) {
    miqaatFilters.push({
      deviceType: { $in: deviceType },
    });
  }

  // Apply all miqaat filters in a single match stage
  if (miqaatFilters.length > 0) {
    pipeline.push({
      $match: {
        $and: miqaatFilters,
      },
    });
  }

  // 5) Optimize lookups - only lookup what's needed for sorting first
  const sortLookups = getSortLookups(sortBy);
  if (sortLookups.length > 0) {
    pipeline.push(...sortLookups);
  }

  // 6) Use $facet for count and paginated results
  pipeline.push({
    $facet: {
      total: [{ $count: "total" }],
      users: [
        // Sort early to benefit from potential indexes
        { $sort: getSortStage(sortBy, sortOrder) },
        
        // Apply pagination
        ...(limit
          ? [
              { $skip: limit * (page - 1) },
              { $limit: limit },
            ]
          : []),

        // Perform remaining lookups only on paginated results
        ...getRemainingLookups(sortBy),

        // Final projection
        {
          $project: {
            _id: 1,
            ITSID: 1,
            name: 1,
            LDName: 1,
            logo: 1,
            age: 1,
            email: 1,
            gender: 1,
            phone: 1,
            whatsapp: 1,
            systemRoleID: 1,
            deviceType: 1,
            appDetails: 1,
            status: "$filteredMiqaat.status",
            isActive: "$filteredMiqaat.isActive",
            isInternationalPlugin: "$filteredMiqaat.isInternationalPlugin",
            declineReason: "$filteredMiqaat.declineReason",
            otherFunction: "$filteredMiqaat.otherFunction",
            razaStatus: {
              $cond: {
                if: { $ifNull: ["$filteredMiqaat.miqaatHR", false] },
                then: "$filteredMiqaat.miqaatHR",
                else: null,
              },
            },
            jamiat: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$jamiats", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$jamiats._id", 0] },
                  name: { $arrayElemAt: ["$jamiats.name", 0] },
                },
                else: null,
              },
            },
            jamaat: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$jamaats", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$jamaats._id", 0] },
                  name: { $arrayElemAt: ["$jamaats.name", 0] },
                },
                else: null,
              },
            },
            hierarchyPosition: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$filteredMiqaat.HierarchyPosition", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$filteredMiqaat.HierarchyPosition._id", 0] },
                  name: { $arrayElemAt: ["$filteredMiqaat.HierarchyPosition.name", 0] },
                  alias: { $arrayElemAt: ["$filteredMiqaat.HierarchyPosition.alias", 0] },
                },
                else: null,
              },
            },
            kgType: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$filteredMiqaat.KGType", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$filteredMiqaat.KGType._id", 0] },
                  name: { $arrayElemAt: ["$filteredMiqaat.KGType.name", 0] },
                  color: { $arrayElemAt: ["$filteredMiqaat.KGType.color", 0] },
                  priority: { $arrayElemAt: ["$filteredMiqaat.KGType.priority", 0] },
                },
                else: null,
              },
            },
            kgGroup: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$filteredMiqaat.KGGroup", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$filteredMiqaat.KGGroup._id", 0] },
                  name: { $arrayElemAt: ["$filteredMiqaat.KGGroup.name", 0] },
                },
                else: null,
              },
            },
            department: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$filteredMiqaat.Department", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$filteredMiqaat.Department._id", 0] },
                  name: { $arrayElemAt: ["$filteredMiqaat.Department.name", 0] },
                  priority: { $arrayElemAt: ["$filteredMiqaat.Department.priority", 0] },
                },
                else: null,
              },
            },
            function: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$functionDetails", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$functionDetails._id", 0] },
                  name: { $arrayElemAt: ["$functionDetails.name", 0] },
                  priority: { $arrayElemAt: ["$functionDetails.priority", 0] },
                },
                else: null,
              },
            },
            arazCityZone: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$filteredMiqaat.arazCityzone", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$filteredMiqaat.arazCityzone._id", 0] },
                  name: { $arrayElemAt: ["$filteredMiqaat.arazCityzone.name", 0] },
                  priority: { $arrayElemAt: ["$filteredMiqaat.arazCityzone.priority", 0] },
                },
                else: null,
              },
            },
            cityRole: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$cityRoleDetails", []] } }, 0] },
                then: {
                  id: { $arrayElemAt: ["$cityRoleDetails._id", 0] },
                  name: { $arrayElemAt: ["$cityRoleDetails.name", 0] },
                  uniqueName: { $arrayElemAt: ["$cityRoleDetails.uniqueName", 0] },
                },
                else: null,
              },
            },
            createdBy: { $arrayElemAt: ["$addedBy.name", 0] },
            updatedBy: { $arrayElemAt: ["$updatedBy.name", 0] },
            createdAt: 1,
            updatedAt: 1,
          },
        },
      ],
    },
  });

  return pipeline;
};

const getSortLookups = (sortBy) => {
  const lookups = [];
  
  switch (sortBy) {
    case "jamiat":
      lookups.push({
        $lookup: {
          from: "jamiats",
          localField: "jamiatID",
          foreignField: "_id",
          as: "jamiats",
        },
      });
      break;
    case "jamaat":
      lookups.push({
        $lookup: {
          from: "jamaats",
          localField: "jamaatID",
          foreignField: "_id",
          as: "jamaats",
        },
      });
      break;
    case "hierarchyPosition":
      lookups.push({
        $lookup: {
          from: "hierarchypositions",
          localField: "filteredMiqaat.hierarchyPositionID",
          foreignField: "_id",
          as: "filteredMiqaat.HierarchyPosition",
        },
      });
      break;
    case "department":
      lookups.push({
        $lookup: {
          from: "departments",
          localField: "filteredMiqaat.departmentID",
          foreignField: "_id",
          as: "filteredMiqaat.Department",
        },
      });
      break;
    case "kgType":
      lookups.push({
        $lookup: {
          from: "kgtypes",
          localField: "filteredMiqaat.kgTypeID",
          foreignField: "_id",
          as: "filteredMiqaat.KGType",
        },
      });
      break;
    case "kgGroup":
      lookups.push({
        $lookup: {
          from: "kggroups",
          localField: "filteredMiqaat.kgGroupID",
          foreignField: "_id",
          as: "filteredMiqaat.KGGroup",
        },
      });
      break;
    case "zone":
      lookups.push({
        $lookup: {
          from: "arazcityzones",
          localField: "filteredMiqaat.arazCityZoneID",
          foreignField: "_id",
          as: "filteredMiqaat.arazCityzone",
        },
      });
      break;
    case "createdBy":
      lookups.push({
        $lookup: {
          from: "kgusers",
          localField: "createdBy",
          foreignField: "_id",
          as: "addedBy",
        },
      });
      break;
    case "updatedBy":
      lookups.push({
        $lookup: {
          from: "kgusers",
          localField: "updatedBy",
          foreignField: "_id",
          as: "updatedBy",
        },
      });
      break;
  }
  
  return lookups;
};

const getRemainingLookups = (sortBy) => {
  const allLookups = [
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiats",
      },
    },
    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaats",
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "filteredMiqaat.hierarchyPositionID",
        foreignField: "_id",
        as: "filteredMiqaat.HierarchyPosition",
      },
    },
    {
      $lookup: {
        from: "departments",
        localField: "filteredMiqaat.departmentID",
        foreignField: "_id",
        as: "filteredMiqaat.Department",
      },
    },
    {
      $lookup: {
        from: "kggroups",
        localField: "filteredMiqaat.kgGroupID",
        foreignField: "_id",
        as: "filteredMiqaat.KGGroup",
      },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "filteredMiqaat.kgTypeID",
        foreignField: "_id",
        as: "filteredMiqaat.KGType",
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "filteredMiqaat.arazCityZoneID",
        foreignField: "_id",
        as: "filteredMiqaat.arazCityzone",
      },
    },
    {
      $lookup: {
        from: "kgusers",
        localField: "createdBy",
        foreignField: "_id",
        as: "addedBy",
      },
    },
    {
      $lookup: {
        from: "kgusers",
        localField: "updatedBy",
        foreignField: "_id",
        as: "updatedBy",
      },
    },
    {
      $lookup: {
        from: "functions",
        localField: "filteredMiqaat.functionID",
        foreignField: "_id",
        as: "functionDetails",
      },
    },
    {
      $lookup: {
        from: "systemroles",
        localField: "filteredMiqaat.cityRoleID",
        foreignField: "_id",
        as: "cityRoleDetails",
      },
    },
  ];

  // Return all lookups except the ones already used for sorting
  const sortLookupFields = {
    jamiat: "jamiats",
    jamaat: "jamaats", 
    hierarchyPosition: "filteredMiqaat.HierarchyPosition",
    department: "filteredMiqaat.Department",
    kgType: "filteredMiqaat.KGType",
    kgGroup: "filteredMiqaat.KGGroup",
    zone: "filteredMiqaat.arazCityzone",
    createdBy: "addedBy",
    updatedBy: "updatedBy",
  };

  const usedLookupField = sortLookupFields[sortBy];
  if (!usedLookupField) {
    return allLookups;
  }

  // Filter out the lookup that was already performed for sorting
  return allLookups.filter(lookup => {
    const as = lookup.$lookup.as;
    return as !== usedLookupField;
  });
};

const getSortStage = (sortBy, sortOrder) => {
  const sortDirection = sortOrder === "desc" ? -1 : 1;

  const sortFields = {
    name: { name: sortDirection },
    ITSID: { ITSID: sortDirection },
    email: { email: sortDirection },
    phone: { phone: sortDirection },
    age: { age: sortDirection },
    qualification: { qualification: sortDirection },
    occupation: { occupation: sortDirection },
    jamiat: { "jamiats.name": sortDirection },
    jamaat: { "jamaats.name": sortDirection },
    hierarchyPosition: { "filteredMiqaat.HierarchyPosition.name": sortDirection },
    department: { "filteredMiqaat.Department.name": sortDirection },
    kgType: {
      "filteredMiqaat.KGType.priority": sortDirection,
      "filteredMiqaat.KGType.name": sortDirection,
    },
    kgGroup: { "filteredMiqaat.KGGroup.name": sortDirection },
    status: { "filteredMiqaat.status": sortDirection },
    createdAt: { createdAt: sortDirection },
    updatedAt: { updatedAt: sortDirection },
    createdBy: { "addedBy.name": sortDirection },
    updatedBy: { "updatedBy.name": sortDirection },
    gender: { gender: sortDirection },
    deviceType: { deviceType: sortDirection },
    zone: { "filteredMiqaat.arazCityzone.name": sortDirection },
  };

  return sortFields[sortBy] || { _id: -1 };
};

module.exports = {
  buildKGUsersPipelineV2,
};



