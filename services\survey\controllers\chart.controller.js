const createStructuredQuestions = (surveyData) => {
  const questionMap = new Map();

  const cleanHTML = (htmlString) =>
    htmlString?.replace(/<[^>]*>/g, "").trim() || "";

  for (const user of surveyData.users) {
    const { answers = [] } = user.response;

    for (const answerItem of answers) {
      const { question, answers: responseAnswers, hasAnswer } = answerItem;

      if (!hasAnswer || !question || question.questionType === "textarea")
        continue;

      const questionID = question._id.toString();
      const questionType = question.questionType.toLowerCase();

      if (!questionMap.has(questionID)) {
        questionMap.set(questionID, {
          questiondetails: {
            ...question,
            title: cleanHTML(question.title),
          },
          users: [],
          options: ["checkbox", "multiselect", "select", "radio"].includes(
            questionType
          )
            ? question.options.map((opt) => ({
                optiondetails: {
                  ...opt,
                  placeholder: cleanHTML(opt.placeholder),
                },
                count: 0,
              }))
            : [],
        });
      }

      const qRef = questionMap.get(questionID);

      if (["number", "date", "time"].includes(questionType)) {
        for (const ans of responseAnswers) {
          const placeholder = cleanHTML(ans.placeholder);
          const value = ans.ans;
          if (!value) continue;

          qRef.users.push({ placeholder, value });
        }
      } else if (
        ["checkbox", "multiselect", "select", "radio"].includes(questionType)
      ) {
        for (const ans of responseAnswers) {
          const cleanAns = cleanHTML(ans.ans);
          const cleanPlaceholder = cleanHTML(ans.placeholder);

          const match = qRef.options.find((opt) => {
            const optText = cleanHTML(opt.optiondetails.placeholder);
            return optText === cleanAns || optText === cleanPlaceholder;
          });

          if (match) {
            match.count += 1;
          }
        }
      }
    }
  }

  return { questions: Array.from(questionMap.values()) };
};

const generateChartData = (surveyData) => {
  const structuredQuestions = createStructuredQuestions(surveyData);
  const chartDataArray = [];

  const cleanHTML = (htmlString) =>
    htmlString?.replace(/<[^>]*>/g, "").trim() || "";

  structuredQuestions.questions.forEach((question) => {
    const { questiondetails, options, users } = question;
    const { questionType, title } = questiondetails;
    const cleanTitle = cleanHTML(title);
    const type = questionType.toLowerCase();

    if (["number", "date", "time"].includes(type)) {
      const placeholderMap = {};

      for (const entry of users) {
        const placeholder = entry.placeholder || "Value";

        let processedValue = entry.value;
        if (type === "number") {
          processedValue = parseFloat(entry.value);
        } else if (type === "date") {
          processedValue = new Date(entry.value).toLocaleDateString();
        } else if (type === "time") {
          processedValue = new Date(entry.value).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          });
        }

        if (!placeholderMap[placeholder]) placeholderMap[placeholder] = {};
        placeholderMap[placeholder][processedValue] =
          (placeholderMap[placeholder][processedValue] || 0) + 1;
      }

      Object.entries(placeholderMap).forEach(([placeholder, valueCounts]) => {
        const data = Object.entries(valueCounts).map(([val, count]) => ({
          value: val,
          count,
        }));

        chartDataArray.push({
          type: "linear",
          question: cleanTitle,
          placeholder,
          data,
        });
      });
    } else if (["checkbox", "multiselect", "select", "radio"].includes(type)) {
      const data = options.map((opt) => ({
        label: cleanHTML(opt.optiondetails.placeholder),
        count: opt.count,
      }));

      const chartType = questiondetails.chartType?.toLowerCase() || "bar"; 

      chartDataArray.push({
        type: chartType,
        question: cleanTitle,
        data,
      });
    }
  });

  return chartDataArray;
};

module.exports = { generateChartData, createStructuredQuestions };
