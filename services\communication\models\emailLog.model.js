const { Schema, model } = require("mongoose");

const EmailLogSchema = new Schema(
  {
    emailID: {
      type: Schema.Types.ObjectId,
      ref: "Email",
      required: true,
    },
    smtpMessageID: {
      type: String,
      required: true,
    },
    recipientEmail: {
      type: String,
      required: true,
    },
    recipientUserID: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    isCCUser: {
      type: Boolean,
      required: false,
    },
    status: {
      type: String,
      required: true,
    },
  },
  { timstamps: true }
);

const EmailLog = model("EmailLog", EmailLogSchema);

module.exports = {
  EmailLog,
};
