const Joi = require("joi");
const { stringValidation, idValidation, arrayStringValidation, arrayIdValidation } = require("../../../utils/validator.util");

const getUsersFromITSSchema = Joi.object({
  ITS_IDs: Joi.array().items(stringValidation.optional())
});

const addRecommendedUsersSchema = Joi.object({
  users: Joi.array().items(Joi.object({
    logo: stringValidation,
    ITSID: stringValidation,
    name: stringValidation,
    jamiatID: idValidation,
    jamaatID: idValidation,
    departmentID: idValidation,
    functionID: idValidation.optional().allow(""),
    khidmatZone: stringValidation.optional().allow(""),
    otherFunction: stringValidation.optional().allow(""),
    travelPriority: idValidation.optional().allow(""),
    razaRecommendation: idValidation.optional().allow(""),
    travelCities: arrayIdValidation.optional().allow(""),
    recommendedBy: idValidation.optional().allow(""),
    remarks: stringValidation.optional().allow("")
  }))
})

const editRecommendedUserSchema = Joi.object({
  id: idValidation,
  logo: stringValidation,
  ITSID: stringValidation,
  name: stringValidation,
  jamiatID: idValidation,
  jamaatID: idValidation,
  departmentID: idValidation,
  functionID: idValidation.optional().allow(""),
  khidmatZone: stringValidation.optional().allow(""),
  otherFunction: stringValidation.optional().allow(""),
  travelPriority: idValidation.optional().allow(""),
  razaRecommendation: idValidation.optional().allow(""),
  travelCities: arrayIdValidation.optional().allow(""),
  recommendedBy: idValidation.optional().allow(""),
  remarks: stringValidation.optional().allow("")
})

const deleteRecommendedUserSchema = Joi.object({
  id: idValidation
})

module.exports = {
  getUsersFromITSSchema,
  addRecommendedUsersSchema,
  editRecommendedUserSchema,
  deleteRecommendedUserSchema
}