const router = require("express").Router();

const {
  getDashboardAnalytics,
  getDashboardAliasName,
} = require("../controllers/hierarchy.controller");
const { validate } = require("../../../middlewares/validation.middleware");
const {
  getDashboardAnalyticsSchema,
  getDashboardAliasNameSchema,
} = require("../validations/hierarchy.validation");

router.post(
  "/get/analytics",
  validate(getDashboardAnalyticsSchema, "body"),
  getDashboardAnalytics
);

router.post(
  "/get/alias-name",
  validate(getDashboardAliasNameSchema, "body"),
  getDashboardAliasName
);

module.exports = router;
