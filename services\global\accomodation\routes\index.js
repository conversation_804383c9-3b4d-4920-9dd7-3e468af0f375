const express = require("express");
const router = express.Router();
const hotelRoute = require("./hotel.route");
const zoneMapping = require("../../../zonesCapacity/routes/zoneMapping.route");
const { getArazCityReport } = require("../../../globalMasters/controllers/arazCity.controller");
const { getArazCityReportSchema } = require("../../../globalMasters/validations/arazCity.validation");
const { validate } = require("../../../../middlewares/validation.middleware");


router.use("/hotel", hotelRoute);
router.use("/zone-mapping", zoneMapping);
router.post(
    "/araz-city-report/get",
    validate(getArazCityReportSchema, "body"),
    getArazCityReport
  );

module.exports = router;
