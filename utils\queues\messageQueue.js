const { Queue, Worker } = require("bullmq");
const { connection } = require('./redis');
const { Message, messageTypes } = require('../../services/communication/models');
const { statusTypes } = require("../../services/communication/models/message.model");
const { sendPushNotifications, notificationTemplate } = require("../oneSignal.util");
const { toString, sanitizeHtml } = require("../misc.util");

// Create BullMQ Queue
const queueName = "messagesQueue";
const messageQueue = new Queue(queueName, {
  connection,
  defaultJobOptions: { 
    removeOnComplete: true,
    removeOnFail: true
  },
});

/**
 * Schedule a message for future processing
 * @param {string} messageID - ID of the message in MongoDB
 * @param {number} delay - Delay in milliseconds before the job runs
 */
const scheduleMessage = async (messageID, delay) => {
  // console.log(`Schedule created for message: ${messageID} at: ${new Date(Date.now() + delay).toLocaleString()}`);
  await messageQueue.add(
    "sendMessage",
    { messageID },
    {
      delay, // Execute at the scheduled time
      attempts: 3, // Retry up to 3 times if it fails
      backoff: { type: "exponential", delay: 5000 }, // Retry delay increases
      jobId: messageID, // Prevent duplicate jobs
    }
  );
}

/**
 * Updates a scheduled job by removing the old job and adding a new one.
 * @param {string} messageID - Unique ID of the message.
 * @param {Date} newScheduleTime - New time when the message should be published.
 */
const updateScheduledMessage = async (messageID, newScheduleTime) => {
  const job = await messageQueue.getJob(messageID); // Find existing job by ID

  if (job) {
    await job.remove(); // Remove old job if it exists
    // console.log(`Old job removed: ${messageID}`);
  }

  // Add new job with the updated schedule
  await scheduleMessage(messageID, newScheduleTime)

  // console.log(`New job scheduled for: ${new Date(Date.now() + newScheduleTime).toLocaleString()}`);
}

/**
 * Deletes a scheduled job from the queue.
 * @param {string} messageID - Unique ID of the message.
 */
const deleteScheduledMessage = async (messageID) => {
  const job = await messageQueue.getJob(messageID);

  if (job) {
    await job.remove();
    // console.log(`Job deleted: ${messageID}`);
  }
}

/**
 * BullMQ Worker: Processes scheduled messages
 */
const worker = new Worker(
  queueName,
  async (job) => {
    try {
      // console.log(`Processing job for message: ${job.data.messageID}`);

      // Fetch message from MongoDB
      const message = await Message.findById(job.data.messageID);
      if (!message || message.status === statusTypes.PUBLISHED) return;

      // Update message status to PUBLISHED
      message.status = statusTypes.PUBLISHED;
      await message.save();

      // Send push notification via OneSignal
      const notificationLogData = {
        messageID: message._id,
        replyID: null,
        messageType: messageTypes.MESSAGE,
        recipients: toString(message.recipients)
      }
      const notificationTemplateData = {
        messageID: message._id,
        title: message.subject,
        message: sanitizeHtml(message.body),
        miqaatID: message.miqaatID,
        arazCityID: message.arazCityID,
      }
      await sendPushNotifications(notificationLogData, notificationTemplate.MESSAGE(notificationTemplateData))

      // console.log(`Message ${message._id} published and notification sent.`);
    } catch (error) {
      console.log("error in job", job.id, error)
    }
  },
  { connection }
);

module.exports = { 
  scheduleMessage,
  updateScheduledMessage,
  deleteScheduledMessage
};
