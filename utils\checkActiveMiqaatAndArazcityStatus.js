const { SYSTEM_ROLES } = require("../constants");
const { ArazCity, Miqaat } = require("../services/hierarchy/models");
const { isEmpty } = require("./misc.util");
const { redisCacheKeys, getCache, setCache } = require("./redis.cache");

const isActiveMiqaatAndArazCity = async (miqaatID, arazCityID, req) => {
  let isSuperAdmin =
    req?.user?.systemRoleID?.toString() ===
    SYSTEM_ROLES.SUPER_ADMIN[0].toString();

  if (isSuperAdmin) return true;

  const hasMiqaat = !!miqaatID;
  const hasArazCity = !!arazCityID;

  if (!hasMiqaat && !hasArazCity) return false;

  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.MIQAAT}:${redisCacheKeys.ARAZ_CITIES}:${miqaatID}:${arazCityID}:${hasMiqaat}:${hasArazCity}`;
  let data = await getCache(cachekey, miqaatID, arazCityID);
  if (!isEmpty(data)) {
    return data.status;
  }

  if (hasMiqaat && hasArazCity) {
    const [miqaat, arazCity] = await Promise.all([
      Miqaat.findOne({ _id: miqaatID, status: "active" }),
      ArazCity.findOne({ _id: arazCityID, status: true }),
    ]);
    await setCache(cachekey, {status: !!miqaat && !!arazCity} , miqaatID, arazCityID);
    return !!miqaat && !!arazCity;
  }

  if (hasMiqaat) {
    const miqaat = await Miqaat.findOne({ _id: miqaatID, status: "active" });
    await setCache(cachekey, {status: !!miqaat} , miqaatID, arazCityID);
    return !!miqaat;
  }

  if (hasArazCity) {
    const arazCity = await ArazCity.findOne({ _id: arazCityID, status: true });
    await setCache(cachekey, {status: !!arazCity} , miqaatID, arazCityID);
    return !!arazCity;
  }
  
  await setCache(cachekey, {status: false} , miqaatID, arazCityID);

  return false;
};

module.exports = { isActiveMiqaatAndArazCity };
