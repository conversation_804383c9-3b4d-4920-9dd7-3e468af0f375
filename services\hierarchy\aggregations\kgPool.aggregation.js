const { LADIES_PHONE_VALDIATION } = require("../../../constants");
const { toObjectId } = require("../../../utils/misc.util");

const kgPoolAggregation = (req) => {
  const loggedInUser = req.user;
  const {
    search = "",
    page,
    limit,
    sortBy = "updatedAt",
    sortOrder = "asc",
    priority = "",
    department = "",
    jamaat = "",
    status = "",
    qualification = "",
    occupation = "",
    gender = [],
    deviceType = [],
    arazCityZoneID = [],
    razaStatus = [],
    miqaatZone = "",
  } = req.body;

  // Validate and sanitize sortBy
  const validatedSortBy =
    sortBy && typeof sortBy === "string" && sortBy.trim() !== ""
      ? sortBy.trim()
      : "updatedAt";

  const { arazCityID, miqaatID } = req.params;

  const isPaginationProvided =
    page !== undefined &&
    page !== null &&
    limit !== undefined &&
    limit !== null;

  const pageNum = isPaginationProvided ? parseInt(page) : 1;
  const limitNum = isPaginationProvided ? parseInt(limit) : 0;
  const skip = isPaginationProvided ? (pageNum - 1) * limitNum : 0;

  // Convert gender array filter
  let genderFilter = null;
  if (gender && Array.isArray(gender) && gender.length > 0) {
    genderFilter = gender
      .map((g) =>
        g.toLowerCase() === "male"
          ? "M"
          : g.toLowerCase() === "female"
          ? "F"
          : null
      )
      .filter((g) => g !== null);

    if (genderFilter.length === 0) {
      genderFilter = null;
    }
  }

  // Define sorting strategy
  const lateOnlySortFields = [
    "department",
    "hierarchyPosition",
    "jamaat",
    "jamiat",
    "priority",
    "zone",
  ];

  const needsLateSorting = lateOnlySortFields.includes(validatedSortBy);
  const needsEarlySorting = !needsLateSorting;

  const pipeline = [
    // **STAGE 1: OPTIMIZED EARLY FILTERING**
    {
      $match: {
        // Primary filter - use $elemMatch for consistent matching
        miqaats: {
          $elemMatch: {
            arazCityID: toObjectId(arazCityID),
            miqaatID: toObjectId(miqaatID),
            // Move miqaat-level filters inside $elemMatch
            ...(razaStatus &&
              razaStatus.length > 0 && {
                "miqaatHR.RazaStatus": { $in: razaStatus },
              }),
            ...(miqaatZone && {
              "miqaatHR.MiqaatZone": {
                $regex: miqaatZone,
                $options: "i",
              },
            }),
          },
        },

        // **OPTIMIZATION 2: Apply all possible filters early**
        ...(genderFilter && { gender: { $in: genderFilter } }),
        ...(jamaat && { jamaatID: toObjectId(jamaat) }),
        ...(qualification &&
          {
            // qualification: { $regex: qualification, $options: "i" },
          }),
        ...(occupation &&
          {
            // occupation: { $regex: occupation, $options: "i" },
          }),

        // **OPTIMIZATION 3: Simplified search - only on indexed fields**
        ...(search &&
          (() => {
            // Normalize and split the search string
            let cleanedSearch = search.trim().replace(/\s+/g, " ");

            if (cleanedSearch.toLowerCase() === "male") {
              cleanedSearch = "M";
            } else if (cleanedSearch.toLowerCase() === "female") {
              cleanedSearch = "F";
            }
            function escapeRegExp(string) {
              return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
            }
            const words = cleanedSearch.split(" ");
            const regexes = words.map(
              (word) => new RegExp(escapeRegExp(word), "i")
            );

            // Helper to match all words in a single field
            const matchAllWordsInField = (field) => ({
              $and: regexes.map((regex) => ({ [field]: regex })),
            });

            return {
              $or: [
                matchAllWordsInField("name"),
                matchAllWordsInField("ITSID"),
                matchAllWordsInField("email"),
                matchAllWordsInField("phone"),
              ],
            };
          })()),
      },
    },

    // **STAGE 2: ADD RELEVANT MIQAAT AND DEVICE TYPE**
    {
      $project: {
        _id: 1,
        ITSID: 1,
        name: 1,
        gender: 1,
        phone: 1,
        whatsapp:1,
        logo: 1,
        qualification: 1,
        occupation: 1,
        jamaatID: 1,
        jamiatID: 1,
        createdAt: 1,
        updatedAt: 1,
        appDetails: 1,
        // **OPTIMIZATION 3: Project only relevant miqaat upfront**
        relevantMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                cond: {
                  $and: [
                    { $eq: ["$$this.miqaatID", toObjectId(miqaatID)] },
                    { $eq: ["$$this.arazCityID", toObjectId(arazCityID)] },
                  ],
                },
              },
            },
            0,
          ],
        },
        deviceType: {
          $cond: {
            if: { $gt: [{ $type: "$appDetails.deviceType" }, "missing"] },
            then: "$appDetails.deviceType",
            else: "not-installed",
          },
        },
      },
    },

    // **STAGE 3: EARLY STATUS FILTER - OPTIMIZED**
    ...(status
      ? [
          {
            $addFields: {
              status: {
                $cond: {
                  if: {
                    $and: [
                      { $ne: ["$relevantMiqaat", null] },
                      {
                        $ne: [
                          {
                            $ifNull: [
                              "$relevantMiqaat.hierarchyPositionID",
                              null,
                            ],
                          },
                          null,
                        ],
                      },
                    ],
                  },
                  then: "assigned",
                  else: "not-assigned",
                },
              },
            },
          },
          {
            $match: { status: status },
          },
        ]
      : []),

    // **STAGE 4: DEVICE TYPE FILTER**
    ...(deviceType?.length
      ? [
          {
            $match: {
              deviceType: { $in: deviceType },
            },
          },
        ]
      : []),

    // **STAGE 5: OPTIMIZED INTERESTS LOOKUP - DIRECT FOREIGN KEY MATCH**
    ...(arazCityZoneID?.length || department || priority
      ? [
          // Step 1: Direct lookup using indexed userID field
          {
            $lookup: {
              from: "interests",
              localField: "_id",
              foreignField: "userID",
              as: "allInterests",
            },
          },
          // Step 2: Filter the matched interests for specific arazCityID and miqaatID
          {
            $addFields: {
              interestData: {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: "$allInterests",
                      cond: {
                        $and: [
                          {
                            $eq: ["$$this.arazCityID", toObjectId(arazCityID)],
                          },
                          { $eq: ["$$this.miqaatID", toObjectId(miqaatID)] },
                        ],
                      },
                    },
                  },
                  0,
                ],
              },
            },
          },
          // Step 3: Remove the temporary allInterests array to save memory
          {
            $project: {
              allInterests: 0,
            },
          },
        ]
      : []),

    // **STAGE 6: ZONE FILTERING - SIMPLIFIED**
    ...(arazCityZoneID?.length
      ? [
          {
            $match: {
              $or: [
                {
                  "relevantMiqaat.arazCityZoneID": {
                    $in: arazCityZoneID.map((id) => toObjectId(id)),
                  },
                },
                {
                  "interestData.arazCityZoneID": {
                    $in: arazCityZoneID.map((id) => toObjectId(id)),
                  },
                },
              ],
            },
          },
        ]
      : []),

    // **STAGE 7: DEPARTMENT/PRIORITY FILTERING - OPTIMIZED**
    ...(department || priority
      ? [
          {
            $lookup: {
              from: "departments",
              localField: "interestData.interestOne.departmentID",
              foreignField: "_id",
              as: "dept1",
            },
          },
          {
            $lookup: {
              from: "departments",
              localField: "interestData.interestTwo.departmentID",
              foreignField: "_id",
              as: "dept2",
            },
          },
          {
            $lookup: {
              from: "departments",
              localField: "interestData.interestThree.departmentID",
              foreignField: "_id",
              as: "dept3",
            },
          },
          {
            $match: (() => {
              if (priority && department) {
                const priorityField =
                  priority === "1"
                    ? "dept1"
                    : priority === "2"
                    ? "dept2"
                    : "dept3";
                return { [`${priorityField}._id`]: toObjectId(department) };
              }
              if (department && !priority) {
                return {
                  $or: [
                    { "dept1._id": toObjectId(department) },
                    { "dept2._id": toObjectId(department) },
                    { "dept3._id": toObjectId(department) },
                  ],
                };
              }
              if (priority && !department) {
                const priorityField =
                  priority === "1"
                    ? "dept1"
                    : priority === "2"
                    ? "dept2"
                    : "dept3";
                return { [priorityField]: { $ne: [] } };
              }
              return {};
            })(),
          },
        ]
      : []),

    // **STAGE 8: OPTIMIZED FACET WITH PARALLEL PROCESSING**
    {
      $facet: {
        // Count pipeline - minimal processing
        count: [{ $count: "total" }],

        // Data pipeline - optimized for FE requirements
        data: [
          // **OPTIMIZATION 4: Early sorting for direct fields**
          ...(needsEarlySorting
            ? [
                {
                  $sort: {
                    [validatedSortBy]: sortOrder === "desc" ? -1 : 1,
                    _id: 1, // Always add _id for consistent sorting
                  },
                },
              ]
            : []),

          // **STAGE 9: ALL LOOKUPS WITHOUT PIPELINE PROJECTIONS**
          {
            $lookup: {
              from: "jamaats",
              localField: "jamaatID",
              foreignField: "_id",
              as: "jamaat",
            },
          },
          {
            $lookup: {
              from: "jamiats",
              localField: "jamiatID",
              foreignField: "_id",
              as: "jamiat",
            },
          },
          {
            $lookup: {
              from: "hierarchypositions",
              localField: "relevantMiqaat.hierarchyPositionID",
              foreignField: "_id",
              as: "hierarchy",
            },
          },
          {
            $lookup: {
              from: "departments",
              localField: "relevantMiqaat.departmentID",
              foreignField: "_id",
              as: "miqaatDept",
            },
          },
          {
            $lookup: {
              from: "arazcityzones",
              localField: "relevantMiqaat.arazCityZoneID",
              foreignField: "_id",
              as: "miqaatZone",
            },
          },
          {
            $lookup: {
              from: "arazcityzones",
              localField: "interestData.arazCityZoneID",
              foreignField: "_id",
              as: "interestZone",
            },
          },

          // **STAGE 10: INTEREST LOOKUPS - ONLY IF NOT ALREADY DONE**
          ...(!department && !priority
            ? [
                // Use the same optimized lookup pattern for interests when not already done
                {
                  $lookup: {
                    from: "interests",
                    localField: "_id",
                    foreignField: "userID",
                    as: "allInterests",
                  },
                },
                {
                  $addFields: {
                    interest: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: "$allInterests",
                            cond: {
                              $and: [
                                {
                                  $eq: [
                                    "$$this.arazCityID",
                                    toObjectId(arazCityID),
                                  ],
                                },
                                {
                                  $eq: [
                                    "$$this.miqaatID",
                                    toObjectId(miqaatID),
                                  ],
                                },
                              ],
                            },
                          },
                        },
                        0,
                      ],
                    },
                  },
                },
                {
                  $project: {
                    allInterests: 0,
                  },
                },
                {
                  $lookup: {
                    from: "departments",
                    localField: "interest.interestOne.departmentID",
                    foreignField: "_id",
                    as: "dept1",
                  },
                },
                {
                  $lookup: {
                    from: "departments",
                    localField: "interest.interestTwo.departmentID",
                    foreignField: "_id",
                    as: "dept2",
                  },
                },
                {
                  $lookup: {
                    from: "departments",
                    localField: "interest.interestThree.departmentID",
                    foreignField: "_id",
                    as: "dept3",
                  },
                },
              ]
            : []),

          // **STAGE 11: FINAL DATA SHAPING - OPTIMIZED FOR FE**
          {
            $addFields: {
              // Status calculation
              status: {
                $cond: {
                  if: {
                    $and: [
                      { $ne: ["$relevantMiqaat", null] },
                      {
                        $ne: [
                          {
                            $ifNull: [
                              "$relevantMiqaat.hierarchyPositionID",
                              null,
                            ],
                          },
                          null,
                        ],
                      },
                    ],
                  },
                  then: "assigned",
                  else: "not-assigned",
                },
              },
              razaStatus: {
                $cond: {
                  if: {
                    $and: [
                      {
                        $ifNull: ["$relevantMiqaat.miqaatHR", false],
                      },
                    ],
                  },
                  then: "$relevantMiqaat.miqaatHR",
                  else: null,
                },
              },

              // Jamaat name
              jamaatID: { name: { $arrayElemAt: ["$jamaat.name", 0] } },

              // Jamiat name
              jamiatID: { name: { $arrayElemAt: ["$jamiat.name", 0] } },

              // Priority departments (interests)
              "interestData.interestOne.departmentID.name": {
                $arrayElemAt: ["$dept1.name", 0],
              },
              "interestData.interestTwo.departmentID.name": {
                $arrayElemAt: ["$dept2.name", 0],
              },
              "interestData.interestThree.departmentID.name": {
                $arrayElemAt: ["$dept3.name", 0],
              },

              // Zone name - prioritize miqaat zone over interest zone
              "zone.name": {
                $cond: {
                  if: { $gt: [{ $size: "$miqaatZone" }, 0] },
                  then: { $arrayElemAt: ["$miqaatZone.name", 0] },
                  else: { $arrayElemAt: ["$interestZone.name", 0] },
                },
              },
            },
          },

          // **STAGE 12: LATE SORTING FOR LOOKUP-DEPENDENT FIELDS**
          ...(needsLateSorting
            ? [
                {
                  $sort: {
                    [validatedSortBy === "jamaat"
                      ? "jamaat.name"
                      : validatedSortBy === "jamiat"
                      ? "jamiat.name"
                      : validatedSortBy === "zone"
                      ? "zone.name"
                      : validatedSortBy === "department"
                      ? "priorityOne"
                      : validatedSortBy]: sortOrder === "desc" ? -1 : 1,
                    _id: 1,
                  },
                },
              ]
            : []),

          // **STAGE 13: PAGINATION - ONLY IF REQUIRED**
          ...(isPaginationProvided
            ? [{ $skip: skip }, { $limit: limitNum }]
            : []),

          // **STAGE 14: FINAL PROJECTION - ONLY FE REQUIRED FIELDS (MOVED TO END)**
          {
            $project: {
              _id: 1,
              ITSID: 1,
              name: 1,
              logo: 1,
              gender: 1,
              phone: 1,
              whatsapp: 1,
              jamaatID: 1,
              jamiatID: 1,
              occupation: 1,
              qualification: 1,
              interestData: 1,
              status: 1,
              updatedAt: 1,
              appDetails: 1,
              zone: 1,
              razaStatus: 1,
            },
          },
        ],
      },
    },
  ];

  return pipeline;
};

module.exports = { kgPoolAggregation };
