const { Schema, model } = require("mongoose");

const hierarchyPositionSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    alias: {
      type: String,
      required: false,
      trim: true,
      unique: false,
    },
    parentType: {
      type: String,
      required: true,
      trim: true,
      enum: ["single", "double", "top-most"],
    },
    isZonal: {
      type: Boolean,
      required: false,
      default: false,
    },
    isDepartmental: {
      type: Boolean,
      required: false,
      default: false,
    },
    isVisibleForArazCityUser: {
      type: Boolean,
      required: false,
      default: false,
    },
    parents: [
      {
        type: Schema.Types.ObjectId,
        ref: "HierarchyPosition",
        required: false,
      },
    ],
    isSystem: {
      type: Boolean,
      default: false,
    },
    weightage: {
      type: Number,
      required: false,
    },
    countRecommendation: {
      type: Number,
      required: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const HierarchyPosition = model("HierarchyPosition", hierarchyPositionSchema);

module.exports = { HierarchyPosition };
