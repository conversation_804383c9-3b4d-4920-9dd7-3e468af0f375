const { api<PERSON><PERSON><PERSON>, apiError, apiResponse } = require("../../../utils/api.util");
const { FETCH, NOT_FOUND } = require("../../../utils/message.util");
const { Jamiat, Jamaat } = require("../models");
const { isEmpty } = require("../../../utils/misc.util");

const getAllJamiats = apiHandler(async (req, res) => {
  const jamiats = await Jamiat.find({}, "_id name ITSID").sort({ _id: 1 });

  return apiResponse(FETCH, "Jamiats", jamiats, res);
});

const getAllJamaats = apiHandler(async (req, res) => {
  const { ids } = req.body;
  const query = ids?.[0] === "all" ? {} : { jamiatID: { $in: ids } };

  const jamaatData = await Jamaat.find(
    query,
    "_id name ITSID ITSJamiatID"
  ).sort({
    _id: 1,
  });

  if (isEmpty(jamaatData)) {
    return apiError(NOT_FOUND, "Jamaats", null, res);
  }
  return apiResponse(FETCH, "Jamaats", jamaatData, res);
});

const getJamiatIDByITSID = async (ITSID) => {
  let jamiat = await Jamiat.findOne(
    { ITSID: ITSID.toString() },
    { _id: 1 }
  ).lean();

  if (!isEmpty(jamiat)) {
    jamiat.id = jamiat?._id?.toString();
  }

  return jamiat;
};

const getJamaatIDByITSID = async (ITSID, ITSJamiatID) => {
  let jamaat = await Jamaat.findOne(
    { ITSID: ITSID.toString(), ITSJamiatID: ITSJamiatID.toString() },
    { _id: 1 }
  ).lean();

  if (!isEmpty(jamaat)) {
    jamaat.id = jamaat?._id?.toString();
  }

  return jamaat;
};

module.exports = {
  getAllJamiats,
  getAllJamaats,
  getJamiatIDByITSID,
  getJamaatIDByITSID,
};
