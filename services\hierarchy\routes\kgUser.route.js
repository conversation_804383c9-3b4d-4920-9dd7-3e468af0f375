const {
  validate,
  validateMultiple,
} = require("../../../middlewares/validation.middleware");

const {
  getAllKGUsers,
  getKGUsers,
  addEditKGUser,
  getSingleKGUser,
  deleteKGUser,
  importITSUsers,
  assignKGUser,
  assignKGUserV2,
  addOneSignalDevice,
  // getKGUsersByPagination,
  getKGUsersByPaginationV2,
  deleteKGUsersBulk,
  uploadKGUser,
} = require("../controllers/kgUser.controller");

const {
  getITSUser,
  loginUser,
  syncITSUser,
  getUserProfile,
  updateKGConsentStatus,
  syncDBITSUser,
  getUserDetailsByITS,
  getKGUsersByITSIDs,
  loginUserV2,
  loginUserV3,
} = require("../controllers/user.controller");
const {
  getEncryptedSystemRole,
  getEncryptedSystemRole2,
} = require("../controllers/systemRole.controller");
const {
  getKGUsersSchema,
  addEditKGUserSchema,
  getSingleKGUserSchema,
  deleteKGUserSchema,
  importITSUsersSchema,
  assignKGUserSchema,
  addOneSignalDeviceSchema,
  getKGUsersByPaginationSchema,
  deleteBulkKGUserSchema,
  uploadKGUserSchema,
} = require("../validations/kgUser.validation");
const {
  getITSUserSchema,
  syncITSUserSchema,
  updateKGConsentStatusSchema,
  getUserDetailByITSSchema,
  getUserByITSIDSchema,
} = require("../validations/user.validation");
const {
  getEncryptedSystemRoleSchema,
} = require("../validations/systemRole.validation");

const router = require("express").Router();

//===============================USER ROUTES=======================================================

router.get(
  "/add/get-its/:id",
  validate(getITSUserSchema, "params"),
  getITSUser
);

router.post("/edit/its-sync", validate(syncITSUserSchema, "body"), syncITSUser);

router.post("/its-one-login", loginUserV3);

router.post(
  "/get/encrypted-permissions",
  validate(getEncryptedSystemRoleSchema, "body"),
  getEncryptedSystemRole
);

router.post(
  "/get/encrypted-permissions2",
  validate(getEncryptedSystemRoleSchema, "body"),
  getEncryptedSystemRole2
);

router.get("/get/profile", getUserProfile);
router.post(
  "/get/kg-users-by-its",
  validate(getUserByITSIDSchema, "body"),
  getKGUsersByITSIDs
);

router.patch(
  "/edit/update-consent-status",
  validate(updateKGConsentStatusSchema, "body"),
  updateKGConsentStatus
);

//================================KG User Routes======================================================

router.get("/get/all", getAllKGUsers);

router.post("/get", validate(getKGUsersSchema, "body"), getKGUsers);
router.post(
  "/get/by-pagination",
  validate(getKGUsersByPaginationSchema, "body"),
  getKGUsersByPaginationV2
);

router.post("/add", validate(addEditKGUserSchema, "body"), addEditKGUser);

router.post(
  "/get/single-kg",
  validate(getSingleKGUserSchema, "body"),
  getSingleKGUser
);

router.delete(
  "/delete/bulk",
  validate(deleteBulkKGUserSchema, "body"),
  deleteKGUsersBulk
);
router.delete("/delete", validate(deleteKGUserSchema, "body"), deleteKGUser);

router.post(
  "/get/import-its-users",
  validate(importITSUsersSchema, "body"),
  importITSUsers
);

router.post(
  "/add/assign",
  validate(assignKGUserSchema, "body"),
  assignKGUserV2
);

router.post(
  "/add/upload-users",
  validate(uploadKGUserSchema, "body"),
  uploadKGUser
);

router.post(
  "/add/one-signal-device",
  validate(addOneSignalDeviceSchema, "body"),
  addOneSignalDevice
);

router.post("/edit/db-its-sync", syncDBITSUser);

router.get(
  "/add/get-user-details-by-its/:id",
  validateMultiple(getUserDetailByITSSchema, ["params", "query"]),
  getUserDetailsByITS
);

module.exports = router;
