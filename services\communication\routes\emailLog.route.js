const { validate } = require("../../../middlewares/validation.middleware");
const {
  getEmails,
  getEmail,
  getEmailRecipients,
} = require("../controllers/email.controller");
const {
  getEmailsSchema,
  getEmailSchema,
  getEmailRecipientsSchema,
} = require("../validations/email.validation");

const router = require("express").Router();

router.post("/get", validate(getEmailsSchema, "body"), getEmails);

router.get("/get/:id", validate(getEmailSchema, "params"), getEmail);

router.get(
  "/get/recipients/:id",
  validate(getEmailRecipientsSchema, "params"),
  getEmailRecipients
);

module.exports = router;
