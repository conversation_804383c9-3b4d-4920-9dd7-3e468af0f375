const path = require("path");
const pdf = require("html-pdf");
const constants = require("../../../constants");
const {
  apiHandler,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const {
  EXISTS,
  UPDATE_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  FETCH,
  ADD_SUCCESS,
} = require("../../../utils/message.util");
const { toObjectId, toString, isEmpty } = require("../../../utils/misc.util");
const {
  CompileList,
  Department,
  KGUser,
  TravelArazCity,
  RazaList,
  ArazCity,
} = require("../models");
const fs = require("fs");
const {
  getDepartmentWiseUsersAggregation,
} = require("../aggregations/compileUserList.aggregation");
const { importITSUsers, importITSUsersHelper } = require("./kgUser.controller");

const updateRazaList = async (miqaatID, compileList, isApproved) => {
  let razaList = await RazaList.findOne({ miqaatID: toObjectId(miqaatID) });

  if (!razaList) {
    const fasalArazCity = await ArazCity.findOne({
      miqaatID: toObjectId(miqaatID),
      isFasal: true,
    });
    razaList = await RazaList.create({
      miqaatID: miqaatID,
      fasalArazCityID: fasalArazCity ? fasalArazCity._id : null,
      kgUsers: [],
      duplicateKgUsers: [],
      totalUniqueUsers: 0,
    });
  }

  if (isApproved) {
    const kgUsers = new Map(
      razaList.kgUsers.map((user) => [user.userITS, user])
    );
    const duplicateKgUsers = razaList.duplicateKgUsers;

    compileList.departments.forEach((department) => {
      department.kgUsers.forEach((kgUser) => {
        if (kgUsers.has(kgUser.ITSID)) {
          const duplicateExists = duplicateKgUsers.some(
            (user) =>
              user.userID.equals(kgUser._id) &&
              user.userITS === kgUser.ITSID &&
              user.departmentID.equals(department.departmentID) &&
              user.compileListName === compileList.name
          );

          if (!duplicateExists) {
            duplicateKgUsers.push({
              userID: kgUser._id,
              userITS: kgUser.ITSID,
              departmentID: department.departmentID,
              compileListName: compileList.name,
            });
          }
        } else {
          if (!kgUsers.has(kgUser.ITSID)) {
            kgUsers.set(kgUser.ITSID, {
              userID: kgUser._id,
              userITS: kgUser.ITSID,
              isMerged: false,
              isAlreadyExist: false,
              isError: false,
            });
          }
        }
      });
    });

    await RazaList.updateOne(
      { miqaatID: toObjectId(miqaatID) },
      {
        $set: {
          kgUsers: Array.from(kgUsers.values()),
          duplicateKgUsers,
          totalUniqueUsers: Array.from(kgUsers.values()).length,
        },
      }
    );
  } else {
    const users = compileList.departments
      .map((department) => department.kgUsers)
      .flat();
    const totalUniqueUsers = Math.max(
      razaList.totalUniqueUsers - users.length,
      0
    );
    await RazaList.updateOne(
      { miqaatID: toObjectId(miqaatID) },
      {
        $pull: {
          kgUsers: {
            userITS: { $in: users.map((user) => user.ITSID) },
          },
        },
        $set: {
          totalUniqueUsers,
        },
      }
    );
  }
};

const createPdf = (htmlContent) => {
  return new Promise((resolve, reject) => {
    pdf
      .create(htmlContent, { format: "A4", orientation: "landscape" })
      .toBuffer((err, buffer) => {
        if (err) {
          reject(err); // Reject the Promise if there’s an error
        } else {
          resolve(buffer); // Resolve the Promise with the PDF buffer
        }
      });
  });
};

const downloadCompileListPDF = apiHandler(async (req, res) => {
  const { departmentID } = req.params;

  const findQuery = departmentID
    ? { "smeRecommendation.departmentID": toObjectId(departmentID) }
    : { smeRecommendation: { $exists: true } };
  const recommendedUsers = await KGUser.find(findQuery)
    .select("age logo name LDName ITSID smeRecommendation jamiatID jamaatID")
    .populate([
      { path: "jamiatID", select: "name" },
      { path: "jamaatID", select: "name" },
      { path: "smeRecommendation.departmentID", select: "name LDName" },
      { path: "smeRecommendation.kgTypeID", select: "name" },
      {
        path: "smeRecommendation.hierarchyPositionID",
        select: "name LDName alias",
      },
      { path: "smeRecommendation.functionID", select: "name" },
      { path: "smeRecommendation.travelPriority", select: "name" },
      { path: "smeRecommendation.razaRecommendation", select: "name" },
      { path: "smeRecommendation.travelCities", select: "name" },
      { path: "smeRecommendation.recommendedBy", select: "name" },
    ]);
  if (!recommendedUsers.length) {
    return apiError(NOT_FOUND, "Recommended users", null, res);
  }

  const filepath = path.resolve(
    __dirname,
    "../../../utils/templates/compileListReport.html"
  );
  const htmlTemplate = fs.readFileSync(filepath, "utf-8");

  let rows = [];
  recommendedUsers.map((item, index) => {
    const rowCount = index + 1;

    const isEven = rowCount % 2 === 0;
    const rowStyle = isEven ? "background-color: #fcfaf6;" : "#ffffff";
    rows.push(`
      <tr style="${rowStyle}">
        <td>${rowCount}</td>
        <td style="text-align: center;">
          <div>${item.ITSID}</div>
          <img src="${constants.IMAGE_BASE_URL}/${item.logo}" alt="${
      item.name
    }">
        </td>
        <td class="arabic">${item.LDName}</td>
        <td>${item.age}</td>
        <td >${item.jamiatID?.name || ""}</td>
        <td >${item.jamaatID?.name || ""}</td>
        <td>${item.smeRecommendation?.departmentID?.name || ""}</td>
        <td>${item.smeRecommendation?.kgTypeID?.name || ""}</td>
        <td>${item.smeRecommendation?.hierarchyPositionID?.name || ""}</td>
        <td>${
          item.smeRecommendation?.otherFunction?.length > 0
            ? item.smeRecommendation.otherFunction
            : item.smeRecommendation?.functionID?.name || ""
        }</td>
        <td>${item.smeRecommendation?.travelPriority?.name || ""}</td>
        <td>${item.smeRecommendation?.razaRecommendation?.name || ""}</td>
      </tr>
    `);
    if (rowCount === 7 || (rowCount > 7 && (rowCount - 7) % 8 === 0)) {
      rows.push(`
        </tbody>
        </table>
        <div style="page-break-after: always;"></div>
        <table style="margin-top: 40px;"><thead>
          <tr>
            <th>Sr</th>
            <th>ITS</th>
            <th>Full Name</th>
            <th>Hijri Age</th>
            <th>Jamiat</th>
            <th>Jamaat</th>
            <th>Department</th>
            <th>KG Type</th>
            <th>Hierarchy Position</th>
            <th>Function</th>
            <th>Travel to Fasal City</th>
            <th>Raza Recom.</th>
          </tr>
        </thead><tbody>
      `);
    }
  });
  rows = rows.join("");

  const finalHTML = htmlTemplate
    .replace("{{rows}}", rows)
    .replace("{{date}}", new Date().toLocaleDateString());

  try {
    // Use async/await with the wrapped createPdf function
    const pdfBuffer = await createPdf(finalHTML);

    res.set({
      "Content-Type": "application/pdf",
      "Content-Length": pdfBuffer.length,
    });
    res.send(pdfBuffer);
  } catch (error) {
    console.error("Error generating PDF:", error);
    res.status(500).send("Error generating PDF");
  }
});

const getUsersFromITS = apiHandler(async (req, res) => {
  const { id: compileListID } = req.body;
  const ITSResponse = await importITSUsersHelper(req);
  const ITSUsers = ITSResponse.data;

  const filteredRecommendedUsers = ITSUsers.filter(
    (user) => !user.isInCompileList
  );

  const existingUsersOnList = ITSUsers.length - filteredRecommendedUsers.length;
  const response = {
    users: filteredRecommendedUsers,
    message:
      compileListID && existingUsersOnList !== 0
        ? existingUsersOnList > 1
          ? `The AMS System has identified that ${existingUsersOnList} individuals are already on the list.`
          : `The AMS System has identified that ${existingUsersOnList} individual is already on the list.`
        : null,
  };

  return apiResponse(FETCH, "Users", response, res);
});

const getDepartmentWiseUsers = apiHandler(async (req, res) => {
  const { departmentID, id: compileListID, arazCityID } = req.body;

  const recommendedUsers = await KGUser.aggregate(
    getDepartmentWiseUsersAggregation(departmentID, arazCityID, compileListID)
  );
  if (!recommendedUsers.length) {
    return apiError(NOT_FOUND, "Recommended users", null, res);
  }

  const filteredRecommendedUsers = recommendedUsers.filter(
    (user) => !user.isInCompileList
  );

  const existingUsersOnList =
    recommendedUsers.length - filteredRecommendedUsers.length;
  const response = {
    users: filteredRecommendedUsers,
    message:
      compileListID && existingUsersOnList !== 0
        ? existingUsersOnList > 1
          ? `The AMS System has identified that ${existingUsersOnList} individuals are already on the list.`
          : `The AMS System has identified that ${existingUsersOnList} individual is already on the list.`
        : null,
  };

  return apiResponse(FETCH, "Recommended users", response, res);
});

const getCompileLists = apiHandler(async (req, res) => {
  const compileLists = await CompileList.find().select("name");
  if (!compileLists.length) {
    return apiError(NOT_FOUND, "Compile Lists", null, res);
  }

  return apiResponse(FETCH, "Compile Lists", compileLists, res);
});

const getCompileList = apiHandler(async (req, res) => {
  const { id } = req.params;

  const compileList = await CompileList.findById(id).populate([
    { path: "departments.departmentID", select: "name LDName" },
    {
      path: "departments.kgUsers",
      select:
        "age logo name LDName ITSID smeRecommendation jamiatID jamaatID gender phone email miqaats appDetails",
      populate: [
        { path: "jamiatID", select: "name" },
        { path: "jamaatID", select: "name" },
        { path: "smeRecommendation.departmentID", select: "name LDName" },
        { path: "smeRecommendation.kgTypeID", select: "name" },
        {
          path: "smeRecommendation.hierarchyPositionID",
          select: "name LDName alias",
        },
        { path: "smeRecommendation.functionID", select: "name" },
        { path: "smeRecommendation.travelPriority", select: "name" },
        { path: "smeRecommendation.razaRecommendation", select: "name" },
        { path: "smeRecommendation.travelCities", select: "name" },
        { path: "smeRecommendation.recommendedBy", select: "name" },
      ],
    },
  ]);

  if (!compileList) {
    return apiError(NOT_FOUND, "Compile List", null, res);
  }

  const AMSMiqaatID = '67f51275351391d7a517d052';
  const ChennaiCityID = '682b16ea16b122b53c939018';

  compileList.departments.forEach(dept => {
    dept.kgUsers.forEach(user => {
      const matchedMiqaat = user.miqaats?.find(miqaat =>
        miqaat.miqaatID?.toString() === AMSMiqaatID &&
        miqaat.arazCityID?.toString() === ChennaiCityID
      );

      user._doc.modeOfTravel = matchedMiqaat?.modeOfTravel || null;
      user._doc.arrivalDate = matchedMiqaat?.arrivalDate || null;
      user._doc.arrivalTime = matchedMiqaat?.arrivalTime || null;
      user._doc.flightOrTrainNumber = matchedMiqaat?.flightOrTrainNumber || null;
      user._doc.recievedArrivalInfo = matchedMiqaat?.recievedArrivalInfo || null;
    });
  });

  return apiResponse(FETCH, "Compile List", compileList, res);
});

// const getCompileList = apiHandler(async (req, res) => {
//   const { id } = req.params;

//   const compileList = await CompileList.findById(id).populate([
//     { path: "departments.departmentID", select: "name LDName" },
//     {
//       path: "departments.kgUsers",
//       select:
//         "age logo name LDName ITSID smeRecommendation jamiatID jamaatID gender phone email",
//       populate: [
//         { path: "jamiatID", select: "name" },
//         { path: "jamaatID", select: "name" },
//         { path: "smeRecommendation.departmentID", select: "name LDName" },
//         { path: "smeRecommendation.kgTypeID", select: "name" },
//         {
//           path: "smeRecommendation.hierarchyPositionID",
//           select: "name LDName alias",
//         },
//         { path: "smeRecommendation.functionID", select: "name" },
//         { path: "smeRecommendation.travelPriority", select: "name" },
//         { path: "smeRecommendation.razaRecommendation", select: "name" },
//         { path: "smeRecommendation.travelCities", select: "name" },
//         { path: "smeRecommendation.recommendedBy", select: "name" },
//       ],
//     },
//   ]); 
//   if (!compileList) {
//     return apiError(NOT_FOUND, "Compile List", null, res);
//   }

//   return apiResponse(FETCH, "Compile List", compileList, res);
// });

const addCompileList = apiHandler(async (req, res) => {
  const { name, approvedForRaza, users } = req.body;
  const { _id: loggedInUserID } = req.user;
  const miqaatID = "67f51275351391d7a517d052" // main DB miqaat
  // const miqaatID = "6841ce2d4c71b479522ba720" // main DB miqaat
  // const miqaatID = "67989e2524b93a66ce04105a"; // testing DB miqaat

  const existingCompileList = await CompileList.findOne({
    name,
  });
  if (existingCompileList) {
    return apiError(EXISTS, "Compile List", null, res);
  }

  const departmentIDs = await Department.find().select("_id");
  const departmentObj = {};
  departmentIDs.forEach((dep) => {
    departmentObj[dep._id.toString()] = [];
  });

  const bulkOps = [];
  for (const user of users) {
    const departmentID = user.departmentID.toString();
    departmentObj[departmentID].push(user._id.toString());

    user.recommendedBy = user.recommendedBy
      ? user.recommendedBy
      : loggedInUserID;
    for (const key in user) {
      if (isEmpty(user[key])) {
        user[key] = null;
      }
    }
    const updateQuery = { $set: { smeRecommendation: user } };

    const isSystemUser = await KGUser.countDocuments({
      ITSID: user.ITSID,
      systemRoleID: { $exists: true },
    });
    if (!isSystemUser) {
      updateQuery["systemRoleID"] = constants.SYSTEM_ROLES.RECOMMENDED_USER[0];
    }
    updateQuery["systemDepartmentID"] = user.departmentID;

    bulkOps.push({
      updateOne: {
        filter: { ITSID: user.ITSID },
        update: updateQuery,
      },
    });
  }
  await KGUser.bulkWrite(bulkOps);

  const departments = Object.entries(departmentObj).map(
    ([departmentID, kgUsers]) => ({
      departmentID,
      kgUsers,
      existingKgUsers: kgUsers.length,
    })
  );

  const createdCompileList = await CompileList.create({
    name,
    approvedForRaza,
    departments,
  });

  const compileList = await CompileList.findById(
    createdCompileList._id
  ).populate("departments.kgUsers", "name LDName ITSID");
  await updateRazaList(miqaatID, compileList, approvedForRaza);

  return apiResponse(ADD_SUCCESS, "Compile List", null, res);
});

const addCompileListQuota = apiHandler(async (req, res) => {
  const { id, departments } = req.body;

  const existingCompileList = await CompileList.findById(id);
  if (!existingCompileList) {
    return apiError(NOT_FOUND, "Compile List", null, res);
  }

  const bulkOps = departments.map((dep) => ({
    updateOne: {
      filter: { _id: toObjectId(id) },
      update: {
        $set: {
          "departments.$[elem].quota": dep.quota,
        },
      },
      arrayFilters: [{ "elem.departmentID": toObjectId(dep.departmentID) }],
    },
  }));

  await CompileList.bulkWrite(bulkOps);

  return apiResponse(ADD_SUCCESS, "Quota for departments", null, res);
});

const editCompileList = apiHandler(async (req, res) => {
  const { id, name, approvedForRaza, users } = req.body;
  const { _id: loggedInUserID } = req.user;
  const miqaatID = "67f51275351391d7a517d052" // main DB miqaat
  // const miqaatID = "6841ce2d4c71b479522ba720" // main DB miqaat
  // const miqaatID = "67989e2524b93a66ce04105a"; // testing DB miqaat

  const existingCompileList = await CompileList.findOne({
    _id: { $ne: toObjectId(id) },
    name,
  });
  if (existingCompileList) {
    return apiError(EXISTS, "Compile List", null, res);
  }

  const compileList = await CompileList.findById(id);

  const departmentObj = {};
  compileList.departments.forEach((dep) => {
    const departmentUsers = toString(dep.kgUsers);
    users.map((user) => {
      const foundIndex = departmentUsers.findIndex(
        (depUser) =>
          depUser === user._id.toString() &&
          user.departmentID.toString() !== dep.departmentID.toString()
      );
      if (foundIndex !== -1) {
        departmentUsers.splice(foundIndex, 1);
      }
    });
    departmentObj[dep.departmentID.toString()] = departmentUsers;
  });

  const bulkOps = [];
  for (const user of users) {
    const departmentID = user.departmentID.toString();
    departmentObj[departmentID].push(user._id.toString());
    user.recommendedBy = user.recommendedBy
      ? user.recommendedBy
      : loggedInUserID;
    const updateFields = {};

    const isSystemUser = await KGUser.countDocuments({
      ITSID: user.ITSID,
      systemRoleID: { $exists: true },
    });
    if (!isSystemUser) {
      updateFields["systemRoleID"] = constants.SYSTEM_ROLES.RECOMMENDED_USER[0];
    }
    updateFields["systemDepartmentID"] = user.departmentID;

    for (const key in user) {
      updateFields[`smeRecommendation.${key}`] = isEmpty(user[key])
        ? null
        : user[key];
    }

    bulkOps.push({
      updateOne: {
        filter: { ITSID: user.ITSID },
        update: { $set: updateFields },
      },
    });
  }
  await KGUser.bulkWrite(bulkOps);

  const departments = Object.entries(departmentObj).map(
    ([departmentID, kgUsers]) => {
      const uniqueUsers = [...new Set(kgUsers)];
      return {
        departmentID,
        kgUsers: uniqueUsers,
        existingKgUsers: uniqueUsers.length,
      };
    }
  );

  await CompileList.findByIdAndUpdate(id, {
    name,
    approvedForRaza,
    departments,
  });

  const populatedCompileList = await CompileList.findById(id).populate(
    "departments.kgUsers",
    "name LDName ITSID"
  );
  await updateRazaList(miqaatID, populatedCompileList, approvedForRaza);

  return apiResponse(UPDATE_SUCCESS, "Compile List", null, res);
});

const editCompileListUser = apiHandler(async (req, res) => {
  const { id, userID, departmentID } = req.body;
  const { _id: loggedInUserID } = req.user;
  const user = req.body;

  const compileList = await CompileList.findById(id);
  const compileListDepartments = compileList.departments;
  compileListDepartments.forEach((dep) => {
    if (toString(dep.kgUsers).includes(userID)) {
      dep.kgUsers = dep.kgUsers.filter((id) => id.toString() !== userID);
      dep.existingKgUsers -= 1;
    }
    if (dep.departmentID.toString() === departmentID) {
      dep.kgUsers.push(userID);
      dep.existingKgUsers += 1;
    }
  });

  const bulkOps = [];
  user.recommendedBy = user.recommendedBy ? user.recommendedBy : loggedInUserID;
  const updateFields = {};

  const isSystemUser = await KGUser.countDocuments({
    ITSID: user.ITSID,
    systemRoleID: { $exists: true },
  });
  if (!isSystemUser) {
    updateFields["systemRoleID"] = constants.SYSTEM_ROLES.RECOMMENDED_USER[0];
  }
  updateFields["systemDepartmentID"] = user.departmentID;

  for (const key in user) {
    updateFields[`smeRecommendation.${key}`] = isEmpty(user[key])
      ? null
      : user[key];
  }

  bulkOps.push({
    updateOne: {
      filter: { ITSID: user.ITSID },
      update: { $set: updateFields },
    },
  });
  await KGUser.bulkWrite(bulkOps);

  await CompileList.findByIdAndUpdate(id, {
    $set: { departments: compileListDepartments },
  });

  return apiResponse(UPDATE_SUCCESS, "User", null, res);
});

const deleteCompileList = apiHandler(async (req, res) => {
  const { id } = req.params;

  const deletedCompileList = await CompileList.findByIdAndDelete(id);
  if (!deletedCompileList) {
    return apiError(NOT_FOUND, "Compile List", null, res);
  }

  return apiResponse(DELETE_SUCCESS, "Compile List", null, res);
});

const deleteCompileListUser = apiHandler(async (req, res) => {
  const { id, userID } = req.body;

  const compileList = await CompileList.findById(id);
  const compileListDepartments = compileList.departments;
  compileListDepartments.forEach((dep) => {
    if (toString(dep.kgUsers).includes(userID)) {
      dep.kgUsers = dep.kgUsers.filter((id) => id.toString() !== userID);
      dep.existingKgUsers -= 1;
    }
  });

  await CompileList.findByIdAndUpdate(id, {
    $set: { departments: compileListDepartments },
  });

  return apiResponse(DELETE_SUCCESS, "User", null, res);
});

const getArazCity = apiHandler(async (req, res) => {
  const data = await TravelArazCity.find();
  return apiResponse(FETCH, "Araz City", data, res);
});

const getAllApprovedCompileLists = apiHandler(async (req, res) => {
  const compileLists = await CompileList.find({
    approvedForRaza: true,
  }).populate([
    { path: "departments.departmentID", select: "name LDName" },
    {
      path: "departments.kgUsers",
      select:
        "age logo name LDName ITSID smeRecommendation jamiatID jamaatID gender phone email appDetails",
      populate: [
        { path: "jamiatID", select: "name" },
        { path: "jamaatID", select: "name" },
        { path: "smeRecommendation.departmentID", select: "name LDName" },
        { path: "smeRecommendation.kgTypeID", select: "name" },
        {
          path: "smeRecommendation.hierarchyPositionID",
          select: "name LDName alias",
        },
        { path: "smeRecommendation.functionID", select: "name" },
        { path: "smeRecommendation.travelPriority", select: "name" },
        { path: "smeRecommendation.razaRecommendation", select: "name" },
        { path: "smeRecommendation.travelCities", select: "name" },
        { path: "smeRecommendation.recommendedBy", select: "name" },
      ],
    },
  ]);

  if (!compileLists || compileLists.length === 0) {
    return apiError(NOT_FOUND, "Compile Lists", null, res);
  }

  return apiResponse(FETCH, "Compile Lists", compileLists, res);
});

module.exports = {
  getAllApprovedCompileLists,
  getArazCity,
  downloadCompileListPDF,
  getUsersFromITS,
  getDepartmentWiseUsers,
  getCompileLists,
  getCompileList,
  addCompileList,
  addCompileListQuota,
  editCompileList,
  editCompileListUser,
  deleteCompileList,
  deleteCompileListUser,
};
