const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON>rror,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
  UNAUTHORIZED,
} = require("../../../utils/message.util");
const { SystemRole, KGUser } = require("../models");
const {
  isEmpty,
  getUniqueName,
  toObjectId,
  decryptPermissions,
} = require("../../../utils/misc.util");
const { CRYPTO_SECRET, SYSTEM_ROLES } = require("../../../constants");
const CryptoJS = require("crypto-js");
const {
  clearCacheByPattern,
  redisCacheKeys,
  setCache,
  getCache,
} = require("../../../utils/redis.cache");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getAllSystemRoles = apiHandler(async (req, res) => {
  const isSystemUser = req.user.isSystemUser;
  const queryObject = isSystemUser ? {} : { isVisibleForArazCityUser: true };

  // if (
  //   req.user.systemRoleID?.toString() !== SYSTEM_ROLES.SUPER_ADMIN[0].toString()
  // ) {
  //   queryObject.isAccessible = true;
  // }
  const systemRoles = await SystemRole.find(queryObject).sort({ _id: -1 });
  return apiResponse(FETCH, "System Roles", systemRoles, res);
});

const addSystemRole = apiHandler(async (req, res) => {
  const { name } = req.body;

  const uniqueName = getUniqueName(name);

  const existingSystemRole = await SystemRole.findOne({ uniqueName });

  if (!isEmpty(existingSystemRole)) {
    return apiError(
      CUSTOM_ERROR,
      "System Role with this name already exists",
      null,
      res
    );
  }

  const newSystemRole = new SystemRole({
    ...req.body,
    uniqueName,
    createdBy: req.user._id,
  });

  const systemRole = await newSystemRole.save();
  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:*`;
  await clearCacheByPattern(cachekey);

  return apiResponse(ADD_SUCCESS, "System Role", systemRole, res);
});

const getSingleSystemRole = apiHandler(async (req, res) => {
  const { id } = req.params;
  const isSystemUser = req.user.isSystemUser;
  const findQuery = isSystemUser
    ? { _id: id }
    : { _id: id, isVisibleForArazCityUser: true };

  const systemRoleData = await SystemRole.findOne(findQuery);

  if (isEmpty(systemRoleData)) {
    return apiError(NOT_FOUND, "System Role", null, res);
  }
  return apiResponse(FETCH, "System Role", systemRoleData, res);
});

const editSystemRole = apiHandler(async (req, res) => {
  const { id, name } = req.body;

  const uniqueName = getUniqueName(name);

  const existingSystemRole = await SystemRole.findOne({
    uniqueName,
    _id: { $ne: id },
  });

  if (!isEmpty(existingSystemRole)) {
    return apiError(
      CUSTOM_ERROR,
      "System Role with this name already exists",
      null,
      res
    );
  }

  const updateData = {
    ...req.body,
    uniqueName,
    updatedBy: req.user._id,
  };

  const systemRoleData = await SystemRole.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  if (isEmpty(systemRoleData)) {
    return apiError(NOT_FOUND, "System Role", null, res);
  }
  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:*`;
  await clearCacheByPattern(cachekey);
  return apiResponse(UPDATE_SUCCESS, "System Role", systemRoleData, res);
});

const deleteSystemRole = apiHandler(async (req, res) => {
  const { id } = req.params;
  const kgUserData = await KGUser.findOne({
    $or: [
      { systemRoleID: toObjectId(id) },
      {
        miqaats: {
          $elemMatch: {
            cityRoleID: toObjectId(id),
          },
        },
      },
    ],
  });

  if (!isEmpty(kgUserData)) {
    return apiError(
      CUSTOM_ERROR,
      "System Role is already assigned to some user or in miqaat, please remove it first",
      null,
      res
    );
  }
  const systemRoleData = await SystemRole.findOneAndDelete({ _id: id });

  if (isEmpty(systemRoleData)) {
    return apiError(NOT_FOUND, "System Role", null, res);
  }
  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:*`;
  await clearCacheByPattern(cachekey);

  return apiResponse(DELETE_SUCCESS, "System Role", null, res);
});

const getEncryptedSystemRole = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  
  const userID = req.user._id;
  
  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.SYSTEM_ROLES}:${miqaatID}:${arazCityID}:${userID}`;
  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Permissions", data, res, true);
  }
  
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let kgUserData = await KGUser.aggregate([
    {
      $match: {
        _id: toObjectId(userID),
      },
    },
    {
      $addFields: {
        filteredMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                as: "miqaat",
                cond: {
                  $and: [
                    { $eq: ["$$miqaat.miqaatID", toObjectId(miqaatID)] },
                    { $eq: ["$$miqaat.arazCityID", toObjectId(arazCityID)] },
                    { $eq: ["$$miqaat.isActive", true] },
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $match: {
        filteredMiqaat: { $ne: null },
      },
    },
    {
      $lookup: {
        from: "systemroles",
        localField: "filteredMiqaat.cityRoleID",
        foreignField: "_id",
        as: "systemRole",
      },
    },
    {
      $unwind: {
        path: "$systemRole",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        "systemRole.uniqueName": 1,
        "systemRole.modules": 1,
      },
    },
    { $sort: { _id: -1 } },
  ]);

  if (isEmpty(kgUserData)) {
    return apiError(NOT_FOUND, "User", null, res);
  }

  let systemRole = {
    systemRoleName: kgUserData[0]?.systemRole?.uniqueName,
    modules: kgUserData[0]?.systemRole?.modules,
  };

  if (isEmpty(systemRole)) {
    return apiError(NOT_FOUND, "Permissions in City", null, res);
  }

  let encryptedSystemRole = CryptoJS.AES.encrypt(
    JSON.stringify(systemRole),
    CRYPTO_SECRET
  ).toString();

  apiResponse(FETCH, "Permissions", encryptedSystemRole, res);

  await setCache(cachekey, encryptedSystemRole);
});

const getEncryptedSystemRole2 = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;
  const userID = req.user._id;
  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.SYSTEM_ROLES}:${miqaatID}:${arazCityID}:${userID}`;

  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Permissions", data, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let kgUserData = await KGUser.aggregate([
    {
      $match: {
        _id: toObjectId(userID),
      },
    },
    {
      $addFields: {
        filteredMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                as: "miqaat",
                cond: {
                  $and: [
                    { $eq: ["$$miqaat.miqaatID", toObjectId(miqaatID)] },
                    { $eq: ["$$miqaat.arazCityID", toObjectId(arazCityID)] },
                    { $eq: ["$$miqaat.isActive", true] },
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $match: {
        filteredMiqaat: { $ne: null },
      },
    },
    {
      $lookup: {
        from: "systemroles",
        localField: "filteredMiqaat.cityRoleID",
        foreignField: "_id",
        as: "systemRole",
      },
    },
    {
      $unwind: {
        path: "$systemRole",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        "systemRole._id": 1,
        "systemRole.uniqueName": 1,
        "systemRole.modules": 1,
      },
    },
    { $sort: { _id: -1 } },
  ]);

  if (isEmpty(kgUserData)) {
    return apiError(NOT_FOUND, "User", null, res);
  }

  let systemRole = {
    systemRoleName: kgUserData[0]?.systemRole?.uniqueName,
    modules: kgUserData[0]?.systemRole?.modules,
  };

  let systemRoleID = {
    systemRoleID: kgUserData[0]?.systemRole?._id,
  };

  if (isEmpty(systemRole)) {
    return apiError(NOT_FOUND, "Permissions in City", null, res);
  }

  let encryptedSystemRole = CryptoJS.AES.encrypt(
    JSON.stringify(systemRole),
    CRYPTO_SECRET
  ).toString();
  let encryptedSystemRoleID = CryptoJS.AES.encrypt(
    JSON.stringify(systemRoleID),
    CRYPTO_SECRET
  ).toString();

  apiResponse(
    FETCH,
    "Permissions",
    { encryptedSystemRole, encryptedSystemRoleID },
    res
  );
  await setCache(cachekey, { encryptedSystemRole, encryptedSystemRoleID });
});

const getUserRoleAndPermissions = (encryptedPermissions) => {
  try {
    const decryptedData = decryptPermissions(
      encryptedPermissions,
      CRYPTO_SECRET
    );
    return JSON.parse(decryptedData);
  } catch (error) {
    return null;
  }
};

module.exports = {
  getAllSystemRoles,
  addSystemRole,
  getSingleSystemRole,
  editSystemRole,
  deleteSystemRole,
  getEncryptedSystemRole,
  getUserRoleAndPermissions,
  getEncryptedSystemRole2,
};
