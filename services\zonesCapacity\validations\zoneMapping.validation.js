const Joi = require("joi");
const { idValidation, stringValidation } = require("../../../utils/validator.util");


const getArazCityWiseJamaatAndJamiatSchema = Joi.object({
  arazCityID: idValidation,
});

const getZoneMappingSchema = Joi.object({
  arazCityID: idValidation,
  miqaatID: idValidation,
});

const getSingleZoneMappingReportSchema = Joi.object({
  arazCityZoneID: idValidation,
});

const getSingleZoneMappingSchema = Joi.object({
  arazCityZoneID: idValidation,
});


const addZoneMappingsByHOFSchema = Joi.object({
  arazCityZoneID: idValidation.required(),
  arazCityID: idValidation.required(),
  miqaatID: idValidation.required(),
  mappings: Joi.array().items(stringValidation).min(1).required(),
});
const addZoneMappingsByJammatSchema = Joi.object({
  arazCityZoneID: idValidation.required(),
  arazCityID: idValidation.required(),
  miqaatID: idValidation.required(),
  jamaatIDs:Joi.array().items(idValidation).min(1).required(),
  jamiatID:idValidation
});



const deleteZoneMappingSchema = Joi.object({
  id: idValidation.required(),
});

module.exports = {
  getZoneMappingSchema,
  getSingleZoneMappingSchema,
  addZoneMappingsByHOFSchema,
  addZoneMappingsByJammatSchema,
  deleteZoneMappingSchema,
  getSingleZoneMappingReportSchema,
  getArazCityWiseJamaatAndJamiatSchema
};
