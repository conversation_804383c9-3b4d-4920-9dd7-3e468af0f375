const { Schema, model } = require("mongoose");

const functionSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    priority: {
      type: Number,
      required: false,
    },
    status: {
      type: Boolean,
      required: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const GlobalFunction = model("GlobalFunction", functionSchema);

module.exports = { GlobalFunction };
