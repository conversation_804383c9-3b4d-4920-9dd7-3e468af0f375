const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON>rror,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  NOT_FOUND,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { KGUser } = require("../models");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const { CRYPTO_SECRET, SYSTEM_ROLES } = require("../../../constants");
const CryptoJS = require("crypto-js");
const {
  redisCacheKeys,
  setCache,
  getCache,
} = require("../../../utils/redis.cache");
const {
  isActiveMiqaatAndArazCity,
} = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getEncryptedSystemRole = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;
  const userID = req.user._id;
  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.SYSTEM_ROLES}:${miqaatID}:${arazCityID}:${userID}`;

  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Permissions", data, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let kgUserData = await KGUser.aggregate([
    {
      $match: {
        _id: toObjectId(userID),
      },
    },
    {
      $addFields: {
        filteredMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                as: "miqaat",
                cond: {
                  $and: [
                    { $eq: ["$$miqaat.miqaatID", toObjectId(miqaatID)] },
                    { $eq: ["$$miqaat.arazCityID", toObjectId(arazCityID)] },
                    { $eq: ["$$miqaat.isActive", true] },
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $match: {
        filteredMiqaat: { $ne: null },
      },
    },
    {
      $lookup: {
        from: "systemroles",
        localField: "filteredMiqaat.cityRoleID",
        foreignField: "_id",
        as: "systemRole",
      },
    },
    {
      $unwind: {
        path: "$systemRole",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        "systemRole._id": 1,
        "systemRole.uniqueName": 1,
        "systemRole.modules": 1,
      },
    },
    { $sort: { _id: -1 } },
  ]);

  if (isEmpty(kgUserData)) {
    return apiError(NOT_FOUND, "User", null, res);
  }

  let systemRole = {
    systemRoleName: kgUserData[0]?.systemRole?.uniqueName,
    modules: kgUserData[0]?.systemRole?.modules,
  };

  let systemRoleID = {
    systemRoleID: kgUserData[0]?.systemRole?._id,
  };

  if (isEmpty(systemRole)) {
    return apiError(NOT_FOUND, "Permissions in City", null, res);
  }

  let encryptedSystemRole = CryptoJS.AES.encrypt(
    JSON.stringify(systemRole),
    CRYPTO_SECRET
  ).toString();
  let encryptedSystemRoleID = CryptoJS.AES.encrypt(
    JSON.stringify(systemRoleID),
    CRYPTO_SECRET
  ).toString();

  apiResponse(
    FETCH,
    "Permissions",
    { encryptedSystemRole, encryptedSystemRoleID },
    res
  );
  await setCache(cachekey, { encryptedSystemRole, encryptedSystemRoleID });
});

module.exports = {
  getEncryptedSystemRole,
};
