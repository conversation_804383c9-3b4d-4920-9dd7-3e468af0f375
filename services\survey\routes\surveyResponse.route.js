const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");
const { addSurveyResponse, getSurveyResponse, getSurveys, getSingleSurvey, uploadFilesInSurvey, getSurveyFiles } = require("../controllers/surveyResponse.controller");
const { createSurveyResponseSchema, getSurveyResponseSchema } = require("../validations/surveyResponse.validation");

const { upload } = require("../../../middlewares/multer.middleware");
const fileUpload = upload("survey-response-files");


router.post("/get/add", validate(createSurveyResponseSchema, "body"), addSurveyResponse);

router.post("/get/upload/:id",fileUpload.array("files"),uploadFilesInSurvey)

router.post("/get/file",getSurveyFiles)

router.get("/get/surveys", getSurveys); 

router.get("/get/:id", validate(getSurveyResponseSchema, "params"), getSingleSurvey);
router.get("/get/response/:id", validate(getSurveyResponseSchema, "params"), getSurveyResponse);

module.exports = router;
