const { validate } = require("../../../middlewares/validation.middleware");

const {
  getAllSystemRoles,
  addSystemRole,
  getSingleSystemRole,
  editSystemRole,
  deleteSystemRole,
  getEncryptedSystemRole,
} = require("../controllers/systemRole.controller");

const {
  addSystemRoleSchema,
  getSingleSystemRoleSchema,
  editSystemRoleSchema,
  deleteSystemRoleSchema,
  getEncryptedSystemRoleSchema,
} = require("../validations/systemRole.validation");

const router = require("express").Router();

router.get("/get", getAllSystemRoles);

router.post("/add", validate(addSystemRoleSchema, "body"), addSystemRole);

router.get(
  "/get/:id",
  validate(getSingleSystemRoleSchema, "params"),
  getSingleSystemRole
);

router.put("/edit", validate(editSystemRoleSchema, "body"), editSystemRole);

router.delete(
  "/delete/:id",
  validate(deleteSystemRoleSchema, "params"),
  deleteSystemRole
);

module.exports = router;
