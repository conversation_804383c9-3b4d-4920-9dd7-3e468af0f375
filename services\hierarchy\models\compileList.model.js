const { Schema, model } = require("mongoose");

const departmentSchema = new Schema(
  {
    departmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: true,
    },
    kgUsers: [
      { 
        type: Schema.Types.ObjectId, 
        ref: "KGUser",
      }
    ],
    existingKgUsers: {
      type: Number,
      default: 0,
    },
    quota: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true, _id: false }
);

const CompileListSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    approvedForRaza: {
      type: Boolean,
      default: false,
    },
    departments: [departmentSchema],
  },
  { timestamps: true }
);

const CompileList = model("CompileList", CompileListSchema);

module.exports = {
  CompileList
}