const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
  emailValidation,
  arrayIdValidation,
  arrayStringValidation,
} = require("../../../utils/validator.util");
const { deviceMeta } = require("../models/kgUser.model");

const addEditKGUserSchema = Joi.object({
  // id: idValidation.optional().allow(""),
  name: stringValidation,
  // LDName: stringValidation.optional().allow(""),
  ITSID: stringValidation,
  // email: emailValidation,
  // phone: stringValidation.optional().allow(""),
  // whatsapp: stringValidation.optional().allow(""),
  status: stringValidation,
  consentRequired: booleanValidation,
  isExists: booleanValidation.optional(),
  hierarchyPositionID: idValidation.label("Hierarchy Position"),

  kgGroupID: idValidation.allow("").optional(),
  kgTypeID: idValidation.allow("").optional(),
  jamiatID: idValidation,
  jamaatID: idValidation,
  arazCityID: idValidation,
  miqaatID: idValidation,
  arazCityZoneID: idValidation.allow("").optional(),
  departmentID: idValidation.allow("").optional(),
  functionID: idValidation.allow("").optional(),
  otherFunction: stringValidation.allow("").optional(),
  isInternationalPlugin: booleanValidation.optional(),
}).unknown();

const getSingleKGUserSchema = Joi.object({
  id: idValidation,
  arazCityID: idValidation,
  miqaatID: idValidation,
});

const deleteKGUserSchema = Joi.object({
  id: idValidation,
  arazCity: idValidation,
  miqaat: idValidation,
});

const deleteBulkKGUserSchema = Joi.object({
  ITSIDs: arrayStringValidation,
  arazCityID: idValidation,
  miqaatID: idValidation,
});

const getKGUsersSchema = Joi.object({
  arazCity: idValidation.optional().allow(""),
  miqaat: idValidation.optional().allow(""),
  kgIDs: arrayIdValidation.optional(),
});

const getKGUsersByPaginationSchema = Joi.object({
  arazCity: idValidation.optional().allow(""),
  miqaat: idValidation.optional().allow(""),
  kgIDs: arrayIdValidation.optional(),
}).unknown();

const importITSUsersSchema = Joi.object({
  ITS_IDs: Joi.array().items(stringValidation.optional()).optional(),
});

const assignKGUserSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  existsInDB: Joi.array().items(Joi.object().unknown()).optional(),
  notExistsInDB: Joi.array().items(Joi.object().unknown()).optional(),
}).unknown();

const uploadKGUserSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  users: Joi.array().items(Joi.object({
    ITSID: stringValidation,
    department: stringValidation,
    zone: stringValidation,
    designation: stringValidation,
  }).unknown()).optional(),
}).unknown();

const addOneSignalDeviceSchema = Joi.object({
  deviceID: stringValidation.allow(""),
  deviceType: stringValidation.valid(...Object.values(deviceMeta.allTypes)),
  appVersion: Joi.when("deviceType", {
    is: Joi.valid(...deviceMeta.appTypes),
    then: stringValidation,
    otherwise: Joi.forbidden(),
  }),
  appOSVersion: Joi.when("deviceType", {
    is: Joi.valid(...deviceMeta.appTypes),
    then: stringValidation,
    otherwise: Joi.forbidden(),
  }),
  webCMSVersion: Joi.when("deviceType", {
    is: Joi.valid(...deviceMeta.webTypes),
    then: stringValidation,
    otherwise: Joi.forbidden(),
  }),
  webBrowserType: Joi.when("deviceType", {
    is: Joi.valid(...deviceMeta.webTypes),
    then: stringValidation,
    otherwise: Joi.forbidden(),
  }),
});

module.exports = {
  addEditKGUserSchema,
  getSingleKGUserSchema,
  deleteKGUserSchema,
  getKGUsersSchema,
  importITSUsersSchema,
  assignKGUserSchema,
  uploadKGUserSchema,
  addOneSignalDeviceSchema,
  getKGUsersByPaginationSchema,
  deleteBulkKGUserSchema,
};
