const router = require("express").Router();

const { upload } = require("../../../middlewares/multer.middleware");
const { validate } = require("../../../middlewares/validation.middleware");
const {
  importDataFromExcel,
  syncUserRazaStatus,
  importAndSyncRazaStatus,
} = require("../controllers/razaMapping.controller");
const { importDataFromExcelSchema, syncUserRazaStatusSchema } = require("../validations/razaMapping.validation");
const fileUpload = upload("raza-file");

router.post("/upload", fileUpload.single("file"),validate(importDataFromExcelSchema, "body") , importDataFromExcel);
router.post("/upload-and-sync", fileUpload.single("file"),validate(importDataFromExcelSchema, "body") , importAndSyncRazaStatus);
router.post("/sync",validate(syncUserRazaStatusSchema, "body") , syncUserRazaStatus);

module.exports = router;
