const mongoose = require("mongoose");
const { SurveyForm, SurveyResponse } = require("../models");
const { ObjectId } = mongoose.Types;
const constants = require("../../../constants");
const { toObjectId } = require("../../../utils/misc.util");

const getSurveysForUser = async (req) => {
  try {
    const user = req.user;

    const allSurveys = await SurveyForm.find({ isActive: true })
      .populate("questions")
      .sort({ createdAt: -1 })
      .lean();

    const activeMiqaats = user.miqaats.filter(
      (m) => m.isActive && m.status === "ACCEPTED"
    );

    // Step 6: Filter eligible surveys
    const eligibleSurveys = [];

    for (const survey of allSurveys) {
      const isEligible = await checkSurveyEligibility(
        survey,
        user,
        activeMiqaats,
        survey.recipients
      );

      if (isEligible) {
        // Step 7: Check frequency and previous responses
        const responseCheck = await checkResponseFrequency(survey, user);
        const surveyIsWithinTimeFrame = isWithinSurveyTimeFrame(survey);
        eligibleSurveys.push({
          ...survey,
          isWithinTimeFrame: surveyIsWithinTimeFrame,
          // hasResponded: responseCheck.hasResponded,
          // responseId: responseCheck.hasResponded
          //   ? responseCheck.responseId
          //   : null,
          hasResponded: false,
          response: null,
        });
      }
    }

    if (!eligibleSurveys.length) {
      return null;
    }

    // Sort by creation date (newest first)
    eligibleSurveys.sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
    );

    return eligibleSurveys;
  } catch (error) {
    console.error("Error in getSurveys:", error);
    return null;
  }
};

const checkResponseFrequency = async (survey, user) => {
  const { frequency } = survey;

  if (!frequency) return { hasResponded: false, responseId: null }; // No frequency restriction

  // Get existing responses for this survey and user
  const existingResponses = await SurveyResponse.find({
    surveyID: survey._id,
    userID: user._id,
  })
    .select("createdAt")
    .sort({ createdAt: -1 }); // Sort by newest first

  switch (frequency.toLowerCase()) {
    case "once":
      // User can respond only once
      return {
        hasResponded: existingResponses.length > 0,
        responseId:
          existingResponses.length > 0 ? existingResponses[0]._id : null,
      };

    case "daily":
      // User can respond once per day
      const today = new Date();
      const startOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate()
      );
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const todayResponses = existingResponses.filter((response) => {
        const responseDate = new Date(response.createdAt);
        return responseDate >= startOfDay && responseDate < endOfDay;
      });

      return {
        hasResponded: todayResponses.length > 0,
        responseId: todayResponses.length > 0 ? todayResponses[0]._id : null,
      };

    case "weekly":
      // User can respond once per week
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Start of current week (Sunday)
      startOfWeek.setHours(0, 0, 0, 0);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 7);

      const weekResponses = existingResponses.filter((response) => {
        const responseDate = new Date(response.createdAt);
        return responseDate >= startOfWeek && responseDate < endOfWeek;
      });

      return {
        hasResponded: weekResponses.length > 0,
        responseId: weekResponses.length > 0 ? weekResponses[0]._id : null,
      };

    case "monthly":
      // User can respond once per month
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);

      const monthResponses = existingResponses.filter((response) => {
        const responseDate = new Date(response.createdAt);
        return responseDate >= startOfMonth && responseDate < endOfMonth;
      });

      return {
        hasResponded: monthResponses.length > 0,
        responseId: monthResponses.length > 0 ? monthResponses[0]._id : null,
      };

    default:
      return { hasResponded: false, responseId: null };
  }
};

// Helper function to check survey eligibility
const checkSurveyEligibility = async (
  survey,
  user,
  activeMiqaats,
  recipients
) => {
  // Check direct assignment by system role
  if (
    recipients.systemRoleIDs &&
    recipients.systemRoleIDs.some((roleID) => roleID.equals(user.systemRoleID) && !user.treatAsCityUser)
  ) {
    return true;
  }

  // Check criteria-based eligibility
  if (recipients.criteria && recipients.criteria.length > 0) {
    for (const criteria of recipients.criteria) {
      const isEligibleForCriteria = checkCriteriaMatch(
        criteria,
        user,
        activeMiqaats
      );
      if (isEligibleForCriteria) {
        return true;
      }
    }
  }

  return false;
};

// Helper function to check criteria match
const checkCriteriaMatch = (criteria, user, activeMiqaats) => {
  const { recipientType } = criteria;

  switch (recipientType) {
    case "BY_ITS":
      return criteria.ITSIDs && criteria.ITSIDs.includes(user.ITSID);

    case "ALL_IN_HIERARCHY":
      // Check if user has the required position in any qualifying miqaat
      return activeMiqaats.some((miqaat) => {
        // First check if this miqaat matches the context constraints
        if (
          criteria.miqaatID &&
          !miqaat.miqaatID.equals(toObjectId(criteria.miqaatID))
        )
          return false;
        if (
          criteria.arazCityID &&
          !miqaat.arazCityID.equals(toObjectId(criteria.arazCityID))
        )
          return false;

        // Then check if position matches within this same miqaat
        if (criteria.positionIDs && criteria.positionIDs.length > 0) {
          return criteria.positionIDs.includes(miqaat.hierarchyPositionID);
        }
        return true; // No position restriction, miqaat context is sufficient
      });

    case "ALL_IN_DEPARTMENT":
      // Check if user is in the required department in any qualifying miqaat
      return activeMiqaats.some((miqaat) => {
        // First check miqaat context constraints
        if (
          criteria.miqaatID &&
          !miqaat.miqaatID.equals(toObjectId(criteria.miqaatID))
        )
          return false;
        if (
          criteria.arazCityID &&
          !miqaat.arazCityID.equals(toObjectId(criteria.arazCityID))
        )
          return false;

        // Then check if department matches within this same miqaat
        return (
          criteria.departmentIDs &&
          criteria.departmentIDs.includes(miqaat.departmentID)
        );
      });

    case "ALL_DEPARTMENT_HOD":
      // Check if user is HOD of the required department in any qualifying miqaat
      return activeMiqaats.some((miqaat) => {
        // First check miqaat context constraints
        if (
          criteria.miqaatID &&
          !miqaat.miqaatID.equals(toObjectId(criteria.miqaatID))
        )
          return false;
        if (
          criteria.arazCityID &&
          !miqaat.arazCityID.equals(toObjectId(criteria.arazCityID))
        )
          return false;

        // Check BOTH department and HOD position in the SAME miqaat
        const isDepartmentMatch =
          criteria.departmentIDs &&
          criteria.departmentIDs.includes(miqaat.departmentID);

        const hodPositionId =
          criteria.hodPositionID ||
          constants.HIERARCHY_POSITIONS.HOD ||
          "HOD_POSITION_ID";
        const isHOD = miqaat.hierarchyPositionID === hodPositionId;

        // Both conditions must be true for the SAME miqaat
        return isDepartmentMatch && isHOD;
      });

    case "ALL_IN_ZONE":
      // Check if user is in the required zone in any qualifying miqaat
      return activeMiqaats.some((miqaat) => {
        // First check miqaat context constraints
        if (
          criteria.miqaatID &&
          !miqaat.miqaatID.equals(toObjectId(criteria.miqaatID))
        )
          return false;
        if (
          criteria.arazCityID &&
          !miqaat.arazCityID.equals(toObjectId(criteria.arazCityID))
        )
          return false;

        // Then check if zone matches within this same miqaat
        return (
          criteria.zoneIDs && criteria.zoneIDs.includes(miqaat.arazCityZoneID)
        );
      });

    case "ALL_ZONE_HOD":
      // Check if user is HOD of the required zone in any qualifying miqaat
      return activeMiqaats.some((miqaat) => {
        // First check miqaat context constraints
        if (
          criteria.miqaatID &&
          !miqaat.miqaatID.equals(toObjectId(criteria.miqaatID))
        )
          return false;
        if (
          criteria.arazCityID &&
          !miqaat.arazCityID.equals(toObjectId(criteria.arazCityID))
        )
          return false;

        // Check BOTH zone and HOD position in the SAME miqaat
        const isZoneMatch =
          criteria.zoneIDs && criteria.zoneIDs.includes(miqaat.arazCityZoneID);

        const hodPositionId =
          criteria.hodPositionID ||
          constants.HIERARCHY_POSITIONS.HOD ||
          "HOD_POSITION_ID";
        const isHOD = miqaat.hierarchyPositionID === hodPositionId;

        // Both conditions must be true for the SAME miqaat
        return isZoneMatch && isHOD;
      });

    case "CUSTOM":
      // Check if ALL criteria are met within a SINGLE miqaat
      return activeMiqaats.some((miqaat) => {
        // First check miqaat context constraints
        if (
          criteria.miqaatID &&
          !miqaat.miqaatID.equals(toObjectId(criteria.miqaatID))
        )
          return false;
        if (
          criteria.arazCityID &&
          !miqaat.arazCityID.equals(toObjectId(criteria.arazCityID))
        )
          return false;

        // ALL criteria must be satisfied within THIS SINGLE miqaat
        const miqaatChecks = [
          // Department check - must match in this miqaat
          !criteria.departmentIDs?.length ||
            criteria.departmentIDs.includes(miqaat.departmentID),

          // Function check - must match in this miqaat
          !criteria.functionIDs?.length ||
            criteria.functionIDs.includes(miqaat.functionID),

          // Zone check - must match in this miqaat
          !criteria.zoneIDs?.length ||
            criteria.zoneIDs.includes(miqaat.arazCityZoneID),

          // Position check - must match in this miqaat
          !criteria.positionIDs?.length ||
            criteria.positionIDs.includes(miqaat.hierarchyPositionID),

          // KG Group check - must match in this miqaat
          !criteria.kgGroupIDs?.length ||
            criteria.kgGroupIDs.includes(miqaat.kgGroupID),

          // KG Type check - must match in this miqaat
          !criteria.kgTypeIDs?.length ||
            criteria.kgTypeIDs.includes(miqaat.kgTypeID),
        ];

        // User-level checks (independent of miqaat)
        const userChecks = [
          // ITS check - this is user-level, not miqaat-specific
          !criteria.ITSIDs?.length || criteria.ITSIDs.includes(user.ITSID),
        ];

        // ALL miqaat-specific criteria must be met in this single miqaat
        // AND all user-level criteria must be met
        return (
          miqaatChecks.every((check) => check === true) &&
          userChecks.every((check) => check === true)
        );
      });

    case "PMO":
      // Check if user has PMO position in any qualifying miqaat
      return activeMiqaats.some((miqaat) => {
        // First check miqaat context constraints
        if (
          criteria.miqaatID &&
          !miqaat.miqaatID.equals(toObjectId(criteria.miqaatID))
        )
          return false;
        if (
          criteria.arazCityID &&
          !miqaat.arazCityID.equals(toObjectId(criteria.arazCityID))
        )
          return false;

        // Check if user has PMO position in this miqaat
        const pmoPositionIds = constants.HIERARCHY_POSITIONS.PMO || [];
        return pmoPositionIds.some(
          (pmoId) => miqaat.hierarchyPositionID?.toString() === pmoId.toString()
        );
      });

    default:
      return false;
  }
};

const isWithinSurveyTimeFrame = (survey) => {
  if (survey.alwaysAvailable) {
    return true;
  }

  const now = new Date();
  const currentTimeInMinutes = now.getHours() * 60 + now.getMinutes();

  if (survey.startTime && survey.endTime) {
    const start = new Date(Number(survey.startTime)); // convert string to number
    const end = new Date(Number(survey.endTime));

    const startTimeInMinutes = start.getHours() * 60 + start.getMinutes();
    const endTimeInMinutes = end.getHours() * 60 + end.getMinutes();

    // Handle overnight case like 10 PM to 2 AM
    if (startTimeInMinutes > endTimeInMinutes) {
      return (
        currentTimeInMinutes >= startTimeInMinutes ||
        currentTimeInMinutes <= endTimeInMinutes
      );
    } else {
      return (
        currentTimeInMinutes >= startTimeInMinutes &&
        currentTimeInMinutes <= endTimeInMinutes
      );
    }
  }

  return true;
};

module.exports = {
  getSurveysForUser,
  checkSurveyEligibility,
  checkCriteriaMatch,
  isWithinSurveyTimeFrame,
};
