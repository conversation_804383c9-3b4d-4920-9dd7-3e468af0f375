const express = require("express");
const router = express.Router();

const waazVenueTypeRoute = require("./waazVenueType.route");
const waazVenueSuitabilityRoute = require("./waazVenueSuitability.route");
const waazVenuePriorityRoute = require("./waazVenuePriority.route");
const mawaidVenueTypeRoute = require("./mawaidVenueType.route");
const mawaidVenueSuitabilityRoute = require("./mawaidVenueSuitability.route");
const waazSeatingCapacities = require("./waazSeatingCapacity.route");

router.use("/waaz-venue-type", waazVenueTypeRoute);
router.use("/waaz-venue-suitability", waazVenueSuitabilityRoute);
router.use("/waaz-venue-priority", waazVenuePriorityRoute);
router.use("/mawaid-venue-type", mawaidVenueTypeRoute);
router.use("/mawaid-venue-suitability", mawaidVenueSuitabilityRoute);
router.use("/waaz-seating-capacity", waazSeatingCapacities);


const miqaatRoute = require("./miqaat.route");
const departmentRoute = require("./department.route");
const arazCityRoute = require("./arazCity.route");
const kgTypeRoute = require("./kgType.route");
const kgGroupRoute = require("./kgGroup.route");
const functionRoute = require("./function.route");
const hierarchyPositionRoute = require("./hierarchyPosition.route");
const smeMappingRoute = require("./smeMapping.route");
const notificationRoute = require("./notification.route");

router.use("/miqaat", miqaatRoute);
router.use("/department", departmentRoute);
router.use("/araz-city", arazCityRoute);
router.use("/kg-type", kgTypeRoute);
router.use("/kg-group", kgGroupRoute);
router.use("/function", functionRoute);
router.use("/kg-hierarchy-position", hierarchyPositionRoute);
router.use("/sme-mapping", smeMappingRoute);
router.use("/notification", notificationRoute);



const guideRoute = require("./guide.route");

router.use("/guide", guideRoute);

//department quota

const departmentQuotaRoute = require("./departmentQuota.route");
router.use("/department-quota", departmentQuotaRoute);


const eventLogs = require("./eventLogs.route");
router.use("/event-logs", eventLogs);


module.exports = router;