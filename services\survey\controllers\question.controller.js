// modules/survey/controllers/question.controller.js

const { Question } = require("../models/question.model"); // Assuming you have a Question model
const { apiResponse, apiError } = require("../../../utils/api.util");
const {
  CUSTOM_ERROR,
  FETCH,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  DELETE_ERROR,
  NOT_FOUND,
  DELETE_SUCCESS,
  CUSTOM_SUCCESS,
} = require("../../../utils/message.util");
const { isEmpty } = require("../../../utils/misc.util");
const { SurveyForm } = require("../models/surveyForm.model");

const getAllQuestions = async (req, res) => {
  try {
    const questions = await Question.find()
      .populate([
        {
          path: "createdBy",
          select: "name ITSID",
        },
        {
          path: "updatedBy",
          select: "name ITSID",
        },
        {
          path: "department",
          select: "name _id",
        },
      ])
      .sort({ createdAt: -1 })
      .lean();
    return apiResponse(FETCH, "Questions", questions, res);
  } catch (error) {
    return apiError(CUSTOM_ERROR, "Failed to fetch questions", null, res);
  }
};

const stripHTML = (html) => {
  return html
    .replace(/<[^>]*>/g, "")
    .replace(/\s+/g, " ")
    .trim();
};

const getSimilarQuestions = async (req, res) => {
  const { title } = req.body;

  try {
    // Convert HTML to plain text
    const plainTextTitle = stripHTML(title).toLowerCase();
    const words = plainTextTitle.split(/\s+/).filter(Boolean);

    // Rough pre-filter using regex to reduce DB scan
    const regexQueries = words.map((word) => ({
      title: { $regex: word, $options: "i" },
    }));

    // Fetch potential matches from DB
    const questions = await Question.find({
      $or: regexQueries,
      title: { $ne: title },
    })
      .populate([
        { path: "createdBy", select: "name ITSID" },
        { path: "updatedBy", select: "name ITSID" },
        { path: "department", select: "name _id" },
      ])
      .sort({ createdAt: -1 })
      .lean();

    // Final filtering using pure text match
    const filteredQuestions = questions.filter((q) => {
      const qPlainText = stripHTML(q.title || "").toLowerCase();
      return words.some((word) => qPlainText.includes(word));
    });

    return apiResponse(FETCH, "Questions", filteredQuestions, res);
  } catch (error) {
    console.error("Error in getSimilarQuestions:", error);
    return apiError(CUSTOM_ERROR, "Failed to fetch questions", null, res);
  }
};

const getMyQuestions = async (req, res) => {
  try {
    const questions = await Question.find({ createdBy: req.user._id })
      .populate([
        {
          path: "createdBy",
          select: "name ITSID",
        },
        {
          path: "updatedBy",
          select: "name ITSID",
        },
        {
          path: "department",
          select: "name _id",
        },
      ])
      .sort({ createdAt: -1 })
      .lean();
    return apiResponse(FETCH, "Questions", questions, res);
  } catch (error) {
    return apiError(CUSTOM_ERROR, "Failed to fetch questions", null, res);
  }
};

const addEditQuestion = async (req, res) => {
  try {
    const { id } = req.params;
    const createdBy = req.user ? req.user._id : null;
    const updatedBy = req.user ? req.user._id : null;

    if (!isEmpty(id)) {
      // Update existing question
      const question = await Question.findById(id).lean();
      if (!question) {
        return apiError(NOT_FOUND, "Question", null, res);
      }

      // Check if question exists in any survey
      const existInSurvey = await SurveyForm.countDocuments({
        questions: question._id,
      });

      let updateData = {};

      if (existInSurvey > 0) {
        // Question exists in survey - only allow specific fields to be modified
        const {
          title,
          description,
          required,
          chartType,
          department,
          isActive,
        } = req.body;

        // Check if user is trying to modify restricted fields
        if (
          req.body.questionType &&
          req.body.questionType !== question.questionType
        ) {
          return apiError(
            CUSTOM_ERROR,
            "Question type cannot be modified as this question is linked with a survey",
            null,
            res
          );
        }

        // Compare options by extracting only placeholder and weightage
        if (req.body.options && req.body.options.length >= 0) {
          const bodyOptionsCore = req.body.options.map((opt) => ({
            placeholder: opt.placeholder,
            weightage: opt.weightage,
          }));

          const questionOptionsCore = question.options.map((opt) => ({
            placeholder: opt.placeholder,
            weightage: opt.weightage,
          }));

          const bodyOptionsString = JSON.stringify(bodyOptionsCore);
          const questionOptionsString = JSON.stringify(questionOptionsCore);

          if (bodyOptionsString !== questionOptionsString) {
            return apiError(
              CUSTOM_ERROR,
              "Question options cannot be modified as this question is linked with a survey",
              null,
              res
            );
          }
        }

        if (req.body.otherOptions && req.body.otherOptions.length >= 0) {
          const bodyOptionsCore = req.body.otherOptions.map((opt) => ({
            placeholder: opt.placeholder,
            weightage: opt.weightage,
          }));

          const questionOptionsCore = question.otherOptions.map((opt) => ({
            placeholder: opt.placeholder,
            weightage: opt.weightage,
          }));

          const bodyOptionsString = JSON.stringify(bodyOptionsCore);
          const questionOptionsString = JSON.stringify(questionOptionsCore);

          if (bodyOptionsString !== questionOptionsString) {
            return apiError(
              CUSTOM_ERROR,
              "Question options cannot be modified as this question is linked with a survey",
              null,
              res
            );
          }
        }

        // Build update object with only allowed fields
        updateData = {
          ...(title !== undefined && { title }),
          ...(description !== undefined && { description }),
          ...(required !== undefined && { required }),
          ...(chartType !== undefined && { chartType }),
          ...(department !== undefined && { department }),
          ...(isActive !== undefined && { isActive }),
          updatedBy,
        };
      } else {
        // Question doesn't exist in survey - allow all fields to be modified
        const {
          title,
          description,
          questionType,
          options,
          otherOptions,
          required,
          chartType,
          department,
          isActive,
        } = req.body;

        updateData = {
          ...(title !== undefined && { title }),
          ...(description !== undefined && { description }),
          ...(questionType !== undefined && { questionType }),
          ...(options !== undefined && { options }),
          ...(otherOptions !== undefined && { otherOptions }),
          ...(required !== undefined && { required }),
          ...(chartType !== undefined && { chartType }),
          ...(department !== undefined && { department }),
          ...(isActive !== undefined && { isActive }),
          updatedBy,
        };
      }

      const updatedQuestion = await Question.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      });

      if (!updatedQuestion) {
        return apiError(NOT_FOUND, "Question", null, res);
      }

      return apiResponse(UPDATE_SUCCESS, "Question", updatedQuestion, res);
    } else {
      // Create new question
      const newQuestion = new Question({ ...req.body, createdBy });
      await newQuestion.save();
      return apiResponse(ADD_SUCCESS, "Question", newQuestion, res);
    }
  } catch (error) {
    console.error("Error in addEditQuestion:", error);
    return apiError(
      CUSTOM_ERROR,
      "Failed to process question",
      error.message,
      res
    );
  }
};

const getSingleQuestion = async (req, res) => {
  try {
    const question = await Question.findById(req.params.id).populate([
      {
        path: "createdBy",
        select: "name ITSID _id",
      },
      {
        path: "updatedBy",
        select: "name ITSID _id",
      },
      {
        path: "department",
        select: "name _id",
      },
    ]);
    if (!question) {
      return apiError(NOT_FOUND, "Question", null, res);
    }
    return apiResponse(FETCH, "Question", question, res);
  } catch (error) {
    return apiError(CUSTOM_ERROR, "Failed to fetch question", null, res);
  }
};

const activateQuestion = async (req, res) => {
  try {
    const question = await Question.findById(req.params.id);
    if (!question) {
      return apiError(NOT_FOUND, "Question", null, res);
    }

    question.isActive = !question.isActive;
    await question.save();

    return apiResponse(
      CUSTOM_SUCCESS,
      "Question Status Updated Successfully",
      question,
      res
    );
  } catch (error) {
    return apiError(DELETE_ERROR, "Question", null, res);
  }
};

const deleteQuestion = async (req, res) => {
  try {
    const existInSurvey = await SurveyForm.countDocuments({
      questions: req.params.id,
    });
    if (existInSurvey > 0) {
      return apiError(
        CUSTOM_ERROR,
        "This question is linked with a survey and cannot be deleted.",
        null,
        res
      );
    }
    const deletedQuestion = await Question.findByIdAndDelete(req.params.id);
    if (!deletedQuestion) {
      return apiError(NOT_FOUND, "Question", null, res);
    }
    return apiResponse(DELETE_SUCCESS, "Question", deletedQuestion, res);
  } catch (error) {
    return apiError(DELETE_ERROR, "Question", null, res);
  }
};

module.exports = {
  getAllQuestions,
  addEditQuestion,
  getSingleQuestion,
  deleteQuestion,
  getMyQuestions,
  activateQuestion,
  getSimilarQuestions,
};
