const Joi = require("joi");
const {
  stringValidation,
  idValidation,
  numberValidation,
} = require("../../../utils/validator.util");

const addWaazVenueSchema = Joi.object({
  name: stringValidation,
  arazCityZoneID: idValidation,
  status: stringValidation.valid("active", "inactive", "cancel").optional(),
  approvalStatus: Joi.optional(),
  mawaidVenues: Joi.array().items(Joi.string()).optional().allow(null, ''),
  arazCityID: idValidation,
  miqaatID: idValidation,
  waazVenueTypeID: idValidation,
  waazVenueSuitabilityID: idValidation,
  waazVenuePriorityID: idValidation,
  waazVenuPolylineArea: numberValidation.optional(),
  waazSeatingCapacity: numberValidation,
  finalizedWaazSeatingCapacity: numberValidation.optional(),
  remarks: stringValidation.optional().allow(""),
});

const getSingleWaazVenueSchema = Joi.object({
  id: idValidation,
});

const getAllWaazVenueSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  arazCityZoneID: idValidation.optional(),
});

const editWaazVenueSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
  arazCityZoneID: idValidation,
  status: stringValidation.valid("active", "inactive", "cancel"),
  approvalStatus: Joi.optional().valid("under-review", "approved"),
  mawaidVenues: Joi.array().items(Joi.string()).optional().allow(null, ''),
  waazVenueTypeID: idValidation,
  waazVenueSuitabilityID: idValidation,
  arazCityID: idValidation,
  miqaatID: idValidation,
  waazVenuePriorityID: idValidation,
  waazVenuPolylineArea: numberValidation.optional(),
  waazSeatingCapacity: numberValidation,
  finalizedWaazSeatingCapacity: numberValidation.optional(),
  remarks: stringValidation.optional().allow(""),
});

const deleteWaazVenueSchema = Joi.object({
  id: idValidation,
});

const activateWaazVenueSchema = Joi.object({
  id: idValidation,
  status: stringValidation,
});
const approveWaazVenueSchema = Joi.object({
  id: idValidation,
  approvalStatus: stringValidation,
});
const finalCapacityWaazVenueSchema = Joi.object({
  id:idValidation,
  finalCapacity: Joi.number().required(),
});

const statusTypes = ["DRAFT", "APPROVED", "UNLOCKED"];

const uploadFileSchema = Joi.object({
  functionID: idValidation,
  arazCityZoneID: idValidation,
  waazVenueID: idValidation,
  miqaatID:idValidation,
  arazCityID:idValidation
});

const updateFileStatusSchema = Joi.object({
  fileId: idValidation,
  status: Joi.string()
    .valid(...statusTypes)
    .required(),
});

const getFilesSchema = Joi.object({
  functionID: idValidation,
  arazCityZoneID: idValidation,
  waazVenueID: idValidation,
  miqaatID:idValidation,
  arazCityID:idValidation,
});

const getDownloadURLSchema = Joi.object({
  fileKey: stringValidation,
});

const deleteArazCityZoneWaazVenueFileSchema = Joi.object({
  id: idValidation,
  function: stringValidation,
});
const getMasterFilesSchema = Joi.object({
  arazCityZoneID: idValidation,
  miqaatID:idValidation,
  arazCityID:idValidation
});

module.exports = {
  addWaazVenueSchema,
  getSingleWaazVenueSchema,
  editWaazVenueSchema,
  deleteWaazVenueSchema,
  getAllWaazVenueSchema,
  activateWaazVenueSchema,
  approveWaazVenueSchema,
  finalCapacityWaazVenueSchema,
  uploadFileSchema,
  updateFileStatusSchema,
  getMasterFilesSchema,
  getDownloadURLSchema,
  deleteArazCityZoneWaazVenueFileSchema,
  getFilesSchema,
};
