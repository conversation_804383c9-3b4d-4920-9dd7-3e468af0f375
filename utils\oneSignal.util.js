const OneSignal = require('onesignal-node');
const constants = require('../constants');
const { KGUser } = require('../services/hierarchy/models');
const { NotificationLog } = require('../services/communication/models');
const { isEmpty } = require('./misc.util');

const notificationTemplate = {
  MESSAGE: ({title, message, messageID, miqaatID, arazCityID}) => ({
    title,
    message,
    // app_url: `ams://Chat/${messageID}/${miqaatID}/${arazCityID}`,
    app_url: `ams://Chat/${messageID}`,
    web_url: ``,
  }),
  NOTIFICATION: ({title, message, notificationID}) => ({
    title,
    message,
    // app_url: `ams://NotificationDetail/${notificationID}`,
    app_url: `ams://NotificationDetail/:notificationID`,
    web_url: ``,
  }),
  NEW_MESSAGE: {
    title: "New Message Alert!",
    message:
      "You’ve received a new message!",
    app_url: `${""}seller://ViewOrder/{id}/true`,
    web_url: ``,
  },
  MESSAGE_REPLY: {
    title: "Message Reply Alert!",
    message:
      "You’ve received a reply on your message!",
    app_url: `${""}seller://ViewOrder/{id}/true`,
    web_url: ``,
  },
};

// Function to send notification
const sendPushNotifications = async (data, template) => {
  const { messageID, replyID, messageType, recipients } = data
  const users = await KGUser.find({_id: { $in: recipients }}).select("_id appDetails webDetails")
  const notificationLogData = {
    title: template.title,
    message: template.message,
    messageID,
    replyID,
    messageType,
    recipients: []
  }

  const notificationLog = await NotificationLog.create(notificationLogData)
  const notificationID = notificationLog._id.toString()
  
  for(const user of users) {
    const { _id: userID, appDetails, webDetails } = user
    const playersData = []
    const userData = {
      userID,
      devices: []
    }

    preparePlayerDetails(appDetails, userData, playersData, true)
    preparePlayerDetails(webDetails, userData, playersData)    

    for(const playerData of playersData) {
      const { playerId, playerDetails } = playerData
      const deviceData = {
        deviceType: playerDetails.deviceType ? "MOBILE" : "WEB",
        deviceMeta: playerDetails.deviceType || "CMS",
      }

      const notification = {
        contents: { en: template.message },
        include_player_ids: [playerId],
        headings: { en: template.title },
        app_url: template.app_url.replace(":notificationID", notificationID),
        web_url: template.web_url || "",
        ios_badgeType: "Increase",
        ios_badgeCount: 1
      };
    
      const oneSignalClient = new OneSignal.Client(constants.ONE_SIGNAL_APP_ID, constants.ONE_SIGNAL_API_KEY_ID);
    
      try {
        const response = await oneSignalClient.createNotification(notification);

        if(response.statusCode === 200) {
          deviceData.messageStatus = "Notification sent successfully"
          deviceData.success=true;
          deviceData.deliveryTime= Date.now();
        }
        // console.log('Notification sent successfully:');
      } catch (error) {
        const errMessage = JSON.parse(error.message).errors[0]
        console.error('Error sending notification:', errMessage);
        deviceData.success=false;
        deviceData.messageStatus = error.message
      }

      userData.devices.push(deviceData)
    }

    notificationLogData.recipients.push(userData)
  }

  // await createNotificationLog(notificationLogData)
  await updateNotificationLog(notificationLogData, notificationID)
}

const preparePlayerDetails = (playerDetails, userData, playersData, isApp = false) => {
  if(isEmpty(playerDetails)) {
    userData.devices.push({
      deviceType: isApp ? "MOBILE" : "WEB",
      devuiceMeta: isApp ? playerDetails.deviceType : "CMS",
      messageStatus: "Device not found"
    })
  } else {
    playersData.push({
      playerId: playerDetails.deviceID,
      playerDetails: playerDetails
    })
  }
}

const createNotificationLog = async (data) => {
  const notificationLog = await NotificationLog.create(data)
  if(notificationLog) {
    console.log("Notification log added successfully")
  } else {
    console.log("Couldn't create notification log")
  }
}

const updateNotificationLog = async (data, id) => {
  const notificationLog = await NotificationLog.findByIdAndUpdate(id, { $set: data })
  if(notificationLog) {
    console.log("Notification log updated successfully")
  } else {
    console.log("Couldn't create notification log")
  }
}

module.exports = {
  sendPushNotifications,
  notificationTemplate
}