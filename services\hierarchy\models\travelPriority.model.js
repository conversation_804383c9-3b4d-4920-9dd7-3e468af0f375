const { Schema, model } = require("mongoose");

const TravelPrioritySchema = new Schema({
  name: {
    type: String,
    required: true
  }
}, { timestamps: true })

const priorities = ["P1", "P2", "P3"]

const TravelPriority = model("TravelPriority", TravelPrioritySchema);

const TravelPriorityIfNotExists = async () => {
  const existingPriorities = await TravelPriority.find().select("name");

  const existingPriorityNames = existingPriorities.map((priority) => priority.name);

  const prioritiesToAdd = priorities.filter(
    (priority) => !existingPriorityNames.includes(priority)
  );

  if (prioritiesToAdd.length > 0) {
    await TravelPriority.insertMany(
      prioritiesToAdd.map((priority) => ({ name: priority }))
    );
  }
};
// TravelPriorityIfNotExists();

module.exports = {
  TravelPriority
}