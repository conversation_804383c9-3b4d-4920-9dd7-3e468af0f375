const { ObjectId } = require("mongodb");

const constants = require("../../../constants");
const { KGUser, ArazCity } = require("../../hierarchy/models");
const {
  Message,
  messageTypes,
  MessageType,
  NotificationLog,
  Email,
} = require("../models");
const { applyFilters, applySort } = require("../../../utils/filter.utill");
const {
  uploadFileToS3,
  generatePreSignedURLs,
  generateGetPreSignedURL,
} = require("../../../utils/aws.util");
const {
  apiHandler,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const {
  toObjectId,
  toString,
  isEmpty,
  sanitizeHtml,
} = require("../../../utils/misc.util");
const {
  sendPushNotifications,
  notificationTemplate,
} = require("../../../utils/oneSignal.util");
const {
  NOT_FOUND,
  ADD_SUCCESS,
  FETCH,
  ADD_ERROR,
  CUSTOM_ERROR,
  CUSTOM_SUCCESS,
  DATE_ERROR,
  FORBIDDEN,
  UPDATE_ERROR,
  UPDATE_SUCCESS,
  DELETE_ERROR,
  DELETE_SUCCESS,
} = require("../../../utils/message.util");
const { statusTypes } = require("../models/message.model");
const { messageQueue } = require("../../../utils/queues");
const { sendTemplatedEmail } = require("../../../utils/email.util");
const { EmailLog } = require("../models/emailLog.model");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getMessages = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    page: pageNo,
    limit: limitNo,
    filters,
  } = req.body;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat is not active", null, res);
  }

  const page = !pageNo || pageNo === 0 ? 1 : pageNo;
  const limit = !limitNo || limitNo === 0 ? 50 : limitNo;
  const skip = (page - 1) * limit;
  const userID = req.user._id; // logged-in userID
  let findQuery = {
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
    $or: [
      { sender: userID, "replies.0": { $exists: true } },
      { recipients: userID },
      { CCrecipients: userID },
    ],
    status: { $nin: [statusTypes.SCHEDULED, statusTypes.DELETED] },
  };
  findQuery = applyFilters(findQuery, filters, userID);

  // Select and populate fields for user details
  const kgUserSelectFields =
    "name ITSID logo systemRoleID miqaats.miqaatID miqaats.arazCityID miqaats.arazCityZoneID miqaats.hierarchyPositionID miqaats.departmentID";
  const kgUserPopulateFields = [
    { path: "systemRoleID", select: "name" },
    { path: "miqaats.arazCityID", select: "name showPositionAlias" },
    { path: "miqaats.hierarchyPositionID", select: "name alias" },
    { path: "miqaats.arazCityZoneID", select: "name" },
    { path: "miqaats.departmentID", select: "name" },
  ];

  // Fetch messages and populate the necessary fields
  const existingMessagesCount = await Message.countDocuments(findQuery);
  const existingMessages = await Message.find(findQuery)
    .populate([
      {
        path: "sender",
        select: kgUserSelectFields,
        populate: kgUserPopulateFields,
      },
      {
        path: "messageType",
        select: "_id name",
      },
      // {path: "recipients", select: kgUserSelectFields, populate: kgUserPopulateFields},
      {
        path: "replies.sender",
        select: kgUserSelectFields,
        populate: kgUserPopulateFields,
      },
    ])
    .skip(skip)
    .limit(limit)
    .sort({ updatedAt: -1 })
    .lean();
  if (!existingMessages.length) {
    return apiError(NOT_FOUND, "Messages", null, res);
  }

  let messagesData = existingMessages.map((message) => {
    const { sender, replies } = message;

    const userData = getUserData(userID, sender, message);
    userData["_id"] = message._id;
    userData["subject"] = message.subject;
    userData["status"] = message.status;
    userData["isTopPriority"] = message.isTopPriority;
    userData["scheduledAt"] = message.scheduledAt;

    // fetch latest reply for the message, if any
    const latestReply = replies[replies.length - 1];
    let latestReplyDetails;
    if (latestReply) {
      const data = {
        createdAt: latestReply.createdAt,
        updatedAt: latestReply.updatedAt,
        isReadBy: message.isReadBy,
        miqaatID,
        arazCityID,
      };

      latestReplyDetails = getUserData(userID, latestReply.sender, data);
    }

    // update the userData with that of teh latest's reply
    if (latestReplyDetails) {
      userData["sender"] = latestReplyDetails.sender;
      userData["senderID"] = latestReplyDetails.senderID;
      userData["logo"] = latestReplyDetails.logo;
      userData["position"] = latestReplyDetails.position;
      userData["body"] = latestReplyDetails.body;
      userData["createdAt"] = latestReplyDetails.createdAt;
      userData["updatedAt"] = latestReplyDetails.updatedAt;
    }
    // replace the sender name if the senderID = logged in userID
    if (
      userData.senderID.toString() === userID.toString() ||
      (latestReplyDetails &&
        latestReplyDetails.senderID.toString() === userID.toString())
    ) {
      userData["sender"] = "You";
    }

    return userData;
  });

  const response = {
    count: existingMessagesCount,
    messages: messagesData,
  };

  return apiResponse(FETCH, "Messages", response, res);
});

const getMessage = apiHandler(async (req, res) => {
  const { id: messageID } = req.params;
  const { miqaatID, arazCityID } = req.body;


  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat is not active", null, res);
  }

  const userID = req.user._id; // logged-in userID
  const findQuery = {
    _id: toObjectId(messageID),
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
    // status: { $ne: statusTypes.SCHEDULED }
  };

  // Marked message as read for the logged in user
  await updateMessageReadStatus(messageID, true, userID);

  // Select and populate fields for user details
  const kgUserSelectFields =
    "name ITSID logo systemRoleID miqaats.miqaatID miqaats.arazCityID miqaats.arazCityZoneID miqaats.hierarchyPositionID miqaats.departmentID";
  const kgUserPopulateFields = [
    { path: "systemRoleID", select: "name" },
    { path: "miqaats.arazCityID", select: "name showPositionAlias" },
    { path: "miqaats.hierarchyPositionID", select: "name alias" },
    { path: "miqaats.arazCityZoneID", select: "name" },
    { path: "miqaats.departmentID", select: "name" },
  ];

  const message = await Message.findOne(findQuery)
    .populate([
      {
        path: "sender",
        select: kgUserSelectFields,
        populate: kgUserPopulateFields,
      },
      {
        path: "messageType",
        select: "_id name",
      },
      {
        path: "replies.sender",
        select: kgUserSelectFields,
        populate: kgUserPopulateFields,
      },
    ])
    .lean();
  if (!message) {
    return apiError(NOT_FOUND, "Message", null, res);
  }

  // Prepare message details
  const { sender, replies } = message;
  let userData = getUserData(userID, sender, message);
  userData = { ...message, ...userData };

  const repliesDetail =
    replies.map((user) => {
      const data = {
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        isReadBy: message.isReadBy,
        miqaatID,
        arazCityID,
      };
      data["body"] = user.body;
      const attachments = user?.attachments.map((attachment) => ({
        ...attachment,
        sender: user?.sender?.name,
      }));
      data["attachments"] = attachments;

      const replyDetail = getUserData(userID, user.sender, data);

      return replyDetail;
    }) || [];
  userData["replies"] = repliesDetail;

  return apiResponse(FETCH, "Messages", userData, res);
});

const addMessage = apiHandler(async (req, res) => {
  await createUpdateMessage(req, res);
});

const editMessage = apiHandler(async (req, res) => {
  await createUpdateMessage(req, res, true);
});

const deleteMessage = apiHandler(async (req, res) => {
  const { id: messageID } = req.params;
  const { miqaatID, arazCityID } = req.body;
  const userID = req.user._id; // logged in userID

  const existingMessage = await Message.findOne({
    _id: toObjectId(messageID),
    status: { $ne: statusTypes.DELETED },
  }).select("sender status");
  if (!existingMessage) {
    return apiError(NOT_FOUND, "Message", null, res);
  }
  if (existingMessage.status !== statusTypes.SCHEDULED) {
    return apiError(CUSTOM_ERROR, "Invalid action", null, res ? res : null);
  }
  if (existingMessage.sender.toString() !== userID.toString()) {
    return apiError(FORBIDDEN, "User", null, res);
  }

  const deletedMessage = await Message.findOneAndUpdate(
    {
      _id: toObjectId(messageID),
      miqaatID: toObjectId(miqaatID),
      arazCityID: toObjectId(arazCityID),
    },
    { $set: { status: statusTypes.DELETED } }
  );
  if (!deletedMessage) {
    return apiError(DELETE_ERROR, "Message", null, res);
  }
  apiResponse(DELETE_SUCCESS, "Message", null, res);

  await messageQueue.deleteScheduledMessage(messageID);
});

const addMessageReply = apiHandler(async (req, res) => {
  const { id: messageID } = req.params;

  const message = await Message.findById(messageID);
  if (!message) {
    return apiError(NOT_FOUND, "Message", null, res);
  }
  if (!message.isAllowReply) {
    return apiError(CUSTOM_ERROR, "Reply not allowed", null, res);
  }

  const { miqaatID, arazCityID, body } = req.body;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "ArazCity is not active", null, res);
  }


  const userID = req.user._id; // logged in userID
  const replyData = req.body;
  replyData["sender"] = userID;

  const updatedMessage = await Message.findOneAndUpdate(
    {
      _id: toObjectId(messageID),
      miqaatID: toObjectId(miqaatID),
      arazCityID: toObjectId(arazCityID),
    },
    { $push: { replies: replyData } },
    { new: true }
  );
  if (!updatedMessage) {
    return apiError(ADD_ERROR, "Reply", null, res);
  }

  const userIDs = [...updatedMessage.recipients, updatedMessage.sender];
  const recipients = userIDs.filter(
    (uID) => uID.toString() !== userID.toString()
  );
  await updateMessageReadStatus(messageID, false, recipients, true);
  apiResponse(ADD_SUCCESS, "Reply", null, res);

  const notificationLogData = {
    messageID: updatedMessage._id,
    replyID: updatedMessage.replies[updatedMessage.replies.length - 1]._id,
    messageType: messageTypes.MESSAGE,
    recipients,
  };
  const notificationTemplateData = {
    messageID: updatedMessage._id,
    title: updatedMessage.subject,
    message: sanitizeHtml(body),
    miqaatID,
    arazCityID,
  };
  await sendPushNotifications(
    notificationLogData,
    notificationTemplate.MESSAGE(notificationTemplateData)
  );
});

const getDashboard = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }
  const userID = req.user._id;

  // Fetch logged in user info as per miqaat and araz city
  const kgUserSelectFields =
    "name ITSID logo systemRoleID miqaats.miqaatID miqaats.arazCityID miqaats.arazCityZoneID miqaats.hierarchyPositionID miqaats.departmentID miqaats.KgTypeID miqaats.KgGroupID";

  const kgUserPopulateFields = [
    { path: "systemRoleID", select: "name" },
    { path: "miqaats.miqaatID", select: "name" },
    { path: "miqaats.arazCityID", select: "name showPositionAlias" },
    { path: "miqaats.hierarchyPositionID", select: "name alias" },
    { path: "miqaats.arazCityZoneID", select: "name" },
    { path: "miqaats.departmentID", select: "name" },
    { path: "miqaats.kgTypeID", select: "name" },
    { path: "miqaats.kgGroupID", select: "name" },
  ];

  const userData = await KGUser.findById(userID)
    .populate(kgUserPopulateFields)
    .select(kgUserSelectFields);
  const userMiqaat = userData.miqaats.find(
    (miqaat) =>
      miqaat.miqaatID?._id.toString() === miqaatID.toString() &&
      miqaat.arazCityID?._id.toString() === arazCityID.toString()
  );

  const arazCity = userMiqaat?.arazCityID?.name || "";
  let position = "";
  if (userMiqaat?.hierarchyPositionID) {
    position = userMiqaat?.arazCityID?.showPositionAlias
      ? `${userMiqaat.hierarchyPositionID.alias}`
      : `${userMiqaat.hierarchyPositionID.name}`;
  }
  if (userMiqaat?.arazCityZoneID) {
    position = `${userMiqaat.arazCityZoneID.name} ${position}`;
  }
  if (userMiqaat?.departmentID) {
    position = `${userMiqaat.departmentID.name} ${position}`;
  }

  const userInfoData = {
    miqaat: userMiqaat?.miqaatID?.name || "",
    arazCity,
    zone: userMiqaat?.arazCityZoneID?.name || "",
    kgGroup: userMiqaat?.kgGroupID?.name || "",
    kgType: userMiqaat?.kgTypeID?.name || "",
    hierarchyPosition: position,
  };

  // Fetch unread messages of logged in user
  const messages = await Message.find({
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
    [`isReadBy.${userID.toString()}`]: false,
  });

  const userNotificationData = [
    { count: messages.length, city: arazCity, color: "#690A00" },
  ];

  const response = {
    infoData: userInfoData,
    notificationData: userNotificationData,
  };

  return apiResponse(FETCH, "Dashboard", response, res);
});

const getEmailReport = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    page: pageNo,
    limit: limitNo,
    filters,
  } = req.body;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    null,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat is not active", null, res);
  }

  const userID = req.user._id;
  const page = !pageNo || pageNo === 0 ? 1 : pageNo;
  const limit = !limitNo || limitNo === 0 ? 10 : limitNo;
  const skip = (page - 1) * limit;
  const isSystemUser = !isEmpty(req.user.systemRoleID) ? true : false;
  let findQuery = {
    miqaatID: toObjectId(miqaatID),
    // arazCityID: toObjectId(arazCityID),
    status: { $ne: statusTypes.DELETED },
  };
  let sortQuery = { createdAt: -1 };
  if (!isSystemUser) {
    // findQuery["$or"] = [{ sender: userID }, { recipients: userID }];
  }
  findQuery = applyFilters(findQuery, filters, userID);
  sortQuery = applySort(sortQuery, filters);

  // Select and populate fields for user details
  const kgUserSelectFields =
    "name ITSID logo systemRoleID miqaats.miqaatID miqaats.arazCityID miqaats.arazCityZoneID miqaats.hierarchyPositionID miqaats.departmentID";
  const kgUserPopulateFields = [
    { path: "systemRoleID", select: "name" },
    { path: "miqaats.arazCityID", select: "_id name showPositionAlias" },
    { path: "miqaats.hierarchyPositionID", select: "name alias" },
    { path: "miqaats.arazCityZoneID", select: "name" },
    { path: "miqaats.departmentID", select: "name" },
  ];

  // Fetch messages and populate the necessary fields
  const existingMessagesCount = await Message.countDocuments(findQuery);
  const existingMessages = await Message.find(findQuery)
    .populate([
      {
        path: "sender",
        select: kgUserSelectFields,
        populate: kgUserPopulateFields,
      },
    ])
    .skip(skip)
    .limit(limit)
    .sort(sortQuery)
    .lean();
  if (!existingMessages.length) {
    return apiError(NOT_FOUND, "Messages", null, res);
  }

  // Transform messages into userData format
  let messagesData = existingMessages.map((message) => {
    const { sender, recipients } = message;

    const userData = getUserData(userID, sender, message);
    userData["_id"] = message._id;

    // userData["position"] = userData.position.split("/")[0]
    // userData["subject"] = message.subject
    // userData["recipients"] = recipients

    return {
      _id: message._id,
      subject: message.subject,
      status: message.status,
      from: userData.position.split("/")[0],
      to: "",
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
    };
  });

  const response = {
    count: existingMessagesCount,
    messages: messagesData,
  };

  return apiResponse(FETCH, "Email Report", response, res);
});

const addAttachments = apiHandler(async (req, res) => {
  if (!req.files || req.files.length === 0) {
    return apiResponse(CUSTOM_ERROR, "No files selected", {}, res);
  }

  const awsGroupID = req.body.awsGroupID || new ObjectId();

  const fileDetails = req.files.map((file) => ({
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  }));

  const preSignedURLs = await generatePreSignedURLs(
    "communication",
    awsGroupID,
    fileDetails
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return apiResponse(
      CUSTOM_ERROR,
      "Failed to generate pre-signed URLs",
      {},
      res
    );
  }

  const uploadPromises = preSignedURLs.map(({ fileKey, preSignedURL }, index) =>
    uploadFileToS3(req.files[index], preSignedURL, fileKey)
  );

  const uploadedFiles = (await Promise.all(uploadPromises)).filter(Boolean);

  return apiResponse(
    CUSTOM_SUCCESS,
    "Files Uploaded Successfully",
    { awsGroupID, data: uploadedFiles },
    res
  );
});

const getAttachment = apiHandler(async (req, res) => {
  const { fileKey } = req.body;
  const preSignedURL = await generateGetPreSignedURL(fileKey);

  return apiResponse(FETCH, "Download URL", preSignedURL, res);
});

const createUpdateMessage = async (req, res, isEdit = false) => {
  const {
    miqaatID,
    arazCityID,
    messageID,
    scheduledAt,
    recipientType,
    departmentIDs,
    zoneIDs,
    positionIDs,
    kgTypeIDs,
    kgGroupIDs,
    systemRoleID,
    CCsystemRoleID,
    CCrecipientType,
    CCdepartmentIDs,
    CCzoneIDs,
    CCpositionIDs,
    CCkgTypeIDs,
    CCkgGroupIDs,
    body,
    subject,
    ITSIDs,
    CCITSIDs,
    isEmail,
    isNotification,
  } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  if (!isEmail && !isNotification) {
    return apiError(
      CUSTOM_ERROR,
      "Please select at least one of email or notification",
      null,
      res ? res : null
    );
  }

  const userID =
    recipientType === "PMO" ? constants.AMS_SYSTEMID : req.user._id; // logged in userID

  if (isEdit) {
    const existingMessage = await Message.findById(messageID).select(
      "sender status"
    );
    if (!existingMessage) {
      return apiError(NOT_FOUND, "Message", null, res ? res : null);
    }
    if (existingMessage.status !== statusTypes.SCHEDULED) {
      return apiError(CUSTOM_ERROR, "Invalid action", null, res ? res : null);
    }
    if (existingMessage.sender.toString() !== userID.toString()) {
      return apiError(FORBIDDEN, "User", null, res ? res : null);
    }
  }

  if (!isEmpty(scheduledAt) && new Date(scheduledAt) <= new Date()) {
    return apiError(DATE_ERROR, null, null, res ? res : null);
  }
  const messageStatus = !isEmpty(scheduledAt)
    ? statusTypes.SCHEDULED
    : recipientType === "PMO"
    ? statusTypes.DECLINED
    : statusTypes.PUBLISHED;

  if (
    recipientType === "CUSTOM" &&
    (!departmentIDs || !departmentIDs.length) &&
    (!zoneIDs || !zoneIDs.length) &&
    (!positionIDs || !positionIDs.length) &&
    (!kgTypeIDs || !kgTypeIDs.length) &&
    (!kgGroupIDs || !kgGroupIDs.length)
  ) {
    return apiError(
      CUSTOM_ERROR,
      "Please select at least one of any",
      null,
      res ? res : null
    );
  }
  if(!recipientType && systemRoleID.length<1){
    return apiError(CUSTOM_ERROR, "Please select at least one of any", null, res);
  }

  const { userEmails: recipientEmails, userIDs: recipients } =
    await getRecipients(
      userID,
      recipientType,
      miqaatID,
      arazCityID,
      departmentIDs,
      zoneIDs,
      positionIDs,
      kgTypeIDs,
      kgGroupIDs,
      ITSIDs,
      systemRoleID
    );

  // Get CC recipients if CCrecipientType exists
  let CCrecipients = [],
    CCrecipientEmails = [];
  if (CCrecipientType || CCsystemRoleID.length > 0) {
    const CCData = await getRecipients(
      userID,
      CCrecipientType,
      miqaatID,
      arazCityID,
      CCdepartmentIDs,
      CCzoneIDs,
      CCpositionIDs,
      CCkgTypeIDs,
      CCkgGroupIDs,
      CCITSIDs,
      CCsystemRoleID
    );
    CCrecipients = CCData.userIDs;
    CCrecipientEmails = CCData.userEmails;
  }

  if (!recipients.length) {
    return apiError(NOT_FOUND, "Recipients", null, res ? res : null);
  }

  const messageData = {
    ...req.body,
    status: messageStatus,
    sender: userID,
    recipients,
  };
  if (messageStatus === statusTypes.SCHEDULED) {
    messageData["createdAt"] = scheduledAt;
    messageData["updatedAt"] = scheduledAt;
  }
  if (CCrecipients.length > 0) {
    messageData["CCrecipients"] = CCrecipients;
  }

  let email;
  let message;
  if (isEmail) {
    email = await Email.create(messageData);
  }
  if (isNotification) {
    if (isEdit) {
      message = await Message.findByIdAndUpdate(messageID, {
        $set: messageData,
      });
    } else {
      message = await Message.create(messageData);
    }
  }
  if (!message && !email) {
    if (isEdit) {
      return apiError(UPDATE_ERROR, "Message", null, res ? res : null);
    } else {
      return apiError(
        CUSTOM_ERROR,
        "Couldn't send message",
        null,
        res ? res : null
      );
    }
  }

  if (isEdit) {
    apiResponse(UPDATE_SUCCESS, "Message", message, res ? res : null);
  } else {
    apiResponse(
      ADD_SUCCESS,
      "Message sent successfully",
      message,
      res ? res : null
    );
  }

  if (isNotification) {
    if (!isEmpty(scheduledAt)) {
      // Schedule job in BullMQ
      const delay = new Date(scheduledAt) - new Date();

      if (isEdit) {
        await messageQueue.updateScheduledMessage(
          message._id.toString(),
          delay
        );
      } else {
        await messageQueue.scheduleMessage(message._id.toString(), delay);
      }
    } else {
      // Send push notification via OneSignal
      const notificationLogData = {
        messageID: message._id,
        replyID: null,
        messageType: messageTypes.MESSAGE,
        recipients: [...recipients, ...CCrecipients],
      };
      const notificationTemplateData = {
        messageID: message._id,
        title: subject,
        message: sanitizeHtml(body),
        miqaatID,
        arazCityID,
      };
      await sendPushNotifications(
        notificationLogData,
        notificationTemplate.MESSAGE(notificationTemplateData)
      );
    }
  }
  if (isEmail) {
    const allRecipientEmails = [...recipientEmails, ...CCrecipientEmails];
    for (const [index, rpData] of allRecipientEmails.entries()) {
      const template = {
        subject: `${rpData.ITSID} || ${messageData.subject}`,
        body: `Afzalus Salaam ${rpData.name},\n\n${messageData.body}`,
      };
      const result = await sendTemplatedEmail(
        rpData.email,
        template,
        {},
        [],
        messageData.attachments || [],
        { emailLogID: email._id.toString(), userID: rpData._id.toString() }
      );
      if (result.accepted.includes(rpData.email)) {
        await EmailLog.create({
          emailID: email._id,
          smtpMessageID: result.messageId,
          recipientEmail: rpData.email,
          recipientUserID: rpData._id,
          status: "processed",
        });
      }

      console.log(`${index + 1}/${allRecipientEmails.length} email has been sent`);
    }
  }
};

const getUserData = (loggedInUserID, user, data, fetchOne = true) => {
  const {
    createdAt,
    updatedAt,
    body,
    attachments,
    isReadBy,
    miqaatID,
    arazCityID,
  } = data;

  if (!user) {
    return {
      [`${fetchOne ? "sender" : "recipient"}ID`]: "683d55c92ee23940cf3d28c8",
      [`${fetchOne ? "sender" : "recipient"}`]: "Deleted User",
      logo: "default_logo.png",
      isRead: false,
      position: "user has been deleted",
      createdAt: createdAt || new Date().toISOString(),
      updatedAt: updatedAt || new Date().toISOString(),
      body: body || "No message content",
      attachments: attachments || [],
    };
  }

  const userData = {
    [`${fetchOne ? "sender" : "recipient"}ID`]: user._id,
    [`${fetchOne ? "sender" : "recipient"}`]: user.name,
    logo: user.logo,
    isRead: isReadBy[loggedInUserID.toString()],
    createdAt,
    updatedAt,
  };

  let position = "";
  if (user._id.toString() === constants.AMS_SYSTEMID) {
    position = `AMS System`;
  } else {
    const userMiqaat = user.miqaats.find(
      (miqaat) =>
        miqaat.miqaatID &&
        miqaat.miqaatID.toString() === miqaatID.toString() &&
        miqaat.arazCityID &&
        miqaat.arazCityID._id.toString() === arazCityID.toString()
    );

    if (!userMiqaat) {
      if (user.systemRoleID) {
        position = `${user.systemRoleID.name}`;
      } else {
        position = `User`;
      }
    } else {
      position = `User`;
      if (userMiqaat.hierarchyPositionID) {
        position = userMiqaat?.arazCityID?.showPositionAlias
          ? `${userMiqaat.hierarchyPositionID.alias}`
          : `${userMiqaat.hierarchyPositionID.name}`;
      }
      if (userMiqaat.arazCityZoneID) {
        position = `${userMiqaat.arazCityZoneID.name} ${position}`;
      }
      if (userMiqaat.departmentID) {
        position = `${userMiqaat.departmentID.name} ${position}`;
      }
    }
    position = `${position} / ${user.ITSID}`;
  }

  userData["position"] = position;

  if (body) userData["body"] = body;
  if (attachments) userData["attachments"] = attachments;

  return userData;
};

const getRecipients = async (
  userID,
  recipientType,
  miqaatID,
  arazCityID,
  departmentIDs,
  zoneIDs,
  positionIDs,
  kgTypeIDs,
  kgGroupIDs,
  ITSIDs,
  systemRoleID
) => {
  const query = {
    _id: { $ne: userID },
  };

  // Add miqaats check only if recipientType is provided
  if (recipientType) {
    query.miqaats = {
      $elemMatch: {
        miqaatID: toObjectId(miqaatID),
        arazCityID: toObjectId(arazCityID),
      },
    };

    switch (recipientType) {
      case "ALL_IN_HIERARCHY":
        query.miqaats["$elemMatch"]["hierarchyPositionID"] = { $ne: null };
        break;

      case "ALL_IN_DEPARTMENT":
        query.miqaats["$elemMatch"]["departmentID"] = {
          $in: toObjectId(departmentIDs),
        };
        break;

      case "ALL_IN_ZONE":
        query.miqaats["$elemMatch"]["arazCityZoneID"] = {
          $in: toObjectId(zoneIDs),
        };
        break;

      case "ALL_DEPARTMENT_HOD":
        query.miqaats["$elemMatch"]["departmentID"] = {
          $in: toObjectId(departmentIDs),
        };
        query.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
          constants.HIERARCHY_POSITIONS.HOD[0].toString()
        );
        break;

      case "ALL_ZONE_HOD":
        query.miqaats["$elemMatch"]["arazCityZoneID"] = {
          $in: toObjectId(zoneIDs),
        };
        query.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
          constants.HIERARCHY_POSITIONS.ZONE_HEAD[0].toString()
        );
        break;

      case "BY_ITS":
        query["ITSID"] = { $in: ITSIDs };
        break;

      case "CUSTOM":
        if (departmentIDs && departmentIDs.length) {
          query.miqaats["$elemMatch"]["departmentID"] = {
            $in: toObjectId(departmentIDs),
          };
        }
        if (zoneIDs && zoneIDs.length) {
          query.miqaats["$elemMatch"]["arazCityZoneID"] = {
            $in: toObjectId(zoneIDs),
          };
        }
        if (positionIDs && positionIDs.length) {
          query.miqaats["$elemMatch"]["hierarchyPositionID"] = {
            $in: toObjectId(positionIDs),
          };
        }
        if (kgTypeIDs && kgTypeIDs.length) {
          query.miqaats["$elemMatch"]["kgTypeID"] = {
            $in: toObjectId(kgTypeIDs),
          };
        }
        if (kgGroupIDs && kgGroupIDs.length) {
          query.miqaats["$elemMatch"]["kgGroupID"] = {
            $in: toObjectId(kgGroupIDs),
          };
        }
        break;

      case "PMO":
        query.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
          constants.HIERARCHY_POSITIONS.PMO[0].toString()
        );
        break;
    }
  }

  // Add systemRole condition
  if (systemRoleID?.length) {
    if (recipientType) {
      // If recipientType is provided, use $or to combine existing conditions and systemRole
      const existingConditions = { ...query };
      delete existingConditions._id; // Keep _id condition separate

      query.$or = [
        existingConditions,
        {
          systemRoleID: { $in: toObjectId(systemRoleID) },
        },
      ];

      // Remove the conditions that are now in $or
      Object.keys(existingConditions).forEach((key) => {
        if (key !== "_id") {
          delete query[key];
        }
      });
    } else {
      // If no recipientType, only add systemRole condition
      query.systemRoleID = { $in: toObjectId(systemRoleID) };
    }
  }

  const existingUsers = await KGUser.find(query)
    .select("_id name ITSID miqaats logo email")
    .populate({
      path: "miqaats.hierarchyPositionID",
      select: "name alias",
    })
    .populate({
      path: "miqaats.departmentID",
      select: "name",
    })
    .populate({
      path: "miqaats.arazCityZoneID",
      select: "name",
    });

  const userIDs = [];
  const userEmails = [];
  existingUsers.map((user) => {
    userIDs.push(user._id);
    userEmails.push({ _id: user._id, email: user.email, name: user.name, ITSID: user.ITSID, });
  });

  const existingUsersData = existingUsers.map((user) => {
    const matchedMiqaat = user.miqaats.find(
      (miqaat) =>
        miqaat.miqaatID?.toString() === miqaatID &&
        miqaat.arazCityID?.toString() === arazCityID
    );

    return {
      _id: user._id,
      name: user.name,
      LDName: user.LDName,
      ITSID: user.ITSID,
      logo: user.logo,
      hierarchyPosition: matchedMiqaat?.hierarchyPositionID?.name || null,
      hierarchyAlias: matchedMiqaat?.hierarchyPositionID?.alias || null,
      department: matchedMiqaat?.departmentID?.name || null,
      zone: matchedMiqaat?.arazCityZoneID?.name || null,
    };
  });

  return { userIDs, userEmails, existingUsersData };
};

const updateMessageReadStatus = async (
  messageID,
  status,
  userID,
  isReply = false
) => {
  const userIDs = Array.isArray(userID) ? toString(userID) : [toString(userID)];

  const updateFields = userIDs.reduce((acc, userID) => {
    acc[`isReadBy.${userID}`] = status;

    return acc;
  }, {});
  if (isReply) {
    updateFields["updatedAt"] = Date.now();
  }

  await Message.findByIdAndUpdate(messageID, { $set: updateFields });

  return;
};

const getMessageRecipients = apiHandler(async (req, res) => {
  const {
    recipientType,
    departmentIDs,
    zoneIDs,
    positionIDs,
    kgTypeIDs,
    kgGroupIDs,
    miqaatID,
    arazCityID,
    ITSIDs,
    systemRoleID,
  } = req.body;

  const userID =
    recipientType === "PMO" ? constants.AMS_SYSTEMID : req.user._id;
  if (
    recipientType === "CUSTOM" &&
    (!departmentIDs || !departmentIDs.length) &&
    (!zoneIDs || !zoneIDs.length) &&
    (!positionIDs || !positionIDs.length) &&
    (!kgTypeIDs || !kgTypeIDs.length) &&
    (!kgGroupIDs || !kgGroupIDs.length)
  ) {
    return apiError(
      CUSTOM_ERROR,
      "Please select at least one of any",
      null,
      res ? res : null
    );
  }
  if(!recipientType && systemRoleID?.length<1) {
    return apiError(CUSTOM_ERROR, "Please select at least one of any", null, res ? res : null);
  }

  const recipients = await getRecipients(
    userID,
    recipientType,
    miqaatID,
    arazCityID,
    departmentIDs,
    zoneIDs,
    positionIDs,
    kgTypeIDs,
    kgGroupIDs,
    ITSIDs,
    systemRoleID
  ).then((res) => res.existingUsersData);
  if (!recipients.length) {
    return apiError(NOT_FOUND, "Recipients", null, res ? res : null);
  }
  return apiResponse(FETCH, "Recipients", recipients, res);
});

const getMessageTypes = apiHandler(async (req, res) => {
  const messageTypes = await MessageType.find();
  if (!messageTypes.length) {
    return apiError(NOT_FOUND, "Message Types", null, res);
  }
  return apiResponse(FETCH, "Message Types", messageTypes, res);
});

const getMessageRecipientsByMessageID = apiHandler(async (req, res) => {
  const { id } = req.params;

  const mappedData = await mapRecipientsByNotificationLog(id);

  return apiResponse(FETCH, "Recipients notification data", mappedData, res);
});

const mapRecipientsByNotificationLog = async (messageID) => {
  try {
    // Fetch the message and notification log
    const message = await Message.findById(messageID).select(
      "recipients CCrecipients ITSIDs CCITSIDs arazCityID miqaatID replies"
    );

    if (!message) {
      throw new Error("Message not found");
    }

    // Fetch notification log for this message
    const notificationLog = await NotificationLog.findOne({
      messageID: messageID,
    });

    if (!notificationLog) {
      throw new Error("Notification log not found");
    }

    const arazCity = await ArazCity.findById(message.arazCityID).select(
      "showPositionAlias"
    );

    if (!arazCity) {
      throw new Error("Araz City not found");
    }

    // Fetch users information
    const allRecipientIds = [
      ...message.recipients,
      ...message.CCrecipients,
    ].map((id) => id.toString());

    const users = await KGUser.find({ _id: { $in: allRecipientIds } })
      .select("_id name ITSID miqaats logo")
      .populate({
        path: "miqaats.hierarchyPositionID",
        select: "name alias",
      })
      .populate({
        path: "miqaats.departmentID",
        select: "name",
      })
      .populate({
        path: "miqaats.arazCityZoneID",
        select: "name",
      });

    // Create a map of users by ID for quick lookup
    const userMap = {};
    users.forEach((user) => {
      userMap[user._id.toString()] = user;
    });

    // Map notification status for each recipient
    const recipientsData = notificationLog.recipients
      .map((recipient) => {
        const userId = recipient.userID.toString();
        const user = userMap[userId];

        if (!user) return null;

        // Find the relevant miqaat entry that matches the message's arazCityID and miqaatID
        const matchedMiqaat = user.miqaats.find(
          (miqaat) =>
            miqaat.miqaatID?.toString() === message.miqaatID?.toString() &&
            miqaat.arazCityID?.toString() === message.arazCityID?.toString()
        );

        // Determine delivery status
        let deliveryStatus = "Undelivered";
        let deliveryTime = null;

        // Check if any device received the notification successfully
        const successfulDelivery = recipient.devices.find(
          (device) => device.success
        );
        if (successfulDelivery) {
          deliveryStatus = "Delivered";
          deliveryTime = successfulDelivery.deliveryTime;
        }

        // Determine if this is a primary recipient or CC
        const recipientType = message.recipients.some(
          (r) => r.toString() === userId
        )
          ? "recipient"
          : "cc";
        const hasReplied = message.replies.some(
          (reply) => reply.sender.toString() === user._id.toString()
        );
        return {
          _id: user._id,
          ITSID: user.ITSID || null,
          name: user.name,
          logo: user.logo || null,
          department: matchedMiqaat?.departmentID?.name || null,
          zone: matchedMiqaat?.arazCityZoneID?.name || null,
          hierarchyPosition: arazCity.showPositionAlias
            ? matchedMiqaat?.hierarchyPositionID?.alias
            : matchedMiqaat?.hierarchyPositionID?.name || null,
          status: deliveryStatus,
          receivedOn: deliveryTime
            ? new Date(deliveryTime).toISOString()
            : null,
          recipientType: recipientType,
          isReplied: hasReplied,
        };
      })
      .filter(Boolean); // Remove null entries

    return {
      messageID: message._id,
      recipients: recipientsData,
      sentAt: notificationLog.createdAt,
    };
  } catch (error) {
    console.error("Error mapping recipients by notification log:", error);
    throw error;
  }
};

module.exports = {
  createUpdateMessage,
  addMessage,
  editMessage,
  deleteMessage,
  getMessages,
  getMessage,
  addMessageReply,
  getDashboard,
  getEmailReport,
  addAttachments,
  getAttachment,
  getMessageRecipients,
  getMessageTypes,
  getMessageRecipientsByMessageID,
  getUserData,
};
