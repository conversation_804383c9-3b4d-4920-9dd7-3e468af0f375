const Joi = require("joi");
const { idValidation } = require("../../../utils/validator.util");

const waazSeatingCapacityBaseSchema = {
  waazVenueTypeID: idValidation,
  waazVenueSuitabilityID: idValidation,
  waazVenuePriorityID: idValidation,
  multiplicationFactor: Joi.number().allow(null),
};

const addWaazSeatingCapacitySchema = Joi.object({
  ...waazSeatingCapacityBaseSchema,
});

const editWaazSeatingCapacitySchema = Joi.object({
  _id: idValidation,
  ...waazSeatingCapacityBaseSchema,
});

const getSingleWaazSeatingCapacitySchema = Joi.object({
  id: idValidation,
});

const deleteWaazSeatingCapacitySchema = Joi.object({
  id: idValidation,
});
const getMultiplicationFactorSchema = Joi.object({
  waazVenueTypeID: idValidation,
  waazVenueSuitabilityID: idValidation,
  waazVenuePriorityID: idValidation
});

module.exports = {
  addWaazSeatingCapacitySchema,
  editWaazSeatingCapacitySchema,
  getSingleWaazSeatingCapacitySchema,
  deleteWaazSeatingCapacitySchema,
  getMultiplicationFactorSchema
};
