const { Queue, Worker } = require("bullmq");
const { connection } = require('./redis');
const { Message, messageTypes } = require('../../services/communication/models');
const { sendPushNotifications, notificationTemplate } = require("../oneSignal.util");
const { getOpenProjectMemberships } = require("../openProject");
const { KGUser } = require("../../services/hierarchy/models");

// Create BullMQ Queue
const queueName = "webhookQueue";
const webhookQueue = new Queue(queueName, {
  connection,
  defaultJobOptions: { 
    removeOnComplete: true,
    removeOnFail: true
  },
});

/**
 * Add webhook for processing
 * @param {object} payload - payload provided from webhook
 */
const addWebhook = async (payload) => {
  // console.log(`Schedule created for message: ${messageID} at: ${new Date(Date.now() + delay).toLocaleString()}`);
  await webhookQueue.add(
    "processWebhook",
    payload,
    {
      attempts: 3, // Retry up to 3 times if it fails
      backoff: { type: "exponential", delay: 5000 }, // Retry delay increases
    }
  );
}

const STATUSES = ['completed', 'failed', 'waiting', 'active', 'delayed', 'paused'];

const getJobs = async () => {
  const allJobs = [];

  for (const status of STATUSES) {
    const jobs = await webhookQueue.getJobs([status], 0, -1, false);
    for (const job of jobs) {
      allJobs.push({
        id: job.id,
        name: job.name,
        data: job.data,
        status,
        returnvalue: job.returnvalue,
        failedReason: job.failedReason,
        timestamp: job.timestamp,
      });
    }
  }

  return allJobs;
}

/**
 * BullMQ Worker: Processes scheduled messages
 */
const worker = new Worker(
  queueName,
  async (job) => {
    try {
      const [event, type] = job.data.action.split(":")
      let url = ""
      let title = ""
      let message = ""

      switch (event) {
        case "project":
          url = job.data[event]?._links?.memberships?.href || ""
          title = type === "created" ? "Project created!" : "Project updated!"
          message = type === "created" ? "A new Project has been created" : "A Project has been updated"
          break;
          
          case "work_package":
          url = job.data[event]?._embedded.project._links?.memberships?.href || ""
          title = type === "created" ? "Task created!" : "Task updated!"
          message = type === "created" ? "A new Task has been created" : "A Task has been updated"
          break;
      
        default:
          break;
      }

      const memberships = await getOpenProjectMemberships(url)
      const memberTitles = memberships.map(m => m._links.self.title)

      const recipients = await KGUser.find({ name: memberTitles }).select("_id")

      if(title && message) {
        const notificationLogData = {
          messageID: null,
          replyID: null,
          messageType: messageTypes.NOTIFICATION,
          recipients: recipients.map(r => r._id.toString())
        }
        const notificationTemplateData = {
          title,
          message
        }
        await sendPushNotifications(notificationLogData, notificationTemplate.NOTIFICATION(notificationTemplateData))
      }
    } catch (error) {
      console.log("error in job", job.id, error)
    }
  },
  { connection }
);

module.exports = { 
  addWebhook,
  getJobs
};
