const express = require("express");
const router = express.Router();
const questionRoute = require("./question.route");
const surveyRoute = require("./surveyForm.route");
const allQuestionRoute = require("./allQuestion.route");
const allSurveyRoute = require("./allSurveyForm.route");
const surveyResponseRoute = require("./surveyResponse.route");
const reports = require("./reports.route");
const allReports = require("./allSurveyReport.route");

router.use("/question", questionRoute);
router.use("/all-question", allQuestionRoute);
router.use("/survey-forms", surveyRoute);
router.use("/all-survey-forms", allSurveyRoute);
router.use("/survey-response", surveyResponseRoute);
router.use("/reports", reports);
router.use("/all-reports", allReports);

module.exports = router;

