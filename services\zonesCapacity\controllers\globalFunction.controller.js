const { GlobalFunction } = require("../models/globalFunction.model");

const generateUniqueName = (name) => {
  return name
    .trim()
    .toLowerCase()
    .replace(/[^\w\s]/gi, "")
    .replace(/\s+/g, "_");
};

const getAllFunctions = async (req, res) => {
  try {
    const functions = await GlobalFunction.find()
      .sort({ priority: 1 })
      .populate("createdBy", "name email")
      .populate("updatedBy", "name email");

    return res.status(200).json({
      success: true,
      data: functions,
      message: "Functions retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching functions:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

const createFunction = async (req, res) => {
  try {
    const uniqueName = generateUniqueName(req.body.name);

    const existingFunction = await GlobalFunction.findOne({ uniqueName });
    if (existingFunction) {
      return res.status(400).json({
        success: false,
        message: "GlobalFunction with this name already exists",
      });
    }

    const newFunction = new GlobalFunction({
      ...req.body,
      uniqueName,
      status: true,
      createdBy: req.user?.id,
    });

    await newFunction.save();

    return res.status(201).json({
      success: true,
      data: newFunction,
      message: "GlobalFunction created successfully",
    });
  } catch (error) {
    console.error("Error creating function:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

const getFunctionById = async (req, res) => {
  try {
    const functionId = req.params.id;
    const functionItem = await GlobalFunction.findById(functionId)
      .populate("createdBy", "name email")
      .populate("updatedBy", "name email");

    if (!functionItem) {
      return res.status(404).json({
        success: false,
        message: "GlobalFunction not found",
      });
    }

    return res.status(200).json({
      success: true,
      data: functionItem,
      message: "GlobalFunction retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching function:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

const updateFunction = async (req, res) => {
  try {
    const functionId = req.body.id;

    const existingFunction = await GlobalFunction.findById(functionId);
    if (!existingFunction) {
      return res.status(404).json({
        success: false,
        message: "GlobalFunction not found",
      });
    }

    let updateData = { ...req.body };

    if (req.body.name && req.body.name !== existingFunction.name) {
      const uniqueName = generateUniqueName(req.body.name);

      const uniqueNameExists = await GlobalFunction.findOne({
        uniqueName,
        _id: { $ne: functionId },
      });

      if (uniqueNameExists) {
        return res.status(400).json({
          success: false,
          message: "GlobalFunction with this name already exists",
        });
      }

      updateData.uniqueName = uniqueName;
    }

    const updatedFunction = await GlobalFunction.findByIdAndUpdate(
      functionId,
      {
        ...updateData,
        updatedBy: req.user?.id,
      },
      { new: true }
    )
      .populate("createdBy", "name email")
      .populate("updatedBy", "name email");

    return res.status(200).json({
      success: true,
      data: updatedFunction,
      message: "GlobalFunction updated successfully",
    });
  } catch (error) {
    console.error("Error updating function:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

const deleteFunction = async (req, res) => {
  try {
    const functionId = req.params.id;

    const existingFunction = await GlobalFunction.findById(functionId);
    if (!existingFunction) {
      return res.status(404).json({
        success: false,
        message: "GlobalFunction not found",
      });
    }

    await GlobalFunction.findByIdAndDelete(functionId);

    return res.status(200).json({
      success: true,
      message: "GlobalFunction deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting function:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

module.exports = {
  getAllFunctions,
  createFunction,
  getFunctionById,
  updateFunction,
  deleteFunction,
};
