const { apiHandler, apiResponse } = require("../../../utils/api.util");
const { FETCH } = require("../../../utils/message.util");
const { KGUser } = require("../../hierarchy/models");

const loginToOpenProject = apiHandler(async (req, res) => {
  const { id } = req.params;

  const kgUser = await KGUser.findById(id).select(
    "email ITSID openProjectID openProjectPassword"
  );

  const response = {
    url: "https://task.asharams.com/login",
    username: kgUser.ITSID,
    password: kgUser.openProjectPassword,
  }

  return apiResponse(FETCH, "tasks", response, res);
});

module.exports = {
  loginToOpenProject
};
