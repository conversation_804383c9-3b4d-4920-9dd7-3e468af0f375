const { Schema, model } = require("mongoose");

const kgUsersSchema = new Schema({
  userID: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  userITS: {
    type: String,
    required: true,
  },
  departmentID: {
    type: Schema.Types.ObjectId,
    ref: "Department",
  },
  compileListName: {
    type: String,
  },
  isMerged: {
    type: Boolean,
  },
  isAlreadyExist: {
    type: Boolean,
  },
  isError: {
    type: Boolean,
  },
  error: {
    type: String,
  },
}, { _id: false })

const RazaListSchema = new Schema(
  {
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    fasalArazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
    },
    totalUniqueUsers: {
      type: Number,
    },
    kgUsers: [kgUsersSchema],
    duplicateKgUsers: [kgUsersSchema],
  },
  { timestamps: true }
);

const RazaList = model("RazaList", RazaListSchema);

module.exports = {RazaList};
