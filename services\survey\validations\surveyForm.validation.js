const Joi = require("joi");
const {
  idValidation,
  stringValidation,
  arrayIdValidation,
} = require("../../../utils/validator.util");
const {
  surveyRecipientMeta,
  surveyFormFrequencyList,
} = require("../models/surveyForm.model");
const { getQuestions } = require("../controllers/surveyForm.controller");

const getAllSurveysSchema = Joi.object({
  miqaatID: idValidation.optional(),
  arazCityID: idValidation.optional(),
});

const criteriaSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  recipientType: Joi.string()
    .valid(...surveyRecipientMeta.allTypes)
    .optional(),
  departmentIDs: arrayIdValidation.optional(),
  functionIDs: arrayIdValidation.optional(),
  zoneIDs: arrayIdValidation.optional(),
  positionIDs: arrayIdValidation.optional(),
  kgGroupIDs: arrayIdValidation.optional(),
  kgTypeIDs: arrayIdValidation.optional(),
  ITSIDs: Joi.array().items(stringValidation).optional(),
});
const recipientsSchema = Joi.object({
  criteria: Joi.array().items(criteriaSchema).optional(),
  systemRoleIDs: arrayIdValidation.optional(),
});

const addEditSurveySchema = Joi.object({
  name: stringValidation.trim().required(),
  isActive: Joi.boolean().default(false),
  frequency: Joi.string()
    .required()
    .valid(...Object.values(surveyFormFrequencyList)),
  startTime: Joi.date().optional(),
  endTime: Joi.date().optional(),
  department: arrayIdValidation.optional(),
  questions: Joi.array().items(idValidation.required()).min(1).required(),
  recipients: recipientsSchema,
  reportRecipients: recipientsSchema,
  alwaysAvailable: Joi.boolean().default(false),
  startDate: Joi.date().optional().allow(null),
  endDate: Joi.date().optional().allow(null),
  questionMapping: Joi.array().items(
    Joi.object({
      surveyID: idValidation.required(),
      questionID: idValidation.required(),
    })
  ),
});

const getSingleSurveySchema = Joi.object({
  id: idValidation.required(),
});

const deleteSurveySchema = Joi.object({
  id: idValidation.required(),
});

const getQeustionsSchema = Joi.object({
  id: idValidation.optional(),
  surveyID: idValidation.optional().allow(""),
});

module.exports = {
  getAllSurveysSchema,
  addEditSurveySchema,
  getSingleSurveySchema,
  deleteSurveySchema,
  getQeustionsSchema,
};
