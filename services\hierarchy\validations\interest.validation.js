const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
  emailValidation,
} = require("../../../utils/validator.util");

const addEditInterestSchema = Joi.object({
  id: idValidation.optional(),
  userID: idValidation,
  ITSID: stringValidation,
  miqaatID: idValidation,
  arazCityID: idValidation,
  interestOne: Joi.object({}).unknown().optional(),
  interestTwo: Joi.object({}).unknown().optional(),
  interestThree: Joi.object({}).unknown().optional(),
  arazCityZoneID: idValidation.optional().allow(null),
});

const getSingleInterestSchema = Joi.object({
  ITSID: stringValidation,
});

const checkInterestStatusSchema = Joi.object({
  ITSID: stringValidation,
});

const getSingleInterestSchemaV2 = Joi.object({
  ITSID: stringValidation,
  miqaatID: idValidation,
  arazCityID: idValidation,
});

const checkInterestStatusSchemaV2 = Joi.object({
  ITSID: stringValidation,
  miqaatID: idValidation,
  arazCityID: idValidation,
});

const getAllInterestsSchema = Joi.object({
  arazCityID: idValidation,
  miqaatID: idValidation,
});
const getAllInterestsByPaginationSchema = Joi.object({
  search: stringValidation.allow("").optional(),
  priority: stringValidation.allow("").optional(),
  department: idValidation.optional().allow(""),
  jamaat: idValidation.optional().allow(""),
  status: Joi.string().valid("assigned", "not-assigned", "").optional().allow(null),
  jamaat: idValidation.optional().allow(""),
  sortBy: Joi.string()
    .valid(
      "name",
      "email",
      "phone",
      "ITSID",
      "gender",
      "qualification",
      "occupation",
      "department",
      "jamaat",
      "jamiat",
      "createdAt",
      "updatedAt",
      "zone"
    )
    .optional()
    .allow(""),
  sortOrder: Joi.string().valid("asc", "desc", "").optional(),
  qualification: stringValidation.optional().allow(""),
  occupation: stringValidation.optional().allow(""),
  gender: Joi.array().items(Joi.string().valid("male", "female")).optional(),
}).unknown();
module.exports = {
  addEditInterestSchema,
  getSingleInterestSchema,
  checkInterestStatusSchema,
  getSingleInterestSchemaV2,
  checkInterestStatusSchemaV2,
  getAllInterestsSchema,
  getAllInterestsByPaginationSchema,
};
