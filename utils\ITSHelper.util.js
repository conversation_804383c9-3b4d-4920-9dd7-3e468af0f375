const axios = require("axios");
const qs = require("qs");
const { isEmpty, generateID } = require("./misc.util");
const {
  ITS_AUTH_TOKEN,
  ITS_USER_HCODE,
  ITS_USER_PHOTO_HCODE,
  ITS_USER_QA_PHOTO_HCODE,
  NODE_ENV,
  ITS_JAMAAT_WISE_HOF_HCODE,
  ITS_JAMIAT_WISE_HOF_HCODE,
  ITS_HOF_WISE_MEMBER_STATS_HCODE,
  ITS_JAMIAT_JAMAAT_MASTER_RECORD_HCODE,
} = require("../constants");
const {
  getJamaatIDByITSID,
  getJamiatIDByITSID,
} = require("../services/hierarchy/controllers/jamiatJamaat.controller");
const { XMLParser } = require("fast-xml-parser");
const path = require("path");
const fs = require("fs");
const { json } = require("stream/consumers");

const extractBase64Content = (xml) => {
  const parser = new XMLParser({ ignoreAttributes: false, trimValues: true });
  const result = parser.parse(xml);
  const base64Content = result.anyType;
  return base64Content;
};

const base64ToImage = async (base64String, folderName) => {
  try {
    const assetsDir = path.join(__dirname, "..", "assets");
    const folderPath = path.join(assetsDir, folderName);

    // Ensure the directory exists
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true });
    }

    // Extract file extension and Base64 data
    const matches = base64String.match(/^data:image\/(\w+);base64,/);
    const fileExtension = matches ? matches[1] : "png";
    const base64Data = base64String.replace(/^data:image\/\w+;base64,/, "");

    // Generate unique file name
    const ID = generateID();
    const fileName = `user_${ID}.${fileExtension}`;
    const filePath = path.join(folderPath, fileName);

    // Save the image file
    const imageBuffer = Buffer.from(base64Data, "base64");
    await fs.promises.writeFile(filePath, imageBuffer);

    // Return a clean relative path
    return path.relative(process.cwd(), filePath).replace(/\\+/g, "/");
  } catch (error) {
    console.error("Error converting Base64 to image:", error);
    throw error;
  }
};

const getSingleUserDataByITSID = async (ITSID) => {
  const data = qs.stringify({
    Auth_Token: ITS_AUTH_TOKEN,
    HCode: ITS_USER_HCODE,
    Data_Output: "JSON",
    Param1: ITSID,
  });

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.its52.com/Services.asmx/HandlerB2",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data: data,
  };

  try {
    let response = await axios.request(config);

    response = response?.data;
    
    if (!isEmpty(response?.Table1)) {
      console.error(response.Table1)
      return null;
    }
    let user = response?.Table[0];

    return user;
  } catch (error) {
    // console.error("Error fetching user data:", error);
    if (error.response && error.response.data && !isEmpty(error.response.data.Table1)) {
      console.error('Table1 in error response:', error.response.data.Table1);
    }
    return null;
  }
};

const getSingleUserImageByITSID = async (ITSID) => {
  const data = qs.stringify({
    Auth_Token: ITS_AUTH_TOKEN,
    HCode: ITSID.startsWith("786")
      ? ITS_USER_QA_PHOTO_HCODE
      : ITS_USER_PHOTO_HCODE,
    Param1: ITSID,
  });

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.its52.com/Services.asmx/HandlerE1",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data: data,
  };

  try {
    let response = await axios.request(config);

    const extractedContent = extractBase64Content(response.data);

    if (
      isEmpty(extractedContent) ||
      extractedContent["@_d1p1:type"] !== "q1:base64Binary"
    ) {
      return null;
    }

    let imagePath = await base64ToImage(extractedContent["#text"], "user");

    return imagePath;
  } catch (error) {
    return null;
  }
};

const getITSUserData = async (id) => {
  if (NODE_ENV !== "production") {
    const sampleData = {
      name: "Burhanuddin bhai Sadiqali bhai Hotelwala",
      LDName: "برهان الدين بهائي  صادق علي بهائي هوٹل والا",
      logo: "assets/user/user_675994332348.png",
      ITSID: "50435912",
      email: "<EMAIL>",
      phone: "+919893163466",
      whatsapp: "+917898159152",
      status: "active",
      jamiat: {
        _id: "678dfbf62192a2e72b37da86",
        id: "678dfbf62192a2e72b37da86",
      },
      jamaat: {
        _id: "678dffdff72cc98446b0283a",
        id: "678dffdff72cc98446b0283a",
      },
      age: 28,
      gender: "M",
      maritialStatus: "Single",
      prefix: null,
      misaq: "Done",
      occupation: "Business",
      qualification: "Higher Secondary",
      idara: null,
      category: null,
      organization: null,
      address: "324 Sai Paradise, Bijalpur",
      city: "Indore",
      country: "India",
      nationality: "Indian",
      vatan: "Indore",
      jamiatID: "678dfbf62192a2e72b37da86",
      jamaatID: "678dffdff72cc98446b0283a",
    };
    return sampleData;
  }
  let ITSUserData = await getSingleUserDataByITSID(id);
  let imagePath = await getSingleUserImageByITSID(id);

  if (isEmpty(ITSUserData)) {
    return null;
  }

  let userData = {
    name: ITSUserData.Fullname,
    LDName: ITSUserData.Arabic_Fullname,
    logo: imagePath,
    ITSID: ITSUserData.ITS_ID,
    jamiat: await getJamiatIDByITSID(ITSUserData.Jamiaat_ID),
    jamaat: await getJamaatIDByITSID(
      ITSUserData.Jamaat_ID,
      ITSUserData.Jamiaat_ID
    ),
    phone: ITSUserData.Mobile,
    whatsapp: ITSUserData.WhatsApp_No,
    email: ITSUserData.Email,
    status: "active",
    age: ITSUserData.Age,
    gender: ITSUserData.Gender,
    maritialStatus: ITSUserData.Marital_Status,
    prefix: ITSUserData.Prefix,
    misaq: ITSUserData.Misaq,
    occupation: ITSUserData.Occupation,
    qualification: ITSUserData.Qualification,
    idara: ITSUserData.Idara,
    category: ITSUserData.Category,
    organization: ITSUserData.Organization,
    address: ITSUserData.Address,
    city: ITSUserData.City,
    country: ITSUserData.Country,
    nationality: ITSUserData.Nationality,
    vatan: ITSUserData.Vatan,
  };

  // console.log(id, ITSUserData.Jamiaat_ID, ITSUserData.Jamaat_ID);
  // console.log(userData.jamiat, userData.jamaat)
  userData.jamiatID = userData.jamiat.id;
  userData.jamaatID = userData.jamaat.id;

  return userData;
};

const getJamaatWiseHofData = async (jamaatID) => {
  if (NODE_ENV !== "production") {
    if(jamaatID === "1851"){
      return [
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20312675",
          Total_Count: 4,
          Mardo_Count: 2,
          Bairo_Count: 1,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-8) Burhani Mohalla",
          Sub_Sector: "(SS-8.2) House 286-390",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20301075",
          Total_Count: 3,
          Mardo_Count: 2,
          Bairo_Count: 1,
          Gair_Baligh_Dikra_Count: 0,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-1) Masakin Saifiyah",
          Sub_Sector: "(SS-1.3) House 122-141, 185-204",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310020",
          Total_Count: 6,
          Mardo_Count: 2,
          Bairo_Count: 2,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 1,
          Sector: "(Sec-1) Masakin Saifiyah",
          Sub_Sector: "(SS-1.4) House 142-184",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310045",
          Total_Count: 3,
          Mardo_Count: 0,
          Bairo_Count: 2,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-2) Masakin Saifiyah",
          Sub_Sector: "(SS-2.4) House 289-304, 376-391",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310049",
          Total_Count: 7,
          Mardo_Count: 3,
          Bairo_Count: 3,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-1) Masakin Saifiyah",
          Sub_Sector: "(SS-1.4) House 142-184",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310060",
          Total_Count: 8,
          Mardo_Count: 4,
          Bairo_Count: 3,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-1) Masakin Saifiyah",
          Sub_Sector: "(SS-1.3) House 122-141, 185-204",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310063",
          Total_Count: 6,
          Mardo_Count: 2,
          Bairo_Count: 3,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-8) Burhani Mohalla",
          Sub_Sector: "(SS-8.2) House 286-390",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310089",
          Total_Count: 6,
          Mardo_Count: 2,
          Bairo_Count: 3,
          Gair_Baligh_Dikra_Count: 0,
          Gair_Baligh_Dikri_Count: 1,
          Sector: "(Sec-6) Ali Township",
          Sub_Sector: "(SS-6.1) House 1-150",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310093",
          Total_Count: 5,
          Mardo_Count: 2,
          Bairo_Count: 2,
          Gair_Baligh_Dikra_Count: 0,
          Gair_Baligh_Dikri_Count: 1,
          Sector: "(Sec-4) Masakin Saifiyah",
          Sub_Sector: "(SS-4.2) House 526-591",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "60474898",
          Total_Count: 3,
          Mardo_Count: 1,
          Bairo_Count: 1,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-5) Crystal Apartments",
          Sub_Sector: "(SS-5.2) Floor 3 & 4",
        },
      ]
    }
    return null;
  }
  const data = qs.stringify({
    Auth_Token: ITS_AUTH_TOKEN,
    HCode: ITS_JAMAAT_WISE_HOF_HCODE,
    Data_Output: "JSON",
    Param1: jamaatID,
  });

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.its52.com/Services.asmx/HandlerB1",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data: data,
  };

  try {
    let response = await axios.request(config);

    response = response?.data;

    if (!isEmpty(response?.Table1)) {
      return null;
    }

    let user = response?.Table;

    return user;
  } catch (error) {
    console.error("Error fetching user data:", error);
    return null;
  }
};

const getJamiatWiseHofData = async (jamiatID) => {
  if (NODE_ENV !== "production") {
    if(jamiatID === "16"){
      return [
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20300430",
          Total_Count: 4,
          Mardo_Count: 2,
          Bairo_Count: 1,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-8) Burhani Mohalla",
          Sub_Sector: "(SS-8.2) House 286-390",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20301075",
          Total_Count: 3,
          Mardo_Count: 2,
          Bairo_Count: 1,
          Gair_Baligh_Dikra_Count: 0,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-1) Masakin Saifiyah",
          Sub_Sector: "(SS-1.3) House 122-141, 185-204",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310020",
          Total_Count: 6,
          Mardo_Count: 2,
          Bairo_Count: 2,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 1,
          Sector: "(Sec-1) Masakin Saifiyah",
          Sub_Sector: "(SS-1.4) House 142-184",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310045",
          Total_Count: 3,
          Mardo_Count: 0,
          Bairo_Count: 2,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-2) Masakin Saifiyah",
          Sub_Sector: "(SS-2.4) House 289-304, 376-391",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310049",
          Total_Count: 7,
          Mardo_Count: 3,
          Bairo_Count: 3,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-1) Masakin Saifiyah",
          Sub_Sector: "(SS-1.4) House 142-184",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310060",
          Total_Count: 8,
          Mardo_Count: 4,
          Bairo_Count: 3,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-1) Masakin Saifiyah",
          Sub_Sector: "(SS-1.3) House 122-141, 185-204",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310063",
          Total_Count: 6,
          Mardo_Count: 2,
          Bairo_Count: 3,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-8) Burhani Mohalla",
          Sub_Sector: "(SS-8.2) House 286-390",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310089",
          Total_Count: 6,
          Mardo_Count: 2,
          Bairo_Count: 3,
          Gair_Baligh_Dikra_Count: 0,
          Gair_Baligh_Dikri_Count: 1,
          Sector: "(Sec-6) Ali Township",
          Sub_Sector: "(SS-6.1) House 1-150",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "20310093",
          Total_Count: 5,
          Mardo_Count: 2,
          Bairo_Count: 2,
          Gair_Baligh_Dikra_Count: 0,
          Gair_Baligh_Dikri_Count: 1,
          Sector: "(Sec-4) Masakin Saifiyah",
          Sub_Sector: "(SS-4.2) House 526-591",
        },
        {
          Jamiaat_ID: 16,
          Jamaat_ID: 1851,
          HOF_ID: "60474898",
          Total_Count: 3,
          Mardo_Count: 1,
          Bairo_Count: 1,
          Gair_Baligh_Dikra_Count: 1,
          Gair_Baligh_Dikri_Count: 0,
          Sector: "(Sec-5) Crystal Apartments",
          Sub_Sector: "(SS-5.2) Floor 3 & 4",
        },
      ]
    }
    return null;
  }
  const data = qs.stringify({
    Auth_Token: ITS_AUTH_TOKEN,
    HCode: ITS_JAMIAT_WISE_HOF_HCODE,
    Data_Output: "JSON",
    Param1: jamiatID,
  });

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.its52.com/Services.asmx/HandlerB1",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data: data,
  };

  try {
    let response = await axios.request(config);

    response = response?.data;

    if (!isEmpty(response?.Table1)) {
      return null;
    }

    let user = response?.Table;

    return user;
  } catch (error) {
    console.error("Error fetching user data:", error);
    return null;
  }
};
const getHofWiseMemberStats = async (HOFID) => {
  if (NODE_ENV !== "production") {
    return [
      {
        Jamiaat_ID: 16,
        Jamaat_ID: 1851,
        HOF_ID: "20312675",
        Total_Count: 5,
        Mardo_Count: 4,
        Bairo_Count: 1,
        Gair_Baligh_Dikra_Count: 0,
        Gair_Baligh_Dikri_Count: 0,
        Sector: "(Sec-8) Burhani Mohalla",
        Sub_Sector: "(SS-8.2) House 286-390",
      },
    ];
  }
  const data = qs.stringify({
    Auth_Token: ITS_AUTH_TOKEN,
    HCode: ITS_HOF_WISE_MEMBER_STATS_HCODE,
    Data_Output: "JSON",
    Param1: HOFID,
  });

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.its52.com/Services.asmx/HandlerB2",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data: data,
  };

  try {
    let response = await axios.request(config);
    response = response?.data;

    if (!isEmpty(response?.Table1)) {
      return null;
    }

    let user = response?.Table;

    return user;
  } catch (error) {
    console.error("Error fetching user data:", JSON.stringify(error));
    return null;
  }
};

const getJamiatJammatMasterRecord = async () => {
  const data = qs.stringify({
    Auth_Token: ITS_AUTH_TOKEN,
    HCode: ITS_JAMIAT_JAMAAT_MASTER_RECORD_HCODE,
    Data_Output: "JSON",
  });

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.its52.com/Services.asmx/HandlerA1",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data: data,
  };

  try {
    let response = await axios.request(config);
    response = response?.data;

    if (!isEmpty(response?.Table1)) {
      return response.Table1;
    }
    console.log(response?.Table);
    let user = response?.Table;

    return user;
  } catch (error) {
    console.error("Error fetching user data:", JSON.stringify(error));
    return null;
  }
};

module.exports = {
  getSingleUserDataByITSID,
  getSingleUserImageByITSID,
  getITSUserData,
  getJamaatWiseHofData,
  getJamiatWiseHofData,
  getHofWiseMemberStats,
  getJamiatJammatMasterRecord,
};
