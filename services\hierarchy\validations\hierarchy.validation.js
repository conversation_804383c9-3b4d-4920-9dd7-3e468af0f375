const Joi = require("joi");
const { idValidation, stringValidation } = require("../../../utils/validator.util");

const getHierarchySchema = Joi.object({
  miqaatID: idValidation.label("Please select a Miqaat"),
  arazCityID: idValidation.label("Please select an Araz City"),
  type: stringValidation.optional().valid("tree"),
});

const regenerateHierarchySchema = Joi.object({
  arazCityID: idValidation,
  miqaatID: idValidation,
});

const editHierarchyUsersSchema = Joi.object({
  arazCityID: idValidation,
  miqaatID: idValidation,
});

const getDashboardAnalyticsSchema = Joi.object({
  arazCityID: stringValidation.optional().allow(""),
  miqaatID: stringValidation.optional().allow(""),
});

const getDashboardAliasNameSchema = Joi.object({
  arazCityID: stringValidation.optional().allow(""),
  miqaatID: stringValidation.optional().allow(""),
});

module.exports = {
  getHierarchySchema,
  regenerateHierarchySchema,
  editHierarchyUsersSchema,
  getDashboardAnalyticsSchema,
  getDashboardAliasNameSchema
};
