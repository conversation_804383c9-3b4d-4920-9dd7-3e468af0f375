const router = require("express").Router();
const {
  getAllZoneMappings,
  getSingleZoneMapping,
  addMultipleZoneMappings,
  deleteZoneMapping,
  getSingleZoneMappingReport,
  getArazCityWiseJamaatAndJamiat,
  addZoneMappingByJamaat,
  addZoneMappingByHof,
} = require("../controllers/zoneMapping.controller");
const {
  getZoneMappingSchema,
  deleteZoneMappingSchema,
  getSingleZoneMappingReportSchema,
  getSingleZoneMappingSchema,
  getArazCityWiseJamaatAndJamiatSchema,
  addZoneMappingsByHOFSchema,
  addZoneMappingsByJammatSchema,
} = require("../validations/zoneMapping.validation");
const { validate } = require("../../../middlewares/validation.middleware");


router.post(
  "/get/jammat-jamiat-by-arazcity",
  validate(getArazCityWiseJamaatAndJamiatSchema, "body"),
  getArazCityWiseJamaatAndJamiat
);


router.post("/get", validate(getZoneMappingSchema, "body"), getAllZoneMappings);

router.get(
  "/get/mapping-report/:arazCityZoneID",
  validate(getSingleZoneMappingReportSchema, "params"),
  getSingleZoneMappingReport
);

router.get(
  "/get/:arazCityZoneID",
  validate(getSingleZoneMappingSchema, "params"),
  getSingleZoneMapping
);

router.post(
  "/add/by-hof",
  validate(addZoneMappingsByHOFSchema, "body"),
  addZoneMappingByHof
);

router.post(
  "/add/by-jamaat",
  validate(addZoneMappingsByJammatSchema, "body"),
  addZoneMappingByJamaat
);


router.delete(
  "/delete/:id",
  validate(deleteZoneMappingSchema, "params"),
  deleteZoneMapping
);

module.exports = router;
