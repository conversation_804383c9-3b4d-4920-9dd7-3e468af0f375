const { required } = require("joi");
const { Schema, model } = require("mongoose");

const arazCityZoneSchema = new Schema(
  {
    ITSID: {
      type: String,
      required: false,
      trim: true,
    },
    ITSName: {
      type: String,
      required: false,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
    },
    priority: {
      type: Number,
      required: false,
    },
    LDName: {
      type: String,
      required: false,
      trim: true,
    },
    alias: {
      type: String,
      required: false,
      trim: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: false,
    },
    jamiatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamiat",
      required: false,
    },
    jamaatID: {
      type: Schema.Types.ObjectId,
      ref: "Jamaat",
      required: false,
    },
    latitude: {
      type: String,
      required: false,
      trim: true,
    },
    longitude: {
      type: String,
      required: false,
      trim: true,
    },
    googleMapsLink: {
      type: String,
      required: false,
      trim: true,
    },
    radius: {
      type: Number,
      required: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    isSynchingZoneMapping:{
      type: Boolean,
      default: false
    },
    lastSynchedDate:{
      type: Date,
      required: false
    },
    lastSynchedBy:{
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false
    }
  },
  {
    timestamps: true,
  }
);

const ArazCityZone = model("ArazCityZone", arazCityZoneSchema);

module.exports = { ArazCityZone };
