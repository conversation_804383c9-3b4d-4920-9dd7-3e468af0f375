const { upload } = require("../../../middlewares/multer.middleware");
const { validate } = require("../../../middlewares/validation.middleware");

const {
  getAllArazCityZones,
  addArazCityZone,
  getSingleArazCityZone,
  editArazCityZone,
  deleteArazCityZone,
  getArazCityZoneByArazCity,
  uploadFile,
  updateFileStatus,
  getMasterFiles,
  getDownloadURL,
  deleteArazCityFile,
  getFiles,
} = require("../controllers/arazCityZone.controller");

const {
  addArazCityZoneSchema,
  getSingleArazCityZoneSchema,
  editArazCityZoneSchema,
  deleteArazCityZoneSchema,
  getArazCityZoneByArazCitySchema,
  uploadFileSchema,
  updateFileStatusSchema,
  getFilesSchema,
  getMasterFilesSchema,
  getDownloadURLSchema,
  deleteArazCityZoneFileSchema,
} = require("../validations/arazCityZone.validation");

const fileUpload = upload("araz_city_zone_files");

const router = require("express").Router();

router.get("/get", getAllArazCityZones);

router.post(
  "/add",
  validate(addArazCityZoneSchema, "body"),
  addArazCityZone
);

router.get(
  "/get/:id",
  validate(getSingleArazCityZoneSchema, "params"),
  getSingleArazCityZone
);

router.put(
  "/edit",
  validate(editArazCityZoneSchema, "body"),
  editArazCityZone
);

router.delete(
  "/delete",
  validate(deleteArazCityZoneSchema, "body"),
  deleteArazCityZone
);

router.get(
  "/get/by-araz-city/:id",
  validate(getArazCityZoneByArazCitySchema, "params"),
  getArazCityZoneByArazCity
);


router.post(
  "/upload/newFile",
  fileUpload.single("file"),
  validate(uploadFileSchema, "body"),
  uploadFile
);

router.put(
  "/approve",
  validate(updateFileStatusSchema, "body"),
  updateFileStatus
);

router.post("/get/araz-city-zone-files", validate(getFilesSchema, "body"), getFiles);

router.post(
  "/get/get-master-files",
  validate(getMasterFilesSchema, "body"),
  getMasterFiles
);

router.post(
  "/get/download-url",
  validate(getDownloadURLSchema, "body"),
  getDownloadURL
);

router.delete(
  "/upload/delete/:id",
  validate(deleteArazCityZoneFileSchema, "params"),
  deleteArazCityFile
);


module.exports = router;

