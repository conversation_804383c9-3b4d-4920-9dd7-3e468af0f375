const { validate } = require("../../../middlewares/validation.middleware")
const { addMessage, editMessage, getMessageRecipients, getMessageTypes } = require("../controllers/message.controller")
const { addEditMessageSchema, getRecipientsSchema } = require("../validations/message.validation")

const router = require("express").Router()

router.post("/get/message", validate(addEditMessageSchema, ["body"]), addMessage)

router.post("/get/edit/message", validate(addEditMessageSchema, ["body"]), editMessage)

router.post("/get/recipients", validate(getRecipientsSchema, ["body"]), getMessageRecipients)

router.get("/get/messageType", getMessageTypes)

module.exports = router