const { apiH<PERSON><PERSON>, apiResponse } = require("../../../utils/api.util");

const XLSX = require("xlsx");
const fs = require("fs");
const { RazaMapping } = require("../models/razaMapping.model");
const { KGUser } = require("../../hierarchy/models");
const { UPDATE_SUCCESS } = require("../../../utils/message.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const importDataFromExcel = apiHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: "No file uploaded." });
  }

  const miqaatID = req.body.miqaatID;
  const arazCityID = req.body.arazCityID;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const fileBuffer = fs.readFileSync(req.file.path);
  const workbook = XLSX.read(fileBuffer, { type: "buffer" });
  const sheet = workbook.Sheets[workbook.SheetNames[0]];
  const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 });


  if (!miqaatID || !arazCityID) {
    return res
      .status(400)
      .json({ error: "miqaatID and arazCityID are required in body." });
  }

  const razaMappingBulkOps = [];
  let successCount = 0;
  let failedCount = 0;
  const failedITSIDs = [];

  for (let i = 2; i < rows.length; i++) {
    const row = rows[i];
    const itsid = row[2]; // Column C
    const zone = row[11]; // Column L
    const rowNum = i + 1;

    console.log(`Processing row ${rowNum} | ITS ID: ${itsid}`);

    if (!itsid || itsid.toString().length !== 8) {
      console.log(`❌ Invalid ITS ID at row ${rowNum}`);
      failedCount++;
      failedITSIDs.push({ row: rowNum, itsid });
      continue;
    }

    razaMappingBulkOps.push({
      updateOne: {
        filter: {
          ITSID: itsid,
          miqaatID: miqaatID,
          arazCityID: arazCityID,
        },
        update: {
          $set: {
            MiqaatZone: zone,
            RazaStatus: "Has Raza",
          },
        },
        upsert: true,
      },
    });

    successCount++;
  }

  if (razaMappingBulkOps.length > 0) {
    await RazaMapping.bulkWrite(razaMappingBulkOps);
  }

  return apiResponse(
    UPDATE_SUCCESS,
    "Raza Mapping",
    {
      message: "Import completed.",
      successCount,
      failedCount,
      failedITSIDs,
    },
    res
  );
});

const syncUserRazaStatus = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, ITSIDs } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  if (!miqaatID || !arazCityID || !Array.isArray(ITSIDs)) {
    return res
      .status(400)
      .json({ error: "miqaatID, arazCityID and ITSIDs[] are required." });
  }

  const kgUserBulkOps = [];
  const failedITSIDs = [];
  let failedCount = 0;
  let successCount = 0;

  for (let i = 0; i < ITSIDs.length; i++) {
    const rowNum = i + 1;
    const ITSID = ITSIDs[i];

    const razaStatus = await RazaMapping.findOne({
      ITSID,
      miqaatID,
      arazCityID,
    });

    if (!razaStatus) {
      failedITSIDs.push({ row: rowNum, ITSID });
      failedCount++;
      continue;
    }

    const kgUser = await KGUser.findOne({ ITSID });
    if (!kgUser) {
      console.log(`❌ KGUser not found for ITS ID: ${ITSID} at row ${rowNum}`);
      failedITSIDs.push({ row: rowNum, ITSID });
      failedCount++;
      continue;
    }

    const miqaatIndex = kgUser.miqaats.findIndex(
      (m) =>
        m.miqaatID?.toString() === miqaatID &&
        m.arazCityID?.toString() === arazCityID
    );

    if (miqaatIndex === -1) {
      console.log(
        `❌ Miqaat not found in user for ITS ID: ${ITSID} at row ${rowNum}`
      );
      failedITSIDs.push({ row: rowNum, ITSID });
      failedCount++;
      continue;
    }

    const path = `miqaats.${miqaatIndex}.miqaatHR`;

    kgUserBulkOps.push({
      updateOne: {
        filter: { _id: kgUser._id },
        update: {
          $set: {
            [path]: {
              RazaStatus: razaStatus.RazaStatus,
              MiqaatZone: razaStatus.MiqaatZone || "",
            },
          },
        },
      },
    });

    successCount++;
  }

  if (kgUserBulkOps.length > 0) {
    await KGUser.bulkWrite(kgUserBulkOps);
  }

  return apiResponse(
    UPDATE_SUCCESS,
    "Raza Mapping",
    {
      message: "Sync completed.",
      successCount,
      failedCount,
      failedITSIDs,
    },
    res
  );
});

const importAndSyncRazaStatus = apiHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: "No file uploaded." });
  }

  const miqaatID = req.body.miqaatID;
  const arazCityID = req.body.arazCityID;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const fileBuffer = fs.readFileSync(req.file.path);
  const workbook = XLSX.read(fileBuffer, { type: "buffer" });
  const sheet = workbook.Sheets[workbook.SheetNames[0]];
  const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 });

  // Gather valid ITSIDs from file
  const incomingRows = rows
    .slice(2)
    .map((row, i) => {
      const itsid = row[2]?.toString();
      const zone = row[11]?.toString() || "";
      return { itsid, zone, rowNum: i + 3 };
    })
    .filter((r) => r.itsid && r.itsid.length === 8);

  const incomingITSIDs = incomingRows.map((r) => r.itsid);

  // Fetch existing mappings in bulk
  const existingMappings = await RazaMapping.find({
    ITSID: { $in: incomingITSIDs },
    miqaatID,
    arazCityID,
  }).lean();

  const existingLookup = existingMappings.reduce((acc, doc) => {
    acc[doc.ITSID] = { zone: doc.MiqaatZone, status: doc.RazaStatus };
    return acc;
  }, {});

  const razaOps = [];
  const toSyncITSIDs = [];
  const failedITSIDs = [];
  let diffCount = 0;

  for (const { itsid, zone, rowNum } of incomingRows) {
    const existing = existingLookup[itsid];
    const newStatus = "Has Raza";
    const changed =
      !existing || existing.status !== newStatus || existing.zone !== zone;

    if (!changed) continue;

    razaOps.push({
      updateOne: {
        filter: { ITSID: itsid, miqaatID, arazCityID },
        update: { $set: { RazaStatus: newStatus, MiqaatZone: zone } },
        upsert: true,
      },
    });

    toSyncITSIDs.push(itsid);
    diffCount++;
  }

  let mappingResult = {
    insertedCount: 0,
    upsertedCount: 0,
    matchedCount: 0,
    modifiedCount: 0,
  };
  if (razaOps.length > 0) {
    mappingResult = await RazaMapping.bulkWrite(razaOps, { ordered: false });
  }

  // Sync only the changed ITSIDs to KGUser
  const kgOps = [];
  for (const itsid of toSyncITSIDs) {
    const mapping = await RazaMapping.findOne({
      ITSID: itsid,
      miqaatID,
      arazCityID,
    });
    const kgUser = await KGUser.findOne({ ITSID: itsid });
    if (!kgUser) {
      failedITSIDs.push({ itsid, reason: "KGUser not found" });
      continue;
    }

    const miqaatList = Array.isArray(kgUser.miqaats) ? kgUser.miqaats : [];
    const miqaatIndex = miqaatList.findIndex(
      (m) =>
        m.miqaatID?.toString() === miqaatID &&
        m.arazCityID?.toString() === arazCityID
    );
    if (miqaatIndex === -1) {
      failedITSIDs.push({ itsid, reason: "Miqaat not found in KGUser" });
      continue;
    }

    const path = `miqaats.${miqaatIndex}.miqaatHR`;
    kgOps.push({
      updateOne: {
        filter: { _id: kgUser._id },
        update: {
          $set: {
            [path]: {
              RazaStatus: mapping.RazaStatus,
              MiqaatZone: mapping.MiqaatZone,
            },
          },
        },
      },
    });
  }

  let kgResult = { modifiedCount: 0 };
  if (kgOps.length > 0) {
    const r = await KGUser.bulkWrite(kgOps, { ordered: false });
    kgResult.modifiedCount = r.modifiedCount;
  }

  return apiResponse(
    UPDATE_SUCCESS,
    "Raza Status Import & Sync",
    {
      totalRows: incomingRows.length,
      diffCount,
      mappingStats: {
        inserted: mappingResult.upsertedCount,
        modified: mappingResult.modifiedCount,
        matched: mappingResult.matchedCount,
      },
      syncedCount: kgResult.modifiedCount,
      failedCount: failedITSIDs.length,
      failedITSIDs,
    },
    res
  );
});

module.exports = {
  importDataFromExcel,
  syncUserRazaStatus,
  importAndSyncRazaStatus,
};
