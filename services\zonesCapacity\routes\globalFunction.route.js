const router = require("express").Router();
const { validate } = require("../../../middlewares/validation.middleware");
const {
  createFunction,
  getAllFunctions,
  getFunctionById,
  updateFunction,
  deleteFunction,
} = require("../controllers/globalFunction.controller");
const {
  addFunctionSchema,
  getSingleFunctionSchema,
  editFunctionSchema,
  deleteFunctionSchema,
} = require("../validations/globalFunction.validation");

router.get("/get", getAllFunctions);

router.post(
  "/add",
  validate(addFunctionSchema, "body"),
  createFunction
);

router.get(
  "/get/:id",
  validate(getSingleFunctionSchema, "params"),
  getFunctionById
);

router.put(
  "/edit",
  validate(editFunctionSchema, "body"),
  updateFunction
);

router.delete(
  "/delete/:id",
  validate(deleteFunctionSchema, "params"),
  deleteFunction
);

module.exports = router;
