const Joi = require("joi");
const { idValidation, stringValidation } = require("../../../utils/validator.util");

const statusTypes = ["DRAFT", "APPROVED", "UNLOCKED"];
const venueTypes = ["ZONES", "WAAZ_VENUE"];

const uploadFileSchema = Joi.object({
  arazCityZoneID: idValidation,
  venueType: Joi.string()
    .valid(...venueTypes)
    .required(),
  venueID: Joi.when("venueType", {
    not: "ZONES",
    then: idValidation.required(),
    otherwise: Joi.forbidden(),
  }),
  functionID: Joi.when("venueType", {
    not: "ZONES",
    then: idValidation.required(),
    otherwise: Joi.forbidden(),
  }),
});

const updateFileStatusSchema = Joi.object({
  fileId: idValidation,
  status: Joi.string()
    .valid(...statusTypes)
    .required(),
});

const getFilesSchema = Joi.object({
  arazCityZoneID: idValidation,
  venueType: Joi.string()
    .valid(...venueTypes)
    .optional(),
  venueID: Joi.when("venueType", {
    not: "ZONES",
    then: idValidation.required(),
    otherwise: Joi.forbidden(),
  }),
  functionID: Joi.when("venueType", {
    not: "ZONES",
    then: Joi.alternatives()
      .try(idValidation, Joi.string().valid("null"))
      .required(),
    otherwise: Joi.forbidden(),
  }),
});

const getFileByIdSchema = Joi.object({
  fileId: idValidation,
});

const getMasterFilesSchema = Joi.object({
  arazCityZoneID: idValidation,
  venueType: Joi.string()
    .valid(...venueTypes)
    .optional(),
  venueID: Joi.when("venueType", {
    not: "ZONES",
    then: idValidation.required(),
    otherwise: Joi.forbidden(),
  }),
  functionID: Joi.when("venueType", {
    not: "ZONES",
    then: idValidation.required(),
    otherwise: Joi.forbidden(),
  }),
});

const getFileHistorySchema = Joi.object({
  fileId: idValidation,
});

const getDownloadURLSchema = Joi.object({
  fileKey: stringValidation,
});

const deleteArazCityZoneFileSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  uploadFileSchema,
  updateFileStatusSchema,
  getFilesSchema,
  getFileByIdSchema,
  getMasterFilesSchema,
  getFileHistorySchema,
  getDownloadURLSchema,
  deleteArazCityZoneFileSchema
};
