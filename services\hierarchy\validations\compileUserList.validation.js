const Joi = require("joi");
const { stringValidation, arrayStringValidation, idValidation, numberValidation, arrayIdValidation, booleanValidation } = require("../../../utils/validator.util");

const downloadCompileListSchema = Joi.object({
  departmentID: idValidation.optional()
})

const getUsersFromITSSchema = Joi.object({
  ITS_IDs: Joi.array().items(stringValidation.optional()),
  id: idValidation.optional()
});

const getDepartmentWiseUsersSchema = Joi.object({
  departmentID: idValidation.optional(),
  arazCityID: idValidation.optional(),
  id: idValidation.optional()
})

const addCompileListSchema = Joi.object({
  name: stringValidation,
  approvedForRaza: booleanValidation,
  users: Joi.array().items(Joi.object({
    _id: idValidation,
    logo: stringValidation,
    ITSID: stringValidation,
    name: stringValidation,
    jamiatID: idValidation,
    jamaatID: idValidation,
    departmentID: idValidation,
    kgTypeID: idValidation.optional().allow(""),
    hierarchyPositionID: idValidation.optional().allow(""),
    functionID: idValidation.optional().allow(""),
    khidmatZone: stringValidation.optional().allow(""),
    travelPriority: idValidation.optional().allow(""),
    razaRecommendation: idValidation.optional().allow(""),
    travelCities: arrayIdValidation.optional().allow(""),
    recommendedBy: idValidation.optional().allow(""),
    remarks: stringValidation.optional().allow("")
  })),
})

const editCompileListSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
  approvedForRaza: booleanValidation,
  users: Joi.array().items(Joi.object({
    _id: idValidation,
    logo: stringValidation,
    ITSID: stringValidation,
    name: stringValidation,
    jamiatID: idValidation,
    jamaatID: idValidation,
    departmentID: idValidation,
    kgTypeID: idValidation.optional().allow(""),
    hierarchyPositionID: idValidation.optional().allow(""),
    functionID: idValidation.optional().allow(""),
    khidmatZone: stringValidation.optional().allow(""),
    travelPriority: idValidation.optional().allow(""),
    razaRecommendation: idValidation.optional().allow(""),
    travelCities: arrayIdValidation.optional().allow(""),
    recommendedBy: idValidation.optional().allow(""),
    remarks: stringValidation.optional().allow(""),
  })),
})

const editCompileListUserSchema = Joi.object({
  id: idValidation,
  userID: idValidation,
  ITSID: stringValidation,
  departmentID: idValidation,
  kgTypeID: idValidation.optional().allow(""),
  hierarchyPositionID: idValidation.optional().allow(""),
  functionID: idValidation.optional().allow(""),
  khidmatZone: stringValidation.optional().allow(""),
  travelPriority: idValidation.optional().allow(""),
  razaRecommendation: idValidation.optional().allow(""),
  travelCities: arrayIdValidation.optional().allow(""),
  recommendedBy: idValidation.optional().allow(""),
  remarks: stringValidation.optional().allow(""),
})

const addCompileListQuotaSchema = Joi.object({
  id: idValidation,
  departments: Joi.array().items(Joi.object({
    departmentID: idValidation,
    quota: numberValidation
  }))
})

const deleteCompileListSchema = Joi.object({
  id: idValidation
})

const deleteCompileListUserSchema = Joi.object({
  id: idValidation,
  userID: idValidation,
})

module.exports = {
  downloadCompileListSchema,
  getUsersFromITSSchema,
  getDepartmentWiseUsersSchema,
  addCompileListSchema,
  addCompileListQuotaSchema,
  editCompileListSchema,
  editCompileListUserSchema,
  deleteCompileListSchema,
  deleteCompileListUserSchema
}