const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  uploadFileToS3,
  generatePreSignedURLs,
  generateGetPreSignedURL,
} = require("../../../utils/aws.util");
const {
  CUSTOM_ERROR,
  NOT_FOUND,
  FETCH,
  CUSTOM_SUCCESS,
} = require("../../../utils/message.util");
const {
  getSurveysForUser,
} = require("../aggregations/surveyResponse.aggregation");
const { SurveyResponse, SurveyForm, KGUser } = require("../models");

const isWithinSurveyTimeFrame = (survey) => {
  if (survey.alwaysAvailable) {
    return true;
  }

  const now = new Date();
  const currentTimeInMinutes = now.getHours() * 60 + now.getMinutes();

  if (survey.startTime && survey.endTime) {
    const start = new Date(Number(survey.startTime)); // convert string to number
    const end = new Date(Number(survey.endTime));

    const startTimeInMinutes = start.getHours() * 60 + start.getMinutes();
    const endTimeInMinutes = end.getHours() * 60 + end.getMinutes();

    // Handle overnight case like 10 PM to 2 AM
    if (startTimeInMinutes > endTimeInMinutes) {
      return (
        currentTimeInMinutes >= startTimeInMinutes ||
        currentTimeInMinutes <= endTimeInMinutes
      );
    } else {
      return (
        currentTimeInMinutes >= startTimeInMinutes &&
        currentTimeInMinutes <= endTimeInMinutes
      );
    }
  }

  return true;
};

const getSingleSurvey = async (req, res) => {
  try {
    const { id } = req.params;
    const userID = req.user._id;

    const survey = await SurveyForm.findById(id)
      .populate([
        { path: "createdBy", select: "name ITSID _id" },
        { path: "updatedBy", select: "name ITSID _id" },
        {
          path: "questions",
          match: { isActive: true }, // Filter only active questions
        },
      ])
      .select(
        "name questions createdBy updatedBy startTime endTime startDate endDate frequency alwaysAvailable questionMapping"
      )
      .lean();

    if (!survey) {
      return apiError(NOT_FOUND, "Survey", null, res);
    }

    // ✅ Filter out questions that are present in questionMapping
    const mappedQuestionIDs = new Set(
      survey.questionMapping.map((qm) => qm.questionID.toString())
    );

    survey.questions = survey.questions.filter(
      (q) => !mappedQuestionIDs.has(q._id.toString())
    );

    let hasSubmitted = false;
    let response = null;

    const query = {
      surveyID: id,
      userID,
    };

    if (survey.frequency?.toLowerCase() === "daily") {
      const now = new Date();
      const startOfUTC = new Date(
        Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate(),
          0,
          0,
          0
        )
      );
      const endOfUTC = new Date(
        Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate(),
          23,
          59,
          59,
          999
        )
      );
      query.createdAt = { $gte: startOfUTC, $lte: endOfUTC };
    }

    // const existingResponse = await SurveyResponse.findOne(query);
    // if (existingResponse) {
    //   hasSubmitted = true;
    //   response = existingResponse;
    // }

    // survey.hasResponded = hasSubmitted;
    // survey.response = response;
    survey.hasResponded = false;
    survey.response = null;
    survey.isWithinTimeFrame = isWithinSurveyTimeFrame(survey);

    return apiResponse(FETCH, "Survey", survey, res);
  } catch (error) {
    console.error(error);
    return apiError(CUSTOM_ERROR, "Something went wrong", null, res);
  }
};

const getSurveyResponse = async (req, res) => {
  try {
    const { id } = req.params;
    const surveyResponse = await SurveyResponse.findById(id).populate(
      "surveyID userID answers.questionID"
    );
    if (!surveyResponse) {
      return apiError(NOT_FOUND, "Survey response", null, res);
    }
    return apiResponse(FETCH, "Survey response", surveyResponse, res);
  } catch (error) {
    console.error(error);
    return apiError(CUSTOM_ERROR, "Something went wrong", null, res);
  }
};

const getSurveys = async (req, res) => {
  try {
    const surveys = await getSurveysForUser(req);

    if (!surveys) {
      return apiError(NOT_FOUND, "Survey", null, res);
    }

    return apiResponse(FETCH, "Survey", surveys, res);
  } catch (error) {
    console.log(error);
    return apiError(CUSTOM_ERROR, "Something went wrong", null, res);
  }
};

const addSurveyResponse = apiHandler(async (req, res) => {
  try {
    const { surveyID, answers, responseID } = req.body;

    const survey = await SurveyForm.findById(surveyID)
      .select("frequency startTime endTime alwaysAvailable questions")
      .populate("questions", "required questionType");

    if (!survey) {
      return apiError(NOT_FOUND, "Survey", null, res);
    }

    // Check if current time is within survey time frame
    if (!isWithinSurveyTimeFrame(survey)) {
      return apiError(
        CUSTOM_ERROR,
        "Survey is not available at this time",
        null,
        res
      );
    }

    const query = {
      userID: req.user._id,
      surveyID,
    };

    // Handle daily frequency - check for today's submission
    if (survey.frequency?.toLowerCase() === "daily") {
      const now = new Date();
      const startOfUTC = new Date(
        Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate(),
          0,
          0,
          0
        )
      );
      const endOfUTC = new Date(
        Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate(),
          23,
          59,
          59,
          999
        )
      );

      query.createdAt = { $gte: startOfUTC, $lte: endOfUTC };
    }

    // If responseID is provided, this is an edit operation
    if (responseID) {
      const existingResponse = await SurveyResponse.findOne({
        _id: responseID,
        userID: req.user._id,
        surveyID,
      });

      if (!existingResponse) {
        return apiError(
          NOT_FOUND,
          "Survey response not found or you don't have permission to edit it",
          null,
          res
        );
      }

      // For daily surveys, check if the response was created today
      if (survey.frequency?.toLowerCase() === "daily") {
        const responseDate = new Date(existingResponse.createdAt);
        const today = new Date();

        const isToday =
          responseDate.getUTCFullYear() === today.getUTCFullYear() &&
          responseDate.getUTCMonth() === today.getUTCMonth() &&
          responseDate.getUTCDate() === today.getUTCDate();

        if (!isToday) {
          return apiError(
            CUSTOM_ERROR,
            "You can only edit today's daily survey response",
            null,
            res
          );
        }
      }

      // Update the existing response
      const updatedResponse = await SurveyResponse.findByIdAndUpdate(
        responseID,
        {
          answers,
          updatedAt: new Date(),
        },
        { new: true }
      );

      return apiResponse(
        CUSTOM_SUCCESS,
        "Survey response updated successfully",
        updatedResponse,
        res
      );
    } else {
      // This is a new response creation
      // const existingResponse = await SurveyResponse.findOne(query);
      // if (existingResponse) {
      //   return apiError(
      //     CUSTOM_ERROR,
      //     "You have already submitted this survey",
      //     null,
      //     res
      //   );
      // }

      const newSurveyResponse = await SurveyResponse.create({
        surveyID,
        userID: req.user._id,
        answers,
      });

      return apiResponse(
        CUSTOM_SUCCESS,
        "Survey response added successfully",
        newSurveyResponse,
        res
      );
    }
  } catch (error) {
    return apiError(CUSTOM_ERROR, "Something went wrong", error, res);
  }
});

const uploadFilesInSurvey = apiHandler(async (req, res) => {
  if (!req.files || req.files.length === 0) {
    return apiResponse(CUSTOM_ERROR, "No files uploaded", {}, res);
  }

  if (req.files.length > 3) {
    return apiResponse(CUSTOM_ERROR, "You can only upload up to 3 files", {}, res);
  }

  const maxFileSize = 10 * 1024 * 1024;
  for (const file of req.files) {
    if (file.size > maxFileSize) {
      return apiResponse(CUSTOM_ERROR, `File size should not exceed 10 MB`, {}, res);
    }
  }

  const surveyID = req.params.id;
  const awsGroupID = surveyID || req.user._id;

  const fileDetails = req.files.map((file) => ({
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  }));

  const preSignedURLs = await generatePreSignedURLs(
    "survey-response-files",
    awsGroupID,
    fileDetails
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return apiResponse(
      CUSTOM_ERROR,
      "Failed to generate pre-signed URLs",
      {},
      res
    );
  }

  const uploadPromises = preSignedURLs.map(({ fileKey, preSignedURL }, index) =>
    uploadFileToS3(req.files[index], preSignedURL, fileKey)
  );

  const uploadedFiles = (await Promise.all(uploadPromises)).filter(Boolean);

  return apiResponse(
    CUSTOM_SUCCESS,
    "Files Uploaded Successfully",
    uploadedFiles,
    res
  );
});


const getSurveyFiles = apiHandler(async (req, res) => {
  const { fileKey } = req.body;

  if (!fileKey) {
    return apiResponse(CUSTOM_ERROR, "File key is required", null, res);
  }

  const preSignedURL = await generateGetPreSignedURL(fileKey);

  return apiResponse(FETCH, "Document", { preSignedURL }, res);
});

module.exports = {
  addSurveyResponse,
  getSurveyResponse,
  getSurveys,
  getSingleSurvey,
  uploadFilesInSurvey,
  getSurveyFiles,
};
