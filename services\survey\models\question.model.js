const { required } = require("joi");
const { Schema, model } = require("mongoose");

const questionTypeList = {
  text: "text",
  number: "number",
  textarea: "textarea",
  select: "select",
  multiselect: "multiselect",
  radio: "radio",
  date: "date",
  slider: "slider",
  checkbox: "checkbox",
  checkbox: "checkbox",
  time: "time",
  file: "file",
  doubleTextBox: "doubleTextBox",
};
const chartEnums = ["BAR", "PIE", "LINEAR", "TABLE", "RADAR"];

const QuestionSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    questionType: {
      type: String,
      required: true,
      enum: Object.values(questionTypeList),
    },
    options: {
      type: [
        {
          placeholder: {
            type: String,
            required: true,
          },
          weightage: {
            type: Number,
          },
          required: {
            type: Boolean,
            default: false,
          },
        },
      ],
      default: [],
    },
    otherOptions: {
      type: [
        {
          placeholder: {
            type: Schema.Types.Mixed,
          },
          weightage: {
            type: Number,
          },
          required: {
            type: Boolean,
            default: false,
          },
        },
      ],
      default: [],
    },
    required: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
    },
    department: [
      {
        type: Schema.Types.ObjectId,
        ref: "Department",
      },
    ],
    chartType: {
      type: String,
      enum: chartEnums,
    },

    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

const Question = new model("Question", QuestionSchema);

module.exports = {
  Question,
  questionTypeList,
  chartEnums,
};
