const {
  getAllWaazVenue,
  addEditWaazVenue,
  getSingleWaazVenue,
  deleteWaazVenue,
  approveWaazVenue,
  activateWaazVenue,
  uploadWaazVenueFile,
  updateWaazVenueFileStatus,
  getWaazVenueMasterFiles,
  getWaazVenueFileDownloadURL,
  deleteWaazVenueFile,
  getWaazVenueFiles,
  finalCapacityWaazVenue,
} = require("../controllers/waazVenue.controller");
const {
  addWaazVenueSchema,
  getSingleWaazVenueSchema,
  editWaazVenueSchema,
  deleteWaazVenueSchema,
  getAllWaazVenueSchema,
  approveWaazVenueSchema,
  activateWaazVenueSchema,

  uploadFileSchema,
  updateFileStatusSchema,
  getFilesSchema,
  getMasterFilesSchema,
  getDownloadURLSchema,
  deleteArazCityZoneWaazVenueFileSchema,
  finalCapacityWaazVenueSchema,

} = require("../validations/waazVenue.validation");
const { validate } = require("../../../middlewares/validation.middleware");
const { upload } = require("../../../middlewares/multer.middleware");

const router = require("express").Router();

router.post("/get",validate(getAllWaazVenueSchema, "body"), getAllWaazVenue);

router.post("/add", validate(addWaazVenueSchema, "body"), addEditWaazVenue);
router.patch("/approve-waaz-venue", validate(approveWaazVenueSchema, "body"), approveWaazVenue);
router.patch("/activate-waaz-venue", validate(activateWaazVenueSchema, "body"), activateWaazVenue);
router.patch(
  "/final-capacity",
  validate(finalCapacityWaazVenueSchema, "body"),
  finalCapacityWaazVenue
);

router.get(
  "/get/:id",
  validate(getSingleWaazVenueSchema, "params"),
  getSingleWaazVenue
);

router.put("/edit", validate(editWaazVenueSchema, "body"), addEditWaazVenue);

router.delete(
  "/delete/:id",
  validate(deleteWaazVenueSchema, "params"),
  deleteWaazVenue
);





const fileUpload = upload("araz_city_waaz_venue_files");

router.post(
  "/upload/:function",
  fileUpload.single("file"),
  validate(uploadFileSchema, "body"),
  uploadWaazVenueFile
);

router.put(
  "/approve/:fucntion",
  validate(updateFileStatusSchema, "body"),
  updateWaazVenueFileStatus
);

router.post("/upload/:function/get", validate(getFilesSchema, "body"), getWaazVenueFiles);

router.post(
  "/upload/file/get-master-files",
  validate(getMasterFilesSchema, "body"),
  getWaazVenueMasterFiles
);

router.post(
  "/get/download-url",
  validate(getDownloadURLSchema, "body"),
  getWaazVenueFileDownloadURL
);

router.delete(
  "/upload/:function/delete/:id",
  validate(deleteArazCityZoneWaazVenueFileSchema, "params"),
  deleteWaazVenueFile
);


module.exports = router;
