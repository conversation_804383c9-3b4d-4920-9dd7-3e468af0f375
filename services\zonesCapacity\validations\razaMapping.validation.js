const Joi = require("joi");
const {
  idValidation,
  stringValidation,
} = require("../../../utils/validator.util");

const importDataFromExcelSchema = Joi.object({
  miqaatID: idValidation.required(),
  arazCityID: idValidation.required(),
});

const syncUserRazaStatusSchema = Joi.object({
  miqaatID: idValidation.required(),
  arazCityID: idValidation.required(),
  ITSIDs: Joi.array().items(stringValidation).required(),
});

module.exports = {
  importDataFromExcelSchema,
  syncUserRazaStatusSchema,
};
