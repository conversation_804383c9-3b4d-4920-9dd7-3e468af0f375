const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
} = require("../../../utils/validator.util");

const addArazCitySchema = Joi.object({
  name: stringValidation,
  miqaatID: idValidation,
  jamiats: Joi.array().items(stringValidation).optional(),
  jamaats: Joi.array().items(stringValidation).optional(),
  fasalDate: dateValidation.optional(),
  addToOpenProject: booleanValidation.optional(),
  showPositionAlias: booleanValidation.optional(),
  timeZoneID: idValidation.optional(),
}).unknown();

const getSingleArazCitySchema = Joi.object({
  id: idValidation,
});

const editArazCitySchema = Joi.object({
  id: idValidation,
  name: stringValidation,
  miqaatID: idValidation,
  jamiats: Joi.array().items(stringValidation).optional(),
  jamaats: Joi.array().items(stringValidation).optional(),
  fasalDate: dateValidation.optional(),
  addToOpenProject: booleanValidation.optional(),
  showPositionAlias: booleanValidation.optional(),
  timeZoneID: idValidation.optional(),
  status: booleanValidation,
}).unknown();

const deleteArazCitySchema = Joi.object({
  id: idValidation,
});

const getArazCityByMiqaatSchema = Joi.object({
  id: idValidation,
});

const getArazCityReportSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
});

module.exports = {
  addArazCitySchema,
  getSingleArazCitySchema,
  editArazCitySchema,
  deleteArazCitySchema,
  getArazCityByMiqaatSchema,
  getArazCityReportSchema,
};
