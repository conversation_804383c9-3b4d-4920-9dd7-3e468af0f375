const router = require("express").Router();

const { validate } = require("../../../middlewares/validation.middleware");
const {
  createMeeting,
  getMeetings,
  getMeeting,
  editMeeting,
  deleteMeeting,
  addMOM,
  sendNotificationToInvitees,
  sendReminderToAgendaAssignees,
  getAssignees,
  addEditAgenda,
  getAllMeetings,
} = require("../controllers/meeting.controller");
const {
  addMeetingSchema,
  getMeetingParamsSchema,
  editMeetingSchema,
  addMOMSchema,
  sendReminderToAgendaAssigneesSchema,
  agendaSchema,
  getAllMeetingsSchema,
} = require("../validations/meeting.validation");

router.post("/get/assignees", getAssignees);
router.post("/add", validate(addMeetingSchema, "body"), createMeeting);
router.get("/add/send-reminder/:id", validate(getMeetingParamsSchema, "params"), sendNotificationToInvitees);
router.get("/add/send-reminder/:meetingId/:agendaId", validate(sendReminderToAgendaAssigneesSchema, "params"), sendReminderToAgendaAssignees);
router.post("/get", getMeetings);
router.post("/get/all-meetings",validate(getAllMeetingsSchema), getAllMeetings);
router.post("/get/:id", validate(getMeetingParamsSchema, "params"), getMeeting);
router.put("/edit/:id", validate(editMeetingSchema, "body"), editMeeting);
router.post("/add/agenda/:id", validate(agendaSchema, "body"), addEditAgenda);
router.delete(
  "/delete/:id",
  validate(getMeetingParamsSchema, "params"),
  deleteMeeting
);
router.patch("/add/mom", validate(addMOMSchema, "body"), addMOM);

module.exports = router;
