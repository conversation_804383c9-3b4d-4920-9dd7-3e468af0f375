const { validate } = require("../../../middlewares/validation.middleware");
const { getSmeUserDepartments, getRecommendationArazCities, getRecommendedUsers, getRecommendedUser, addRecommendedUsers, editRecommendedUser, deleteRecommendedUser, getTravelPriorities, getRazaRecommendations, getUsersFromITS, getDepartmentQuota } = require("../controllers/recommendUser.controller");
const { addRecommendedUsersSchema, editRecommendedUserSchema, deleteRecommendedUserSchema, getUsersFromITSSchema } = require("../validations/recommendUser.validation");

const router = require("express").Router();

router.post("/get/import-its-users", validate(getUsersFromITSSchema), getUsersFromITS)

router.get("/get/departments", getSmeUserDepartments)

router.get("/get/araz-cities", getRecommendationArazCities)

router.get("/get/travel-priorities", getTravelPriorities)

router.get("/get/raza-recommendations", getRazaRecommendations)

router.get("/get/departmental-quota", getDepartmentQuota)

router.get("/get", getRecommendedUsers)

router.get("/get/:id", getRecommendedUser)

router.patch("/add", validate(addRecommendedUsersSchema, "body"), addRecommendedUsers)

router.patch("/edit", validate(editRecommendedUserSchema, "body"), editRecommendedUser)

router.delete("/delete/:id", validate(deleteRecommendedUserSchema, "params"), deleteRecommendedUser)


module.exports = router