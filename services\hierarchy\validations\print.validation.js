const Joi = require("joi");
const {
  idValidation,
  arrayStringValidation,
  arrayIdValidation,
} = require("../../../utils/validator.util");
const { recipientMeta } = require("../../communication/models");

const unprintListSchema = Joi.object({
  miqaatID: idValidation.required().label("Miqaat ID"),
  arazCityID: idValidation.required().label("ArazCity ID"),
  ITSIDs: arrayStringValidation.optional().allow(null).label("ITS IDs"),
});

const getListSchema = Joi.object({
  miqaatID: idValidation.required().label("Miqaat ID"),
  arazCityID: idValidation.required().label("ArazCity ID"),
  memberType: Joi.string()
    .trim()
    .required()
    .valid(...recipientMeta.allTypes)
    .label("Message type")
    .messages({
      "any.only": "Message type is required",
      "string.empty": "Message type is required",
    }),
  departmentIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.departmentTypes),
    then: arrayIdValidation.min(1),
  }),
  zoneIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.zoneTypes),
    then: arrayIdValidation.min(1),
  }),
  positionIDs: arrayIdValidation.optional().default([]).label("Position IDs"),
  kgGroupIDs: arrayIdValidation.optional().default([]).label("KG Group IDs"),
  kgTypeIDs: arrayIdValidation.optional().default([]).label("KG Type IDs"),
  ITSIDs: Joi.when("memberType", {
    is: Joi.valid(...recipientMeta.ITSTypes),
    then: arrayStringValidation.min(1),
  }),
});

module.exports = {
  unprintListSchema,
  getListSchema,
};
