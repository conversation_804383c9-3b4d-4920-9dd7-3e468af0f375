const Joi = require("joi");
const {
  idValidation,
  stringValidation,
} = require("../../../utils/validator.util");

const measurementSchema = Joi.object({
  value: Joi.number().required(),
  unit: Joi.string().valid("feet", "meter").required(),
});

const mawaidVenueBaseSchema = {
  name: stringValidation,
  uniqueName: Joi.string().trim().optional(),
  miqaatID: idValidation,
  arazCityID: idValidation,
  waazVenueID: idValidation.optional().allow(null,""),
  arazCityZoneID: idValidation,
  mawaidVenueTypeID: idValidation,
  mawaidVenueSuitabilityID: idValidation,
  mawaidVenueArea: Joi.object({
    length: measurementSchema,
    breadth: measurementSchema,
  }).required(),
  supportingKitchenArea: Joi.object({
    length: measurementSchema,
    breadth: measurementSchema,
  }),
  distanceFromKitchen: Joi.number().allow(null),
  plannedThoks: Joi.number().allow(null),
  numberOfThoks: Joi.number().allow(null),
  estimatedCapacity: Joi.number().allow(null),
  finalCapacity: Joi.number().allow(null),
  kitchenAvailability: Joi.string().allow(null, ""),
  whichFloor: Joi.number().allow(null),
  negativeArea: Joi.number().allow(null),
  kitchenCapacity: Joi.number().allow(null),
  cookingCapacity: Joi.number().allow(null),
  mainCookingArea: Joi.object({
    length: measurementSchema,
    breadth: measurementSchema,
  }),
  remarks: Joi.string().allow(null, ""),
};

const addMawaidVenueSchema = Joi.object({
  ...mawaidVenueBaseSchema,
});

const editMawaidVenueSchema = Joi.object({
  _id: idValidation,
  ...mawaidVenueBaseSchema,
});

const getSingleMawaidVenueSchema = Joi.object({
  id: idValidation,
});

const deleteMawaidVenueSchema = Joi.object({
  id: idValidation,
});

const getAllMawaidVenuesSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
});

const activateMawaidVenueSchema = Joi.object({
  id:idValidation,
  status: stringValidation,
});

const finalCapacityMawaidVenueSchema = Joi.object({
  id:idValidation,
  finalCapacity: Joi.number().required(),
});




const statusTypes = ["DRAFT", "APPROVED", "UNLOCKED"];

const uploadFileSchema = Joi.object({
  functionID: idValidation,
  arazCityZoneID: idValidation,
  mawaidVenueID: idValidation,
  miqaatID:idValidation,
  arazCityID:idValidation
});

const updateFileStatusSchema = Joi.object({
  fileId: idValidation,
  status: Joi.string()
    .valid(...statusTypes)
    .required(),
});

const getFilesSchema = Joi.object({
  functionID: idValidation,
  arazCityZoneID: idValidation,
  mawaidVenueID: idValidation,
  miqaatID:idValidation,
  arazCityID:idValidation
});

const getDownloadURLSchema = Joi.object({
  fileKey: stringValidation,
});

const deleteArazCityZoneMawaidVenueFileSchema = Joi.object({
  id: idValidation,
  function: stringValidation,
});
const getMasterFilesSchema = Joi.object({
  arazCityZoneID: idValidation,
  miqaatID:idValidation,
  arazCityID:idValidation
});

module.exports = {
  addMawaidVenueSchema,
  editMawaidVenueSchema,
  getSingleMawaidVenueSchema,
  deleteMawaidVenueSchema,
  getAllMawaidVenuesSchema,
  activateMawaidVenueSchema,
  finalCapacityMawaidVenueSchema,
  uploadFileSchema,
  updateFileStatusSchema,
  getFilesSchema,
  getDownloadURLSchema,
  deleteArazCityZoneMawaidVenueFileSchema,
  getMasterFilesSchema,
};
