const { Schema, model } = require("mongoose");

const functionSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    priority: {
      type: Number,
      required: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const Function = model("Function", functionSchema);

module.exports = { Function };
