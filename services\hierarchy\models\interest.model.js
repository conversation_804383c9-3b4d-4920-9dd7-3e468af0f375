const { Schema, model } = require("mongoose");

const interestedDepartmentsSchema = new Schema(
  {
    departmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: false,
    },
    functionID: {
      type: Schema.Types.ObjectId,
      ref: "Function",
      required: false,
    },
  },
  { _id: false }
);

const interestSchema = new Schema(
  {
    userID: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: false,
    },
    ITSID: {
      type: String,
      required: false,
    },
    status: {
      type: String,
      required: false,
    },
    interestOne: interestedDepartmentsSchema,
    interestTwo: interestedDepartmentsSchema,
    interestThree: interestedDepartmentsSchema,
  },
  { timestamps: true }
);

const Interest = model("Interest", interestSchema);

module.exports = { Interest };
