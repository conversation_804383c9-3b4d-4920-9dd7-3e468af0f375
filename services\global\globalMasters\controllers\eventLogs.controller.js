const { apiResponse, apiError } = require("../../../utils/api.util");
const {
  FETCH,
  NOT_FOUND,
} = require("../../../utils/message.util");
const { EventLogs } = require("../models/eventLogs.model");

const getAllEventLogs = async (req, res) => {
  const eventLogs = await EventLogs.find().populate({path: "performedBy", select: "name ITSID logo"}).sort({ createdAt: -1 }).lean();
  if (!eventLogs) return apiError(NOT_FOUND, "Event Logs", null, res);
  return apiResponse(FETCH, "Event Logs", eventLogs, res);
};

module.exports = {
  getAllEventLogs,
};
