const express = require("express");
const router = express.Router();

const {
  getAllSurveysForReports,
  getSurveyResults,
} = require("../controllers/reports.controller");
const { getSurveyFiles } = require("../controllers/surveyResponse.controller");

router.post("/get", getAllSurveysForReports);

router.get("/get", getAllSurveysForReports);

router.post("/get/survey/file", getSurveyFiles);

router.post("/get/:id", getSurveyResults);

module.exports = router;
