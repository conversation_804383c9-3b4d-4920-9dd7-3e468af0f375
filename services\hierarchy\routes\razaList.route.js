const router = require("express").Router()
const { validate } = require("../../../middlewares/validation.middleware")
const { getCompiledRazaList, mergeUserIntoHierarchy } = require("../controllers/razaList.controller")
const { getCompiledRazaListSchema, mergeUserIntoHierarchySchema } = require("../validations/razaList.validation")

router.get("/get/:miqaatID", validate(getCompiledRazaListSchema), getCompiledRazaList)

router.patch("/update", validate(mergeUserIntoHierarchySchema), mergeUserIntoHierarchy)

module.exports = router