const { Schema, model } = require("mongoose");

const KGRequisitionByDepartmentSchema = new Schema(
  {
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: true,
    },
    departmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: true,
    },
    requiredMardoKgCount: {
      type: Number,
      required: true,
    },
    requiredBairaoKgCount: {
      type: Number,
      required: true,
    },
  },
  {
    timeseries: true,
    timestamps: true,
  }
);

const KgRequisitionByDepartment = model(
  "KGRequisitionByDepartment",
  KGRequisitionByDepartmentSchema
);

module.exports = {
  KgRequisitionByDepartment,
};
