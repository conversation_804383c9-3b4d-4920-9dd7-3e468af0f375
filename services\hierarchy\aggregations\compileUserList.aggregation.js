const { toObjectId } = require("../../../utils/misc.util");

const getDepartmentWiseUsersAggregation = (
  departmentID,
  arazCityID,
  compileListID = null
) => {

    const matchStage = {};

  if (arazCityID) {
    matchStage["smeRecommendation.travelCities"] = toObjectId(arazCityID);
  } else if (departmentID) {
    matchStage["smeRecommendation.departmentID"] = toObjectId(departmentID);
  }
  const pipline = [
    {
      $match: matchStage,
    },
    {
      $project: {
        age: 1,
        logo: 1,
        name: 1,
        LDName: 1,
        ITSID: 1,
        phone:1,
        gender:1,
        smeRecommendation: 1,
        jamiatID: 1,
        jamaatID: 1,
      },
    },
    // Lookup for jamiatID
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiatID",
      },
    },
    {
      $unwind: {
        path: "$jamiatID",
        preserveNullAndEmptyArrays: true,
      },
    },

    // Lookup for jamaatID
    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaatID",
      },
    },
    {
      $unwind: {
        path: "$jamaatID",
        preserveNullAndEmptyArrays: true,
      },
    },

    // Lookup for smeRecommendation.departmentID
    {
      $lookup: {
        from: "departments",
        localField: "smeRecommendation.departmentID",
        foreignField: "_id",
        as: "smeRecommendation.departmentID",
      },
    },
    {
      $unwind: {
        path: "$smeRecommendation.departmentID",
        preserveNullAndEmptyArrays: true,
      },
    },

    // smeRecommendation.kgTypeID
    {
      $lookup: {
        from: "kgtypes",
        localField: "smeRecommendation.kgTypeID",
        foreignField: "_id",
        as: "smeRecommendation.kgTypeID",
      },
    },
    {
      $unwind: {
        path: "$smeRecommendation.kgTypeID",
        preserveNullAndEmptyArrays: true,
      },
    },

    // smeRecommendation.hierarchyPositionID
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "smeRecommendation.hierarchyPositionID",
        foreignField: "_id",
        as: "smeRecommendation.hierarchyPositionID",
      },
    },
    {
      $unwind: {
        path: "$smeRecommendation.hierarchyPositionID",
        preserveNullAndEmptyArrays: true,
      },
    },

    // smeRecommendation.functionID
    {
      $lookup: {
        from: "functions",
        localField: "smeRecommendation.functionID",
        foreignField: "_id",
        as: "smeRecommendation.functionID",
      },
    },
    {
      $unwind: {
        path: "$smeRecommendation.functionID",
        preserveNullAndEmptyArrays: true,
      },
    },

    // smeRecommendation.travelPriority
    {
      $lookup: {
        from: "travelpriorities",
        localField: "smeRecommendation.travelPriority",
        foreignField: "_id",
        as: "smeRecommendation.travelPriority",
      },
    },
    {
      $unwind: {
        path: "$smeRecommendation.travelPriority",
        preserveNullAndEmptyArrays: true,
      },
    },

    // smeRecommendation.razaRecommendation
    {
      $lookup: {
        from: "razarecommendations",
        localField: "smeRecommendation.razaRecommendation",
        foreignField: "_id",
        as: "smeRecommendation.razaRecommendation",
      },
    },
    {
      $unwind: {
        path: "$smeRecommendation.razaRecommendation",
        preserveNullAndEmptyArrays: true,
      },
    },

    // smeRecommendation.travelCities (array of refs)
    {
      $lookup: {
        from: "travelarazcities",
        localField: "smeRecommendation.travelCities",
        foreignField: "_id",
        as: "smeRecommendation.travelCities",
      },
    },

    // smeRecommendation.recommendedBy
    {
      $lookup: {
        from: "kgusers", // Assuming the recommendedBy is also a KGUser
        localField: "smeRecommendation.recommendedBy",
        foreignField: "_id",
        as: "smeRecommendation.recommendedBy",
      },
    },
    {
      $unwind: {
        path: "$smeRecommendation.recommendedBy",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        age: 1,
        logo: 1,
        name: 1,
        LDName: 1,
        ITSID: 1,
        phone:1,
        gender: 1,

        "jamaatID._id": 1,
        "jamaatID.name": 1,

        "jamiatID._id": 1,
        "jamiatID.name": 1,

        "smeRecommendation.departmentID._id": 1,
        "smeRecommendation.departmentID.name": 1,
        "smeRecommendation.departmentID.LDName": 1,

        "smeRecommendation.functionID._id": 1,
        "smeRecommendation.functionID.name": 1,

        "smeRecommendation.khidmatZone": 1,

        "smeRecommendation.travelPriority._id": 1,
        "smeRecommendation.travelPriority.name": 1,

        "smeRecommendation.kgTypeID._id": 1,
        "smeRecommendation.kgTypeID.name": 1,

        "smeRecommendation.hierarchyPositionID._id": 1,
        "smeRecommendation.hierarchyPositionID.name": 1,

        "smeRecommendation.razaRecommendation._id": 1,
        "smeRecommendation.razaRecommendation.name": 1,

        "smeRecommendation.travelCities._id": 1,
        "smeRecommendation.travelCities.name": 1,

        "smeRecommendation.recommendedBy._id": 1,
        "smeRecommendation.recommendedBy.name": 1,

        isInCompileList: 1,
      }
    }
  ];

  if (compileListID) {
    pipline.splice(2, 0, {
      $lookup: {
        from: "compilelists",
        let: { userId: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  {
                    $eq: ["$_id", toObjectId(compileListID)],
                  },
                  {
                    $gt: [
                      {
                        $size: {
                          $filter: {
                            input: "$departments",
                            as: "dept",
                            cond: {
                              $in: ["$$userId", "$$dept.kgUsers"],
                            },
                          },
                        },
                      },
                      0,
                    ],
                  },
                ],
              },
            },
          },
        ],
        as: "isInCompileList",
      },
    });
    pipline.splice(3, 0, {
      $addFields: {
        isInCompileList: {
          $gt: [{ $size: "$isInCompileList" }, 0],
        },
      },
    });
  }

  return pipline;
};

module.exports = {
  getDepartmentWiseUsersAggregation
}