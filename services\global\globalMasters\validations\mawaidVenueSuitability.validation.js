const Joi = require("joi");
const {
  stringValidation,
  idValidation,
} = require("../../../utils/validator.util");

const addMawaidVenueSuitabilitySchema = Joi.object({
  name: stringValidation,
});

const getSingleMawaidVenueSuitabilitySchema = Joi.object({
  id: idValidation,
});

const editMawaidVenueSuitabilitySchema = Joi.object({
  id: idValidation,
  name: stringValidation,
});

const deleteMawaidVenueSuitabilitySchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addMawaidVenueSuitabilitySchema,
  getSingleMawaidVenueSuitabilitySchema,
  editMawaidVenueSuitabilitySchema,
  deleteMawaidVenueSuitabilitySchema,
};
