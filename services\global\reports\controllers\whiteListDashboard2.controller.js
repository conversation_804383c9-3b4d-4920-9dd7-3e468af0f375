const {
  api<PERSON><PERSON>po<PERSON>,
  api<PERSON><PERSON>r,
  api<PERSON><PERSON><PERSON>,
} = require("../../../../utils/api.util");
const { NOT_FOUND, FETCH } = require("../../../../utils/message.util");
const { CompileList } = require("../../../hierarchy/models");

const getWhiteListDashboard2 = apiHandler(async (req, res) => {
  const { complieListID, departmentID } = req.body;

  // Fetch all compile lists
  const compileLists = await CompileList.find({ _id: { $in: complieListID } })
    .populate({
      path: "departments.departmentID",
      model: "Department",
    })
    .populate({
      path: "departments.kgUsers",
      model: "KGUser",
      populate: [
        { path: "jamiatID", model: "Jamiat" },
        { path: "smeRecommendation.functionID", model: "Function" },
      ],
    });

  // Aggregate all departments from all compile lists
  let allDepartments = compileLists.flatMap((list) => list.departments);

  // Filter by departmentID if provided
  if (departmentID) {
    allDepartments = allDepartments.filter(
      (dept) => dept.departmentID._id.toString() === departmentID
    );
  }

  // Collect all KGUsers
  let allKgUsers = [];
  for (const dept of allDepartments) {
    allKgUsers = allKgUsers.concat(dept.kgUsers);
  }

  const totalCount = allKgUsers.length;

  const jamiatGroups = {};
  const functionGroups = {};

  allKgUsers.forEach((user) => {
    const jamiatName = user.jamiatID?.name || "Unknown";
    if (!jamiatGroups[jamiatName]) jamiatGroups[jamiatName] = [];
    jamiatGroups[jamiatName].push(user);

    const functionName = user.smeRecommendation?.functionID?.name || "No Function";
    if (!functionGroups[functionName]) functionGroups[functionName] = [];
    functionGroups[functionName].push(user);
  });

  const jamiatWiseRows = Object.keys(jamiatGroups).map((jamiatName) => {
    const count = jamiatGroups[jamiatName].length;
    const percentage =
      totalCount > 0 ? ((count / totalCount) * 100).toFixed(0) : "0";
    return { jamiatName, percentage: `${percentage}%`, total: count };
  });

  const functionWiseRows = Object.keys(functionGroups).map((functionName) => {
    const count = functionGroups[functionName].length;
    const percentage =
      totalCount > 0 ? ((count / totalCount) * 100).toFixed(0) : "0";
    return { functionName, percentage: `${percentage}%`, total: count };
  });

  const WhiteListCompileList = {
    total: totalCount.toString(),
    jamiatWise: {
      total: totalCount.toString(),
      rows: jamiatWiseRows,
    },
    functionWise: {
      total: totalCount.toString(),
      rows: functionWiseRows,
    },
  };

  apiResponse(FETCH, "White List Dashboard 2", WhiteListCompileList, res);
});

module.exports = {
  getWhiteListDashboard2,
};
