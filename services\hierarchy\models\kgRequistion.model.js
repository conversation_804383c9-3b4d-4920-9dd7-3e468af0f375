const {Schema, model} = require("mongoose");

const KGRequisitionSchema = new Schema(
  {
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    departmentID: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: false,
    },
    purpose: { type: String, required: false },
    khidmatGuzaarCount: { type: Number, required: true },
    status: {
      type: String,
      enum: ["Open", "Filled", "Closed"],
      default: "Open",
    },
    assignedCount: { type: Number, default: 0 },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    applications: [
      {
        applicant: {
          type: Schema.Types.ObjectId,
          ref: "KGUser",
          required: true,
        },
        status: {
          type: String,
          enum: ["Applied", "Assigned", "Rejected"],
          default: "Applied",
        },
        appliedAt: Date,
        assignedAt: Date,
        assignedBy: {
          type: Schema.Types.ObjectId,
          ref: "KGUser",
          required: false,
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);
const KgRequisition = model("KgRequisition", KGRequisitionSchema);

module.exports = { KgRequisition };
