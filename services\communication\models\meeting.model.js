const { Schema, model } = require("mongoose");
const { recipientMeta } = require("./message.model");

const timeSchema = new Schema(
  {
    hh: {
      type: Number,
      required: false,
    },
    mm: {
      type: Number,
      required: false,
    },
    period: {
      type: String,
      enum: ["AM", "PM"],
      required: false,
    },
  },
  { timestamps: true, _id: false }
);

const memeberSchema = new Schema({
  arazCityID: {
    type: Schema.Types.ObjectId,
    ref: "ArazCity",
    required: true,
  },
  memberType: {
    type: String,
    enum: recipientMeta.allTypes,
    required: true,
  },
  departmentIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "Department",
    },
  ],
  zoneIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
    },
  ],
  positionIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "HierarchyPosition",
    },
  ],
  kgGroupIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGGroup",
    },
  ],
  kgTypeIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGType",
    },
  ],
  ITSIDs: [
    {
      type: String,
    },
  ],
  members: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
    },
  ],
});

const meetingSchema = new Schema(
  {
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: false,
    },
    title: {
      type: String,
      required: true,
    },
    hostedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
    date: {
      type: String,
      required: false,
    },
    time: timeSchema,
    meetingUrl: {
      type: String,
      // required: true
    },
    meetingAddress: {
      type: String,
      // required: true
    },
    invities: [memeberSchema],
    meetingMembers: [
      {
        type: Schema.Types.ObjectId,
        ref: "KGUser",
      },
    ],
    systemRoles: [
      {
        type: Schema.Types.ObjectId,
        ref: "SystemRole",
      },
    ],
    ITSIDs: [
      {
        type: Schema.Types.ObjectId,
        ref: "KGUser",
      },
    ],
    agendas: [
      {
        language:{
          type: String,
          enum: ["ld", "en"],
          default: "en"
        },
        agenda: {
          type: String,
        },
        proceedings: {
          type: String,
        },
        actionable: {
          type: String,
        },
        assignee: [memeberSchema],
        systemRoles: [
          {
            type: Schema.Types.ObjectId,
            ref: "SystemRole",
          },
        ],
        ITSIDs: [
          {
            type: Schema.Types.ObjectId,
            ref: "KGUser",
          },
        ],
        assigneeMembers: [
          {
            type: Schema.Types.ObjectId,
            ref: "KGUser",
          },
        ],
      },
    ],
    mom: {
      type: String,
    }, // Minutes of Meeting
    meetingCreatedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

const Meeting = model("Meeting", meetingSchema);

module.exports = { Meeting };
