const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON><PERSON>r,
  apiResponse,
} = require("../../../utils/api.util");
const {
  Attendance,
  KGUser,
  ArazCity,
  Miqaat,
  ArazCityZone,
} = require("../models");

const { toObjectId } = require("../../../utils/misc.util");
const {
  NOT_FOUND,
  FETCH,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const haversine = require("haversine-distance");
const moment = require("moment-timezone");

const getProfile = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  const user = await KGUser.findById(req.user._id)
    .select(
      "_id name ITSID miqaats logo phone whatsapp gender LDName systemRoleID"
    )
    .populate([
      { path: "miqaats.hierarchyPositionID", select: "name alias" },
      { path: "miqaats.departmentID", select: "name LDName" },
      { path: "miqaats.arazCityZoneID", select: "name LDName" },
      {
        path: "miqaats.arazCityID",
        select: "name LDName showPositionAlias status",
      },
      { path: "miqaats.miqaatID", select: "name LDName status" },
      { path: "miqaats.kgTypeID", select: "name" },
      { path: "miqaats.kgGroupID", select: "name" },
      { path: "miqaats.functionID", select: "name" },
    ]);

  if (!user) {
    return apiError(NOT_FOUND, "User not found", null, res);
  }

  if (!Array.isArray(user.miqaats) || user.miqaats.length === 0) {
    return apiError(NOT_FOUND, "No Miqaats associated with user", null, res);
  }

  const matchedMiqaat = user.miqaats.find((miqaat) => {
    const matchMiqaatID = miqaatID
      ? miqaat?.miqaatID?._id?.toString() === miqaatID.toString()
      : true;
    const matchArazCityID = arazCityID
      ? miqaat?.arazCityID?._id?.toString() === arazCityID.toString()
      : true;

    return (
      matchMiqaatID &&
      matchArazCityID &&
      miqaat?.miqaatID?.status === "active" &&
      miqaat?.arazCityID?.status === true
    );
  });

  if (!matchedMiqaat) {
    return apiError(NOT_FOUND, "Matching Miqaat not found", null, res);
  }

  const {
    hierarchyPositionID: hierarchyPosition,
    departmentID: department,
    arazCityZoneID: arazCityZone,
    arazCityID: arazCityRef,
    miqaatID: miqaatRef,
    kgTypeID: kgType,
    kgGroupID: kgGroup,
    functionID: functionRef,
    miqaatHR: khidmatZone,
  } = matchedMiqaat;

  const {
    _id,
    name,
    LDName,
    ITSID,
    logo: Photo,
    phone,
    whatsapp: whatsappNumber,
    gender,
  } = user;

  const userData = {
    _id,
    Name: name,
    LDName,
    ITS: ITSID,
    Photo,
    phone,
    whatsappNumber,
    gender,
    hierarchyPosition,
    department,
    khidmatZone: khidmatZone?.name,
    razaStatus: khidmatZone?.RazaStatus === "Has Raza",
    arazCityZone,
    arazCity: arazCityRef,
    miqaat: miqaatRef,
    kgType,
    kgGroup,
    function: functionRef,
    showAlias: !!arazCityRef?.showPositionAlias,
    isAttendanceAlreadyMarked: false,
  };

  return apiResponse(FETCH, "User Data", userData, res);
});

const markAttendance = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, latitude, longitude } = req.body;

  const [user, arazCity, miqaat] = await Promise.all([
    KGUser.findById(req.user._id),
    ArazCity.findOne({ _id: toObjectId(arazCityID), status: true }).populate(
      "timeZoneID"
    ),
    Miqaat.findOne({ _id: toObjectId(miqaatID), status: "active" }),
  ]);

  if (!user || !arazCity || !miqaat) {
    return apiError(NOT_FOUND, "Invalid user, Araz City, or Miqaat", null, res);
  }
  if (!arazCity.timeZoneID?.value) {
    return apiError(
      CUSTOM_ERROR,
      "Time zone not configured for Araz City",
      null,
      res
    );
  }

  const matchedMiqaat = user.miqaats?.find(
    (m) =>
      m.miqaatID.toString() === miqaatID &&
      m.arazCityID.toString() === arazCityID
  );
  if (!matchedMiqaat) {
    return apiError(NOT_FOUND, "Matching Miqaat not found", null, res);
  }

  const userCoords = {
    lat: parseFloat(latitude),
    lon: parseFloat(longitude),
  };
  
  if (matchedMiqaat.arazCityZoneID) {
    const zone = await ArazCityZone.findById(matchedMiqaat.arazCityZoneID);
    if (!zone || !zone.latitude || !zone.longitude) {
      return apiError(NOT_FOUND, "Araz City Zone Coordinates", null, res);
    }

    const zoneCoords = {
      lat: parseFloat(zone.latitude),
      lon: parseFloat(zone.longitude),
    };

    if (!haversine(userCoords, zoneCoords) <= 500) {
      return apiError(
        CUSTOM_ERROR,
        `You are not within 500m of the Araz City Zone`,
        null,
        res
      );
    }
  }

  const now = moment().tz(ArazCity.timeZoneID?.value || "UTC");
  const start = now.clone().startOf("day");
  const end = start.clone().add(24, "hours");

  if (!now.isBetween(start, end)) {
    return apiError(NOT_FOUND, "Attendance window has passed", null, res);
  }

  const existingAttendance = await Attendance.findOne({
    userID: user._id,
    arazCityID,
    miqaatID,
    createdAt: { $gte: start.toDate(), $lt: end.toDate() },
  });

  if (existingAttendance) {
    return apiError(
      CUSTOM_SUCCESS,
      "Attendance already marked for today",
      null,
      res
    );
  }

  await new Attendance({
    userID: user._id,
    arazCityID,
    miqaatID,
    latitude: userCoords.lat,
    longitude: userCoords.lon,
  }).save();

  return apiResponse(
    CUSTOM_SUCCESS,
    "Attendance marked successfully",
    null,
    res
  );
});

module.exports = {
  getProfile,
  markAttendance,
};
