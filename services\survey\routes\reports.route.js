const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");
const { getSurveyResultsSchema } = require("../validations/report.validation");
const {
  getEligibleSurveysForReports,
  getSurveyResults,
} = require("../controllers/reports.controller");
const { getSurveyFiles } = require("../controllers/surveyResponse.controller");

router.get("/get", getEligibleSurveysForReports);
router.post("/get", getEligibleSurveysForReports);

router.post(
  "/get/survey/file",
  getSurveyFiles
);

router.post(
  "/get/:id",
  validate(getSurveyResultsSchema, "body"),
  getSurveyResults
);


module.exports = router;
