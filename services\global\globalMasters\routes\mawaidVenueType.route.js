const {
  getAllMawaidVenueTypes,
  addEditMawaidVenueType,
  getSingleMawaidVenueType,
  deleteMawaidVenueType,
} = require("../controllers/mawaidVenueType.controller");
const {
  addMawaidVenueTypeSchema,
  getSingleMawaidVenueTypeSchema,
  editMawaidVenueTypeSchema,
  deleteMawaidVenueTypeSchema,
} = require("../validations/mawaidVenueType.validation");
const { validate } = require("../../../middlewares/validation.middleware");

const router = require("express").Router();

router.get("/get", getAllMawaidVenueTypes);

router.post(
  "/add",
  validate(addMawaidVenueTypeSchema, "body"),
  addEditMawaidVenueType
);

router.get(
  "/get/:id",
  validate(getSingleMawaidVenueTypeSchema, "params"),
  getSingleMawaidVenueType
);

router.put(
  "/edit",
  validate(editMawaidVenueTypeSchema, "body"),
  addEditMawaidVenueType
);

router.delete(
  "/delete/:id",
  validate(deleteMawaidVenueTypeSchema, "params"),
  deleteMawaidVenueType
);

module.exports = router;
