const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  CUSTOM_ERROR,
  CUSTOM_SUCCESS,
  FETCH,
  DELETE_SUCCESS,
  NOT_FOUND,
} = require("../../../utils/message.util");
const {
  generatePreSignedURLs,
  uploadFileToS3,
  generateGetPreSignedURL,
} = require("../../../utils/aws.util");

const { ArazCityZone, ArazCityZoneFile } = require("../models");
const { isEmpty } = require("../../../utils/misc.util");

const statusTypes = {
  DRAFT: "DRAFT",
  APPROVED: "APPROVED",
  UNLOCKED: "UNLOCKED",
};

const venueTypes = {
  ZONES: "ZONES",
  WAAZ_VENUE: "WAAZ_VENUE",
};

const handleS3Upload = async (file, arazCityZoneID) => {
  const fileDetails = {
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  };

  const preSignedURLs = await generatePreSignedURLs(
    "araz_city_zone_files",
    arazCityZoneID,
    [fileDetails]
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return null;
  }

  const { fileKey, preSignedURL } = preSignedURLs[0];
  const uploadResult = await uploadFileToS3(file, preSignedURL, fileKey);

  if (!uploadResult) {
    return null;
  }

  return { fileKey, fileDetails };
};

const buildFileQuery = (arazCityZoneID, venueType, venueID, functionID) => {
  const query = {
    arazCityZoneID,
    venueType,
  };

  if (venueType === venueTypes.WAAZ_VENUE) {
    if (venueID) query.venueID = venueID;
    if (functionID) query.functionID = functionID;
  } else {
    query.venueID = { $exists: false };
    query.functionID = { $exists: false };
  }

  return query;
};

const validateZoneExists = async (arazCityZoneID) => {
  if (!arazCityZoneID) {
    return false;
  }

  const arazCityData = await ArazCityZone.findById(arazCityZoneID);
  return !!arazCityData;
};

const createFileDocument = (data, userId) => {
  const fileDocument = {
    arazCityZoneID: data.arazCityZoneID,
    fileData: {
      fileName: data.fileDetails.fileName,
      fileType: data.fileDetails.fileType,
      fileSize: data.fileDetails.fileSize,
      fileKey: data.fileKey,
    },
    venueType: data.venueType,
    userID: userId,
    status: data.status || statusTypes.DRAFT,
  };

  if (data.venueID) fileDocument.venueID = data.venueID;
  if (data.functionID) fileDocument.functionID = data.functionID;

  return fileDocument;
};

const getRecentFile = async (query) => {
  return await ArazCityZoneFile.findOne({
    ...query,
  }).sort({ createdAt: -1 });
};

const uploadFile = apiHandler(async (req, res) => {
  try {
    if (!req.file) {
      return apiError(CUSTOM_ERROR, "No file uploaded", null, res);
    }

    const { arazCityZoneID, functionID, venueID, venueType } = req.body;

    if (!(await validateZoneExists(arazCityZoneID))) {
      return apiResponse(CUSTOM_ERROR, "Invalid arazCityZoneID", {}, res);
    }

    const query = buildFileQuery(
      arazCityZoneID,
      venueType,
      venueID,
      functionID
    );
    const recentFile = await getRecentFile(query);

    if (recentFile?.status === statusTypes.APPROVED) {
      return apiResponse(
        CUSTOM_ERROR,
        "An approved file already exists. Please unlock it before uploading a new file.",
        { fileId: recentFile._id },
        res
      );
    }

    const uploadResult = await handleS3Upload(req.file, arazCityZoneID);
    if (!uploadResult) {
      return apiResponse(CUSTOM_ERROR, "Failed to upload file", {}, res);
    }

    const { fileKey, fileDetails } = uploadResult;

    let fileDocument;
    if (recentFile?.status === statusTypes.DRAFT) {
      recentFile.fileData = {
        fileName: fileDetails.fileName,
        fileType: fileDetails.fileType,
        fileSize: fileDetails.fileSize,
        fileKey: fileKey,
      };
      recentFile.userID = req.user._id;

      fileDocument = await recentFile.save();
    } else {
      const newFileData = createFileDocument(
        {
          arazCityZoneID,
          fileDetails,
          fileKey,
          venueType,
          venueID,
          functionID,
        },
        req.user._id
      );

      fileDocument = await ArazCityZoneFile.create(newFileData);
    }

    return apiResponse(
      CUSTOM_SUCCESS,
      "File Uploaded Successfully",
      fileDocument,
      res
    );
  } catch (error) {
    console.error("Error uploading file:", error);
    return apiResponse(CUSTOM_ERROR, "Failed to process file upload", {}, res);
  }
});

const updateFileStatus = apiHandler(async (req, res) => {
  try {
    const { fileId, status } = req.body;

    if (!fileId || !status) {
      return apiResponse(
        CUSTOM_ERROR,
        "FileId and status are required",
        {},
        res
      );
    }

    if (!Object.values(statusTypes).includes(status)) {
      return apiResponse(CUSTOM_ERROR, "Invalid status value", {}, res);
    }

    const existingFile = await ArazCityZoneFile.findById(fileId);
    if (!existingFile) {
      return apiResponse(CUSTOM_ERROR, "File not found", {}, res);
    }

    if (existingFile.status === status) {
      return apiResponse(
        CUSTOM_ERROR,
        `File is already in ${status} status`,
        {},
        res
      );
    }

    if (
      status === statusTypes.UNLOCKED &&
      existingFile.status !== statusTypes.APPROVED
    ) {
      return apiResponse(
        CUSTOM_ERROR,
        "Only files with 'approved' status can be changed to 'unlocked'",
        {},
        res
      );
    }

    if (status === statusTypes.APPROVED) {
      const query = buildFileQuery(
        existingFile.arazCityZoneID,
        existingFile.venueType,
        existingFile.venueID,
        existingFile.functionID
      );

      const recentFile = await ArazCityZoneFile.findOne({
        ...query,
        _id: { $ne: fileId },
      }).sort({ createdAt: -1 });

      if (recentFile?.status === statusTypes.APPROVED) {
        return apiResponse(
          CUSTOM_ERROR,
          "An approved file already exists. Please unlock it first.",
          { fileId: recentFile._id },
          res
        );
      }
    }

    const newFileData = createFileDocument(
      {
        arazCityZoneID: existingFile.arazCityZoneID,
        fileDetails: {
          fileName: existingFile.fileData.fileName,
          fileType: existingFile.fileData.fileType,
          fileSize: existingFile.fileData.fileSize,
        },
        fileKey: existingFile.fileData.fileKey,
        venueType: existingFile.venueType,
        venueID: existingFile.venueID,
        functionID: existingFile.functionID,
        status: status,
      },
      req.user._id
    );

    const newFileDocument = await ArazCityZoneFile.create(newFileData);

    return apiResponse(
      CUSTOM_SUCCESS,
      `File status updated to ${status}`,
      { fileDocument: newFileDocument },
      res
    );
  } catch (error) {
    console.error("Error updating file status:", error);
    return apiResponse(CUSTOM_ERROR, "Failed to update file status", {}, res);
  }
});

const buildQueryFromParams = (params) => {
  const { arazCityZoneID, functionID, venueID, venueType } = params;
  const query = {};

  if (arazCityZoneID) {
    query.arazCityZoneID = arazCityZoneID;
  }

  if (venueType) {
    query.venueType = venueType;

    if (venueType === venueTypes.WAAZ_VENUE) {
      if (venueID) {
        query.venueID = venueID;
      }

      if (functionID === "null") {
        query.functionID = { $exists: false };
      } else if (functionID) {
        query.functionID = functionID;
      }
    } else if (venueType === venueTypes.ZONES) {
      query.functionID = { $exists: false };
      query.venueID = { $exists: false };
    }
  }

  return query;
};

const getFiles = apiHandler(async (req, res) => {
  try {
    const { arazCityZoneID } = req.body;

    if (!arazCityZoneID) {
      return apiError(CUSTOM_ERROR, "arazCityZoneID is required", {}, res);
    }

    const query = buildQueryFromParams(req.body);
    const files = await ArazCityZoneFile.find(query)
      .sort({ createdAt: -1 })
      .populate("userID", "name email");

    const fileGroups = {};
    files.forEach((file) => {
      if (!fileGroups[file.fileData.fileKey]) {
        fileGroups[file.fileData.fileKey] = [];
      }
      fileGroups[file.fileData.fileKey].push(file);
    });

    return apiResponse(
      CUSTOM_SUCCESS,
      "Files retrieved successfully",
      { files },
      res
    );
  } catch (error) {
    console.error("Error retrieving files:", error);
    return apiResponse(CUSTOM_ERROR, "Failed to retrieve files", {}, res);
  }
});

const getMasterFiles = apiHandler(async (req, res) => {
  try {
    const query = buildQueryFromParams(req.query);
    query.status = statusTypes.APPROVED;

    const masterFiles = await ArazCityZoneFile.find(query)
      .sort({ updatedAt: -1 })
      .populate("userID", "name email");
    if (isEmpty(masterFiles)) {
      return apiError(NOT_FOUND, "Master file", null, res);
    }

    return apiResponse(
      CUSTOM_SUCCESS,
      "Master files retrieved successfully",
      { masterFiles },
      res
    );
  } catch (error) {
    console.error("Error retrieving master files:", error);
    return apiError(CUSTOM_ERROR, "Failed to retrieve master files", {}, res);
  }
});

const getDownloadURL = apiHandler(async (req, res) => {
  const { fileKey } = req.body;
  const preSignedURL = await generateGetPreSignedURL(fileKey);
  return apiResponse(FETCH, "Download URL", preSignedURL, res);
});

const deleteArazCityFile = apiHandler(async (req, res) => {
  const { id } = req.params;

  const fileData = await ArazCityZoneFile.findOneAndDelete({ _id: id });

  if (isEmpty(fileData)) return apiError(NOT_FOUND, "File", null, res);

  return apiResponse(DELETE_SUCCESS, "File", null, res);
});

module.exports = {
  uploadFile,
  updateFileStatus,
  getFiles,
  getMasterFiles,
  getDownloadURL,
  deleteArazCityFile,
};
