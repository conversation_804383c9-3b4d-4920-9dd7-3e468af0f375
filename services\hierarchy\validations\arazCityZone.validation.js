const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
} = require("../../../utils/validator.util");

const addArazCityZoneSchema = Joi.object({
  name: stringValidation,
  LDName: stringValidation.optional().allow(""),
  alias: stringValidation.optional().allow(""),
  ITSID: stringValidation.optional().allow(""),
  ITSName: stringValidation.optional().allow(""),
  arazCityID: idValidation.optional().allow(""),
}).unknown();

const getSingleArazCityZoneSchema = Joi.object({
  id: idValidation,
});

const editArazCityZoneSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
  LDName: stringValidation.optional().allow(""),
  alias: stringValidation.optional().allow(""),
  ITSID: stringValidation.optional().allow(""),
  ITSName: stringValidation.optional().allow(""),
  arazCityID: idValidation.optional().allow(""),
}).unknown();

const deleteArazCityZoneSchema = Joi.object({
  id: idValidation,
  arazCityID: idValidation,
});

const getArazCityZoneByArazCitySchema = Joi.object({
  id: idValidation,
});

const getArazCityZoneReportSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  arazCityZoneID: idValidation,
  kgTypes: Joi.array().items(stringValidation).optional(),
});

const getHODReportSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  kgTypes: Joi.array().items(stringValidation).optional(),
});

const statusTypes = ["DRAFT", "APPROVED", "UNLOCKED"];

const uploadFileSchema = Joi.object({
  arazCityZoneID: idValidation,
  arazCityID: idValidation,
  miqaatID:idValidation
});

const updateFileStatusSchema = Joi.object({
  fileId: idValidation,
  status: Joi.string()
    .valid(...statusTypes)
    .required(),
}).unknown();

const getFilesSchema = Joi.object({
  arazCityZoneID: idValidation,
  miqaatID:idValidation,
  arazCityID: idValidation,
}).unknown();

const getDownloadURLSchema = Joi.object({
  fileKey: stringValidation,
});

const deleteArazCityZoneFileSchema = Joi.object({
  id: idValidation,
});
const getMasterFilesSchema = Joi.object({
  arazCityZoneID: idValidation,
  miqaatID:idValidation,
  arazCityID: idValidation,
}).unknown();

module.exports = {
  addArazCityZoneSchema,
  getSingleArazCityZoneSchema,
  editArazCityZoneSchema,
  deleteArazCityZoneSchema,
  getArazCityZoneByArazCitySchema,
  getArazCityZoneReportSchema,
  getHODReportSchema,
  uploadFileSchema,
  updateFileStatusSchema,
  getMasterFilesSchema,
  getDownloadURLSchema,
  deleteArazCityZoneFileSchema,
  getFilesSchema,
};
