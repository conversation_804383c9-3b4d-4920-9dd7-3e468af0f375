const { Schema, model } = require("mongoose");
const aggregatePaginate = require("mongoose-aggregate-paginate-v2");
const { recipientMeta, statusTypes } = require("./message.model");

const attachmentSchema = new Schema(
  {
    fileName: {
      type: String,
      required: true,
    },
    fileType: {
      type: String,
      required: true,
    },
    fileSize: {
      type: Number,
      required: true,
    },
    fileKey: {
      type: String,
      required: true,
    },
  },
  { timestamps: true, _id: false }
);

const EmailSchema = new Schema({
  awsGroupID: {
    type: String,
  },
  miqaatID: {
    type: Schema.Types.ObjectId,
    ref: "Miqaat",
    required: true,
  },
  arazCityID: {
    type: Schema.Types.ObjectId,
    ref: "ArazCity",
    required: true,
  },
  sender: {
    type: Schema.Types.ObjectId,
    ref: "KGUser",
    required: true,
  },
  recipientType: {
    type: String,
    enum: recipientMeta.allTypes,
    required: true,
  },
  departmentIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "Department",
    },
  ],
  zoneIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
    },
  ],
  positionIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "HierarchyPosition",
    },
  ],
  kgGroupIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGGroup",
    },
  ],
  kgTypeIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGType",
    },
  ],
  ITSIDs: [
    {
      type: String,
    },
  ],
  recipients: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
  ],
  subject: {
    type: String,
    required: true,
  },
  body: {
    type: String,
    required: true,
  },
  attachments: [attachmentSchema],
  status: {
    type: String,
    enum: Object.values(statusTypes),
    default: statusTypes.PUBLISHED,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  isTopPriority: {
    type: Boolean,
    default: false,
  },
  isAllowReply: {
    type: Boolean,
    default: true,
  },
  messageType: {
    type: Schema.Types.ObjectId,
    ref: "MessageType",
  },
  systemRoleID: [
    {
      type: Schema.Types.ObjectId,
      ref: "SystemRole",
    },
  ],
  CCsystemRoleID: [
    {
      type: Schema.Types.ObjectId,
      ref: "SystemRole",
    },
  ],
  CCrecipientType: {
    type: String,
    enum: recipientMeta.allTypes,
    required: false,
  },
  CCdepartmentIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "Department",
    },
  ],
  CCzoneIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
    },
  ],
  CCpositionIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "HierarchyPosition",
    },
  ],
  CCkgGroupIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGGroup",
    },
  ],
  CCkgTypeIDs: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGType",
    },
  ],
  CCITSIDs: [
    {
      type: String,
    },
  ],
  CCrecipients: [
    {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
  ],
  isReadBy: {
    type: Map,
    of: Boolean,
    default: function () {
      const isReadMap = new Map();
      isReadMap.set(this.sender.toString(), true);
      this.recipients.forEach((userId) => {
        isReadMap.set(userId.toString(), false);
      });
      return isReadMap;
    },
  },
});

EmailSchema.plugin(aggregatePaginate);
const Email = model("Email", EmailSchema);

module.exports = { Email };
