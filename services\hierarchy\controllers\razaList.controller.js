const {
  validate,
  validateSchema,
} = require("../../../middlewares/validation.middleware");
const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const {
  FETCH,
  EXISTS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { toObjectId } = require("../../../utils/misc.util");
const { <PERSON>z<PERSON><PERSON>, CompileList, RazaList, KGUser } = require("../models");
const { addEditKGUserSchema } = require("../validations/kgUser.validation");
const {
  importITSUsersHelper,
  addEditKGUserFunc,
} = require("./kgUser.controller");

// Helper functions
const processCompileLists = async () => {
  const kgUsers = new Map(); // Using Map for O(1) lookup
  const duplicateKgUsers = [];

  const compileLists = await CompileList.find({
    approvedForRaza: true,
  }).populate("departments.kgUsers", "name LDName ITSID");

  compileLists.forEach((compileList) => {
    compileList.departments.forEach((department) => {
      department.kgUsers.forEach((kgUser) => {
        if (kgUsers.has(kgUser.ITSID)) {
          duplicateKgUsers.push({
            userID: kgUser._id,
            userITS: kgUser.ITSID,
            departmentID: department.departmentID,
            compileListName: compileList.name,
          });
        } else {
          kgUsers.set(kgUser.ITSID, {
            userID: kgUser._id,
            userITS: kgUser.ITSID,
            isMerged: false,
            isAlreadyExist: false,
            isError: false,
          });
        }
      });
    });
  });

  return {
    kgUsers: Array.from(kgUsers.values()),
    duplicateKgUsers,
  };
};

const processITSUsers = async (kgUsers) => {
  const ITSIDs = kgUsers.map((kgUser) => kgUser.userITS);
  const { data: ITSUsers } = await importITSUsersHelper({
    body: { ITS_IDs: ITSIDs },
  });

  const kgUsersMap = new Map(kgUsers.map((user) => [user.userITS, user]));

  return ITSUsers.map((ITSUser) => ({
    ...ITSUser,
    isMerged: kgUsersMap.get(ITSUser.ITSID).isMerged,
    isAlreadyExist: kgUsersMap.get(ITSUser.ITSID).isAlreadyExist,
    isError: kgUsersMap.get(ITSUser.ITSID).isError,
    error: kgUsersMap.get(ITSUser.ITSID).error,
  }));
};

const calculateTotals = (ITSUsers) => {
  return ITSUsers.reduce(
    (acc, user) => ({
      totalMerged: acc.totalMerged + (user.isMerged ? 1 : 0),
      totalUnmerged: acc.totalUnmerged + (user.isMerged ? 0 : 1),
    }),
    { totalMerged: 0, totalUnmerged: 0 }
  );
};

const getCompiledRazaList = apiHandler(async (req, res) => {
  const { miqaatID } = req.params;
  const objectMiqaatID = toObjectId(miqaatID);

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, null, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat City is not active", null, res);
  }

  // Parallel database queries for better performance
  let [fasalArazCity, razaList] = await Promise.all([
    ArazCity.findOne({
      miqaatID: objectMiqaatID,
      isFasal: true,
    }).select("name"),
    RazaList.findOne({ miqaatID: objectMiqaatID }),
  ]);

  // Create new razaList if it doesn't exist
  if (!razaList) {
    const { kgUsers, duplicateKgUsers } = await processCompileLists();
    razaList = await RazaList.create({
      miqaatID: objectMiqaatID,
      fasalArazCityID: fasalArazCity ? fasalArazCity._id : null,
      kgUsers,
      duplicateKgUsers,
      totalUniqueUsers: kgUsers.length,
    });
  }

  // Process ITS users
  const ITSUsers = await processITSUsers(razaList.kgUsers);
  const { totalMerged, totalUnmerged } = calculateTotals(ITSUsers);

  return apiResponse(
    FETCH,
    "Raza List",
    {
      totalMerged,
      totalUnmerged,
      buttonLabel: `Merge Users into ${
        fasalArazCity ? `${fasalArazCity.name} ` : ""
      }Hierarchy`,
      fasalArazCityID: fasalArazCity ? fasalArazCity._id : null,
      kgUsers: ITSUsers,
    },
    res
  );
});

const mergeUserIntoHierarchy = apiHandler(async (req, res) => {
  const {
    ITSID,
    miqaatID,
    arazCityID,
    hierarchyPositionID,
    departmentID,
    arazCityZoneID,
  } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const populateOptions = [
    { path: "miqaats.hierarchyPositionID", select: "name alias" },
    { path: "miqaats.departmentID", select: "name" },
    { path: "miqaats.arazCityZoneID", select: "name" },
    { path: "miqaats.functionID", select: "name" },
    { path: "miqaats.kgTypeID", select: "name" },
    { path: "miqaats.kgGroupID", select: "name" },
    { path: "miqaats.cityRoleID", select: "name" },
  ];

  const createUserResponse = (user, miqaat, isAlreadyExist = false) => ({
    isMerged: true,
    isAlreadyExist,
    ITSID,
    name: user.name,
    hierarchyPositionName: miqaat.hierarchyPositionID?.name || null,
    hierarchyPositionAliasName: miqaat.hierarchyPositionID?.alias || null,
    departmentName: miqaat.departmentID?.name || null,
    zoneName: miqaat.arazCityZoneID?.name || null,
    functionName: miqaat.functionID?.name || null,
    kgTypeName: miqaat.kgTypeID?.name || null,
    kgGroupName: miqaat.kgGroupID?.name || null,
    otherFunctionName: miqaat.otherFunction || null,
    permissionName: miqaat.cityRoleID?.name || null,
  });

  const updateRazaList = async (updateData) => {
    const updateFields = {};
    Object.keys(updateData).map(
      (field) => (updateFields[`kgUsers.$[user].${field}`] = updateData[field])
    );

    await RazaList.findOneAndUpdate(
      { miqaatID: toObjectId(miqaatID) },
      { $set: updateFields },
      { arrayFilters: [{ "user.userITS": ITSID }] }
    );
  };

  const validateResult = validateSchema(req, addEditKGUserSchema, "body");
  if (!validateResult.success) {
    await updateRazaList({
      error: validateResult.message,
      isError: true,
    });
    return apiError(CUSTOM_ERROR, validateResult.message, null, res);
  }

  // Check if user already exists in hierarchy
  const existingUser = await KGUser.findOne({
    ITSID,
    miqaats: {
      $elemMatch: {
        miqaatID: toObjectId(miqaatID),
        arazCityID: toObjectId(arazCityID),
        hierarchyPositionID: toObjectId(hierarchyPositionID),
        departmentID: departmentID ? toObjectId(departmentID) : null,
        arazCityZoneID: arazCityZoneID ? toObjectId(arazCityZoneID) : null,
      },
    },
  }).populate(populateOptions);

  if (existingUser) {
    const existingUserMiqaat = existingUser.miqaats.find(
      (miqaat) => miqaat.miqaatID.toString() === miqaatID
    );
    const response = createUserResponse(existingUser, existingUserMiqaat, true);

    await updateRazaList({
      isMerged: true,
      isAlreadyExist: true,
      isError: false,
      error: null,
    });

    return apiResponse(
      CUSTOM_SUCCESS,
      "User already exists in hierarchy",
      response,
      res
    );
  }

  // Try to add/edit user
  const result = await addEditKGUserFunc({
    ...req,
    body: {
      ...req.body,
      isAddedFromRazaList: true,
      consentRequired: false,
      consentAccepted: true,
    },
  });
  if (result && !result.success) {
    await updateRazaList({
      error: result.message,
      isError: true,
    });
    return apiError(CUSTOM_ERROR, result.message, null, res);
  }

  // Get updated user data
  const updatedUser = await KGUser.findOne({ ITSID }).populate(populateOptions);
  const updatedUserMiqaat = updatedUser.miqaats.find(
    (miqaat) => miqaat.miqaatID.toString() === miqaatID
  );
  const response = createUserResponse(updatedUser, updatedUserMiqaat);

  await updateRazaList({
    isMerged: true,
    isError: false,
    error: null,
  });

  return apiResponse(
    CUSTOM_SUCCESS,
    "User merged into hierarchy successfully",
    response,
    res
  );
});

module.exports = {
  getCompiledRazaList,
  mergeUserIntoHierarchy,
};
