const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  CUSTOM_ERROR,
  NOT_FOUND,
} = require("../../../utils/message.util");
const {
  checkSurveyEligibility,
} = require("../aggregations/surveyResponse.aggregation");
const {
  SurveyForm,
  SurveyResponse,
  surveyFormFrequencyList,
} = require("../models");
const { generateChartData } = require("./chart.controller");
const { getMember } = require("./surveyForm.controller");

const getEligibleSurveysForReports = apiHandler(async (req, res) => {
  try {
    const user = req.user;

    let inputDate = req.body.date;
    if (inputDate) {
      inputDate = inputDate.split("T")[0];
    }
    // const today = inputDate ? new Date(inputDate) : new Date();

    // const startOfToday = new Date(
    //   today.getFullYear(),
    //   today.getMonth(),
    //   today.getDate()
    // );
    // const endOfToday = new Date(
    //   today.getFullYear(),
    //   today.getMonth(),
    //   today.getDate(),
    //   23,
    //   59,
    //   59,
    //   999
    // );

    // Single DB call with all necessary populations
    const allSurveys = await SurveyForm.find({ isActive: true })
      .populate([
        "questions",
        "createdBy",
        "updatedBy",
        "department",
        // Populate all recipient criteria in one go
        {
          path: "recipients.criteria.miqaatID",
          select: "name _id",
        },
        {
          path: "recipients.criteria.arazCityID",
          select: "name _id",
        },
        {
          path: "recipients.criteria.departmentIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.functionIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.zoneIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.positionIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.kgGroupIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.kgTypeIDs",
          select: "name _id",
        },
        {
          path: "recipients.systemRoleIDs",
          select: "name _id",
        },
        {
          path: "recipients.systemUserIDs",
          select: "name _id",
        },
      ])
      .sort({ createdAt: -1 })
      .lean();

    const activeMiqaats = user.miqaats.filter(
      (m) => m.isActive && m.status === "ACCEPTED"
    );

    const eligibleSurveys = [];

    for (const survey of allSurveys) {
      const isEligible = await checkSurveyEligibility(
        survey,
        user,
        activeMiqaats,
        survey.reportRecipients
      );

      if (isEligible) {
        // Pass the already populated survey to avoid DB call
        const participantStats = await getSurveyParticipantStats(
          survey,
          inputDate
        );

        eligibleSurveys.push({
          ...survey,
          ...participantStats,
        });
      }
    }

    return apiResponse(FETCH, "Surveys", eligibleSurveys, res);
  } catch (error) {
    return apiError(
      CUSTOM_ERROR,
      "Error fetching eligible surveys",
      error,
      res
    );
  }
});

const getAllSurveysForReports = apiHandler(async (req, res) => {
  try {
    let inputDate = req.body.date;
    if (inputDate) {
      inputDate = inputDate.split("T")[0]; // "YYYY-MM-DD"
    }
    const matchDate = inputDate || new Date().toISOString().split("T")[0];

    console.log("Matching on date:", matchDate);

    const allSurveys = await SurveyForm.find({
      isActive: true,
      $or: [
        {
          $expr: {
            $eq: [
              { $dateToString: { format: "%Y-%m-%d", date: "$startDate" } },
              matchDate,
            ],
          },
        },
        { frequency: surveyFormFrequencyList.DAILY },
      ],
    })
      .populate([
        "questions",
        "createdBy",
        "updatedBy",
        "department",
        {
          path: "recipients.criteria.miqaatID",
          select: "name _id",
        },
        {
          path: "recipients.criteria.arazCityID",
          select: "name _id",
        },
        {
          path: "recipients.criteria.departmentIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.functionIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.zoneIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.positionIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.kgGroupIDs",
          select: "name _id",
        },
        {
          path: "recipients.criteria.kgTypeIDs",
          select: "name _id",
        },
        {
          path: "recipients.systemRoleIDs",
          select: "name _id",
        },
        {
          path: "recipients.systemUserIDs",
          select: "name _id",
        },
      ])
      .sort({ createdAt: -1 })
      .lean();

    // const today = inputDate ? new Date(inputDate) : new Date();
    // const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    // const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);

    const surveysWithStats = await Promise.all(
      allSurveys.map(async (survey) => {
        const participantStats = await getSurveyParticipantStats(
          survey,
          inputDate
        );
        return {
          ...survey,
          ...participantStats,
        };
      })
    );

    return apiResponse(FETCH, "Surveys", surveysWithStats, res);
  } catch (error) {
    return apiError(
      CUSTOM_ERROR,
      "Error fetching surveys with stats",
      error,
      res
    );
  }
});

// const getAllSurveysForReports = apiHandler(async (req, res) => {
//   try {
//     let inputDate = req.body.date;
//     if (inputDate) {
//       inputDate = inputDate.split("T")[0];
//     }
//     const today = inputDate ? new Date(inputDate) : new Date();
//     const startOfToday = new Date(
//       today.getFullYear(),
//       today.getMonth(),
//       today.getDate()
//     );
//     const endOfToday = new Date(
//       today.getFullYear(),
//       today.getMonth(),
//       today.getDate(),
//       23,
//       59,
//       59,
//       999
//     );
//     console.log("startOfToday",startOfToday)
//     console.log("endOfToday",endOfToday)

//     // Single DB call with all necessary populations
//     const allSurveys = await SurveyForm.find({
//       isActive: true,
//       $or: [
//         { startDate: { $gte: startOfToday, $lte: endOfToday } },
//         { frequency: surveyFormFrequencyList.DAILY },
//       ],
//     })
//       .populate([
//         "questions",
//         "createdBy",
//         "updatedBy",
//         "department",
//         // Populate all recipient criteria in one go
//         {
//           path: "recipients.criteria.miqaatID",
//           select: "name _id",
//         },
//         {
//           path: "recipients.criteria.arazCityID",
//           select: "name _id",
//         },
//         {
//           path: "recipients.criteria.departmentIDs",
//           select: "name _id",
//         },
//         {
//           path: "recipients.criteria.functionIDs",
//           select: "name _id",
//         },
//         {
//           path: "recipients.criteria.zoneIDs",
//           select: "name _id",
//         },
//         {
//           path: "recipients.criteria.positionIDs",
//           select: "name _id",
//         },
//         {
//           path: "recipients.criteria.kgGroupIDs",
//           select: "name _id",
//         },
//         {
//           path: "recipients.criteria.kgTypeIDs",
//           select: "name _id",
//         },
//         {
//           path: "recipients.systemRoleIDs",
//           select: "name _id",
//         },
//         {
//           path: "recipients.systemUserIDs",
//           select: "name _id",
//         },
//       ])
//       .sort({ createdAt: -1 })
//       .lean();

//     // Add participant stats to each survey - pass the populated survey
//     const surveysWithStats = await Promise.all(
//       allSurveys.map(async (survey) => {
//         const participantStats = await getSurveyParticipantStats(
//           survey,
//           startOfToday,
//           endOfToday
//         );
//         return {
//           ...survey,
//           ...participantStats,
//         };
//       })
//     );

//     return apiResponse(FETCH, "Surveys", surveysWithStats, res);
//   } catch (error) {
//     return apiError(
//       CUSTOM_ERROR,
//       "Error fetching surveys with stats",
//       error,
//       res
//     );
//   }
// });

const getSurveyParticipantStats = async (survey, inputDate) => {
  try {
    if (!survey || !survey.recipients) {
      return {
        totalParticipants: 0,
        respondedParticipants: 0,
        uniqueParticipants: 0,
        completionRate: 0,
        pendingParticipants: 0,
        totalResponses: 0,
      };
    }

    let surveyMembers = [];

    // Process criteria-based recipients
    if (survey.recipients.criteria && survey.recipients.criteria.length > 0) {
      for (const criteria of survey.recipients.criteria) {
        try {
          const memberQuery = {
            miqaatID: criteria.miqaatID?._id,
            arazCityID: criteria.arazCityID?._id,
            recipientType: criteria.recipientType,
            departmentIDs: criteria.departmentIDs?.map((dept) => dept._id),
            functionIDs: criteria.functionIDs?.map((func) => func._id),
            zoneIDs: criteria.zoneIDs?.map((zone) => zone._id),
            positionIDs: criteria.positionIDs?.map((pos) => pos._id),
            kgTypeIDs: criteria.kgTypeIDs?.map((type) => type._id),
            kgGroupIDs: criteria.kgGroupIDs?.map((group) => group._id),
            ITSIDs: criteria.ITSIDs,
          };

          const memberData = await getMember(memberQuery);

          if (memberData?.existingUsersData?.length > 0) {
            surveyMembers.push(...memberData.existingUsersData);
          }
        } catch (criteriaError) {
          console.error(`Error processing criteria:`, criteriaError);
        }
      }
    }

    // Process system role recipients
    if (survey.recipients.systemRoleIDs?.length > 0) {
      try {
        const systemRoleMembers = await getMember({
          systemRoles: survey.recipients.systemRoleIDs.map((role) => role._id),
        });

        if (systemRoleMembers?.existingUsersData?.length > 0) {
          surveyMembers.push(...systemRoleMembers.existingUsersData);
        }
      } catch (systemRoleError) {
        console.error("Error processing system roles:", systemRoleError);
      }
    }

    // Process direct system user recipients
    if (survey.recipients.systemUserIDs?.length > 0) {
      try {
        const systemUserMembers = await getMember({
          systemUserIDs: survey.recipients.systemUserIDs.map(
            (user) => user._id
          ),
        });

        if (systemUserMembers?.existingUsersData?.length > 0) {
          surveyMembers.push(...systemUserMembers.existingUsersData);
        }
      } catch (systemUserError) {
        console.error("Error processing system users:", systemUserError);
      }
    }

    // Remove duplicates based on user ID
    // const uniqueSurveyMembers = surveyMembers.filter(
    //   (member, index, self) =>
    //     index ===
    //     self.findIndex((m) => m._id.toString() === member._id.toString())
    // );

    // Create date filters for daily surveys
    let dateFilter = {};
    if (survey.frequency === surveyFormFrequencyList.DAILY) {
      const matchDate = inputDate || new Date().toISOString().split("T")[0];
      dateFilter = {
        $expr: {
          $eq: [
            { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
            matchDate,
          ],
        },
      };
    }

    // Base query for survey responses
    const baseQuery = { surveyID: survey._id };

    // Add date filter for daily surveys
    const responseQuery =
      survey.frequency === surveyFormFrequencyList.DAILY
        ? { ...baseQuery, ...dateFilter }
        : baseQuery;

    // Get survey responses count and unique respondents in parallel
    const [surveyResponsesCount, uniqueRespondents] = await Promise.all([
      SurveyResponse.countDocuments(responseQuery),
      // SurveyResponse.distinct("userID", responseQuery),
    ]);

    // let responseQuery = { surveyID: survey._id };

    // if (survey.frequency === surveyFormFrequencyList.DAILY) {
    //   const matchDate = new Date(startOfDay).toISOString().split("T")[0]; // get 'YYYY-MM-DD'

    //   responseQuery = {
    //     ...responseQuery,
    //     $expr: {
    //       $eq: [
    //         { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
    //         matchDate
    //       ]
    //     }
    //   };
    // };

    // const [surveyResponsesCount, uniqueRespondents] = await Promise.all([
    //   SurveyResponse.countDocuments(responseQuery),
    //   SurveyResponse.distinct("userID", responseQuery),
    // ]);

    console.log("surveyResponsesCount", surveyResponsesCount);

    // Calculate statistics
    const totalParticipants = surveyMembers.length;
    const uniqueParticipants = surveyResponsesCount;
    const completionRate =
      totalParticipants > 0
        ? ((uniqueParticipants / totalParticipants) * 100).toFixed(2)
        : 0;

    const stats = {
      totalParticipants,
      respondedParticipants: surveyResponsesCount,
      uniqueParticipants: surveyResponsesCount,
      completionRate: parseFloat(completionRate),
      pendingParticipants: totalParticipants - uniqueParticipants,
      totalResponses: surveyResponsesCount,
    };

    return stats;
  } catch (error) {
    console.error("Error getting survey participant stats:", error);
    return {
      totalParticipants: 0,
      respondedParticipants: 0,
      uniqueParticipants: 0,
      completionRate: 0,
      pendingParticipants: 0,
      totalResponses: 0,
    };
  }
};

const getSurveyResults = apiHandler(async (req, res) => {
  const { id } = req.params;
  const { date } = req.body;

  const survey = await SurveyForm.findById(id).populate("questions").lean();

  if (!survey) {
    return apiError(NOT_FOUND, "Survey", null, res);
  }

  const mappedQuestions = survey.questionMapping || [];

  // Fallback answer map: { questionID -> answerArray }
  const fallbackAnswersMap = {};

  // Step 1: Get active mapped-from surveys
  const mappedSurveyIDs = mappedQuestions.map((m) => m.surveyID);
  const activeMappedSurveys = await SurveyForm.find({
    _id: { $in: mappedSurveyIDs },
    isActive: true,
  })
    .select("_id")
    .lean();

  const activeSurveyIDSet = new Set(
    activeMappedSurveys.map((s) => s._id.toString())
  );

  // Step 2: Only include answers from active source surveys
  for (const mapping of mappedQuestions) {
    if (!activeSurveyIDSet.has(mapping.surveyID.toString())) continue;

    const query = {
      surveyID: mapping.surveyID,
      "answers.questionID": mapping.questionID,
      "answers.value": { $exists: true, $ne: [] },
    };

    if (date) {
      const dateOnly = new Date(date).toISOString().split("T")[0];
      const startOfDay = new Date(`${dateOnly}T00:00:00.000Z`);
      const endOfDay = new Date(`${dateOnly}T23:59:59.999Z`);
      query.updatedAt = { $gte: startOfDay, $lte: endOfDay };
    }

    const latestResponse = await SurveyResponse.findOne(query)
      .sort({ updatedAt: -1 })
      .lean();

    if (latestResponse) {
      const answer = latestResponse.answers.find(
        (a) => a.questionID.toString() === mapping.questionID.toString()
      );
      if (answer) {
        fallbackAnswersMap[mapping.questionID.toString()] = answer.value;
      }
    }
  }

  const targetDate = date || new Date().toISOString();
  const dateOnly = new Date(targetDate).toISOString().split("T")[0];
  const startOfDay = new Date(`${dateOnly}T00:00:00.000Z`);
  const endOfDay = new Date(`${dateOnly}T23:59:59.999Z`);

  let responses = await SurveyResponse.find({
    surveyID: id,
    createdAt: { $gte: startOfDay, $lte: endOfDay },
  })
    .populate("userID")
    .lean();

  if (responses.length === 0 && Object.keys(fallbackAnswersMap).length > 0) {
    // Create dummy user
    const dummyUser = { _id: null, name: "ams" };

    // Create dummy response structure
    const dummyAnswers = survey.questions.map((q) => {
      const qid = q._id.toString();
      const answers = fallbackAnswersMap[qid] || [];

      return {
        questionID: q._id,
        question: { ...q },
        answers,
        hasAnswer: answers.length > 0,
      };
    });

    responses = [
      {
        _id: "dummy",
        userID: dummyUser,
        answers: dummyAnswers,
        createdAt: new Date(),
      },
    ];
  }

  const results = responses.map((resp) => {
    const userAnswersMap = {};
    for (const ans of resp.answers) {
      userAnswersMap[ans.questionID.toString()] = ans.value;
    }

    const formattedAnswers = survey.questions.map((q) => {
      const qid = q._id.toString();

      const answers = userAnswersMap[qid]?.length
        ? userAnswersMap[qid]
        : fallbackAnswersMap[qid] || [];

      return {
        questionID: q._id,
        question: { ...q }, // Avoid reuse of populated object
        answers,
        hasAnswer: answers.length > 0,
      };
    });

    const totalQuestions = formattedAnswers.length;
    const answeredQuestions = formattedAnswers.filter(
      (a) => a.hasAnswer
    ).length;
    const completionPercentage = totalQuestions
      ? Math.round((answeredQuestions / totalQuestions) * 100)
      : 0;

    return {
      _id: resp._id,
      user: resp.userID,
      questions: survey.questions.map((q) => ({ ...q })), // clean clone
      response: { answers: formattedAnswers },
      createdAt: resp.createdAt,
      submittedAt: resp.createdAt,
      totalQuestions,
      answeredQuestions,
      completionPercentage,
    };
  });

  const responseData = {
    survey: {
      _id: survey._id,
      title: survey.title,
      name: survey.name,
      description: survey.description,
      frequency: survey.frequency,
      startTime: survey.startTime,
      endTime: survey.endTime,
      startDate: survey.startDate,
      endDate: survey.endDate,
      isActive: survey.isActive,
      createdBy: survey.createdBy,
      createdAt: survey.createdAt,
      updatedAt: survey.updatedAt,
    },
    users: results,
  };

  responseData.chartData = generateChartData(responseData);

  return apiResponse(FETCH, "Survey Results", responseData, res);
});

module.exports = {
  getEligibleSurveysForReports,
  getSurveyResults,
  getAllSurveysForReports,
};
