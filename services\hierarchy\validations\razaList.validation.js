const Joi = require("joi");
const { idValidation, stringValidation, booleanValidation } = require("../../../utils/validator.util");

const getCompiledRazaListSchema = Joi.object({
  miqaatID: idValidation
})

const mergeUserIntoHierarchySchema = Joi.object({
  name: stringValidation,
  ITSID: stringValidation,
  status: stringValidation,
  consentRequired: booleanValidation,
  isExists: booleanValidation.optional(),
  hierarchyPositionID: idValidation.allow("").optional(),
  kgGroupID: idValidation.allow("").optional(),
  kgTypeID: idValidation.allow("").optional(),
  jamiatID: idValidation,
  jamaatID: idValidation,
  arazCityID: idValidation,
  miqaatID: idValidation,
  arazCityZoneID: idValidation.allow("").optional(),
  departmentID: idValidation.allow("").optional(),
  functionID: idValidation.allow("").optional(),
  otherFunction: stringValidation.allow("").optional(),
  isInternationalPlugin: booleanValidation.optional(),
}).unknown();

module.exports = {
  getCompiledRazaListSchema,
  mergeUserIntoHierarchySchema
}