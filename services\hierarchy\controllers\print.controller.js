const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  CUSTOM_ERROR,
  UPDATE_SUCCESS,
} = require("../../../utils/message.util");
const { KGUser } = require("../models");
const constants = require("../../../constants");
const { toObjectId } = require("../../../utils/misc.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getList = async (req, res) => {
  try {
    const { miqaatID, arazCityID } = req.body;
    const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    const list = await getMember(req.body);
    return apiResponse(FETCH, "List", list, res);
  } catch (error) {
    console.error("Error in get list:", error);
    return apiError(CUSTOM_ERROR, "Failed to fetch kg users", null, res);
  }
};

const markAsPrinted = async (req, res) => {
  try {
    const { miqaatID, arazCityID } = req.body;

    // Validate required parameters
    if (!miqaatID || !arazCityID) {
      return apiError(
        CUSTOM_ERROR,
        "miqaatID and arazCityID are required",
        null,
        res
      );
    }
    const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    // First get the members based on the criteria
    const members = await getMember(req.body);

    if (!members || members.length === 0) {
      return apiResponse(
        UPDATE_SUCCESS,
        "No members found to mark as printed",
        [],
        res
      );
    }

    // Extract user IDs from the members list
    const userIds = members.map((member) => member._id);

    // Update the miqaats array elements to mark them as printed
    const updateResult = await KGUser.updateMany(
      {
        _id: { $in: userIds },
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.isActive": true,
      },
      {
        $set: {
          "miqaats.$[elem].isPrinted": true,
          "miqaats.$[elem].printedAt": new Date(),
          "miqaats.$[elem].printedBy": req.user?.id || req.user?._id, // Assuming user info is available in req.user
        },
      },
      {
        arrayFilters: [
          {
            "elem.arazCityID": toObjectId(arazCityID),
            "elem.miqaatID": toObjectId(miqaatID),
            "elem.isActive": true,
          },
        ],
      }
    );

    const response = {
      modifiedCount: updateResult.modifiedCount,
      matchedCount: updateResult.matchedCount,
      totalMembers: members.length,
      message: `${updateResult.modifiedCount} miqaat records marked as printed`,
    };

    return apiResponse(
      UPDATE_SUCCESS,
      "Members marked as printed",
      response,
      res
    );
  } catch (error) {
    console.error("Error in markAsPrinted:", error);
    return apiError(
      CUSTOM_ERROR,
      "Failed to mark members as printed",
      null,
      res
    );
  }
};

const unprintList = apiHandler(async (req, res) => {
  try {
    const { miqaatID, arazCityID, ITSIDs } = req.body;

    const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    // Validate required parameters
    if (!miqaatID || !arazCityID) {
      return apiError(
        CUSTOM_ERROR,
        "miqaatID and arazCityID are required",
        null,
        res
      );
    }

    let query = {
      "miqaats.arazCityID": toObjectId(arazCityID),
      "miqaats.miqaatID": toObjectId(miqaatID),
      "miqaats.isActive": true,
    };

    // If specific ITSIDs are provided, filter by them
    if (ITSIDs && ITSIDs.length > 0) {
      query.ITSID = { $in: ITSIDs };
    }

    // Update the miqaats array elements to unmark them as printed
    const updateResult = await KGUser.updateMany(
      query,
      {
        $unset: {
          "miqaats.$[elem].isPrinted": "",
          "miqaats.$[elem].printedAt": "",
          "miqaats.$[elem].printedBy": "",
        },
      },
      {
        arrayFilters: [
          {
            "elem.arazCityID": toObjectId(arazCityID),
            "elem.miqaatID": toObjectId(miqaatID),
            "elem.isActive": true,
          },
        ],
      }
    );

    const response = {
      modifiedCount: updateResult.modifiedCount,
      matchedCount: updateResult.matchedCount,
      requestedITSIDs: ITSIDs || [],
      message: `${updateResult.modifiedCount} miqaat records unmarked as printed`,
    };

    return apiResponse(
      UPDATE_SUCCESS,
      "List unmarked as printed",
      response,
      res
    );
  } catch (error) {
    console.error("Error in unprintList:", error);
    return apiError(
      CUSTOM_ERROR,
      "Failed to unmark list as printed",
      null,
      res
    );
  }
});

const getMember = async (data) => {
  const {
    miqaatID,
    arazCityID,
    memberType,
    departmentIDs,
    zoneIDs,
    positionIDs,
    kgTypeIDs,
    kgGroupIDs,
    ITSIDs,
  } = data;

  const query = {
    _id: { $ne: constants.AMS_SYSTEMID },
  };

  const orConditions = [];

  const miqaatCondition = {
    miqaats: {
      $elemMatch: {
        arazCityID: toObjectId(arazCityID),
        miqaatID: toObjectId(miqaatID),
        isActive: true,
        isPrinted: false,
      },
    },
  };

  switch (memberType) {
    case "ALL_IN_HIERARCHY":
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = {
        $ne: null,
      };
      break;

    case "ALL_IN_DEPARTMENT":
      miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
        $in: toObjectId(departmentIDs),
      };
      break;

    case "ALL_IN_ZONE":
      miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
        $in: toObjectId(zoneIDs),
      };
      break;

    case "ALL_DEPARTMENT_HOD":
      miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
        $in: toObjectId(departmentIDs),
      };
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.HOD[0].toString()
      );
      break;

    case "ALL_ZONE_HOD":
      miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
        $in: toObjectId(zoneIDs),
      };
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.ZONE_HEAD[0].toString()
      );
      break;

    case "BY_ITS":
      miqaatCondition["ITSID"] = { $in: ITSIDs };
      break;

    case "CUSTOM":
      if (departmentIDs && departmentIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["departmentID"] = {
          $in: toObjectId(departmentIDs),
        };
      }
      if (zoneIDs && zoneIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["arazCityZoneID"] = {
          $in: toObjectId(zoneIDs),
        };
      }
      if (positionIDs && positionIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = {
          $in: toObjectId(positionIDs),
        };
      }
      if (kgTypeIDs && kgTypeIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["kgTypeID"] = {
          $in: toObjectId(kgTypeIDs),
        };
      }
      if (kgGroupIDs && kgGroupIDs.length) {
        miqaatCondition.miqaats["$elemMatch"]["kgGroupID"] = {
          $in: toObjectId(kgGroupIDs),
        };
      }
      break;

    case "PMO":
      miqaatCondition.miqaats["$elemMatch"]["hierarchyPositionID"] = toObjectId(
        constants.HIERARCHY_POSITIONS.PMO[0].toString()
      );
      break;
  }

  if (Object.keys(miqaatCondition).length > 0) {
    orConditions.push(miqaatCondition);
  }

  if (orConditions.length > 1) {
    query.$or = orConditions;
  } else if (orConditions.length === 1) {
    Object.assign(query, orConditions[0]);
  }

  const existingUsers = await KGUser.find(query)
    .select(
      "_id name ITSID miqaats logo phone whatsapp gender LDName systemRoleID"
    )
    .populate({
      path: "miqaats.hierarchyPositionID",
      select: "name alias",
    })
    .populate({
      path: "miqaats.departmentID",
      select: "name LDName",
    })
    .populate({
      path: "miqaats.arazCityZoneID LDName",
      select: "name",
    })
    .populate({
      path: "miqaats.arazCityID",
      select: "name LDName showPositionAlias",
    })
    .populate({
      path: "miqaats.miqaatID",
      select: "name LDName",
    })
    .populate({
      path: "miqaats.kgTypeID",
      select: "name",
    })
    .populate({
      path: "miqaats.kgGroupID",
      select: "name",
    })
    .populate({
      path: "miqaats.functionID",
      select: "name",
    });

  const existingUsersData = existingUsers.map((user) => {
    const matchedMiqaat = user.miqaats.find((miqaat) => {
      const matchMiqaatID = miqaatID
        ? miqaat?.miqaatID?._id?.toString() === miqaatID.toString()
        : true;
      const matchArazCityID = arazCityID
        ? miqaat?.arazCityID?._id?.toString() === arazCityID.toString()
        : true;
      return matchMiqaatID && matchArazCityID;
    });

    return {
      _id: user._id,
      Name: user.name,
      LDName: user.LDName,
      ITS: user.ITSID,
      Photo: user.logo,
      phone: user.phone,
      whatsappNumber: user.whatsapp,
      gender: user.gender,
      hierarchyPosition: {
        _id: matchedMiqaat?.hierarchyPositionID?._id,
        name: matchedMiqaat?.hierarchyPositionID?.name,
        alias: matchedMiqaat?.hierarchyPositionID?.alias,
      },
      department: {
        _id: matchedMiqaat?.departmentID?._id,
        name: matchedMiqaat?.departmentID?.name,
        LDName: matchedMiqaat?.departmentID?.LDName,
      },
      khidmatZone: {
        name: matchedMiqaat?.miqaatHR?.MiqaatZone,
      },
      razaStatus:
        matchedMiqaat?.miqaatHR?.RazaStatus === "Has Raza" ? true : false,
      arazCityZone: {
        _id: matchedMiqaat?.arazCityZoneID?._id,
        name: matchedMiqaat?.arazCityZoneID?.name,
        LDName: matchedMiqaat?.arazCityZoneID?.LDName,
      },
      arazCity: {
        _id: matchedMiqaat?.arazCityID?._id,
        name: matchedMiqaat?.arazCityID?.name,
        LDName: matchedMiqaat?.arazCityID?.LDName,
      },
      miqaat: {
        _id: matchedMiqaat?.miqaatID?._id,
        name: matchedMiqaat?.miqaatID?.name,
        LDName: matchedMiqaat?.miqaatID?.LDName,
      },
      kgType: {
        _id: matchedMiqaat?.kgTypeID?._id,
        name: matchedMiqaat?.kgTypeID?.name,
      },
      kgGroup: {
        _id: matchedMiqaat?.kgGroupID?._id,
        name: matchedMiqaat?.kgGroupID?.name,
      },
      function: {
        _id: matchedMiqaat?.functionID?._id,
        name: matchedMiqaat?.functionID?.name,
      },
      showAlias: matchedMiqaat?.arazCityID?.showPositionAlias ? true : false,
      // Include print status information
      isPrinted: matchedMiqaat?.isPrinted || false,
      printedAt: matchedMiqaat?.printedAt || null,
      printedBy: matchedMiqaat?.printedBy || null,
    };
  });

  return existingUsersData;
};

module.exports = {
  getList,
  unprintList,
  markAsPrinted,
};
