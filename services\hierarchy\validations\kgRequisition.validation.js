const Joi = require("joi");
const { idValidation, stringValidation, numberValidation } = require("../../../utils/validator.util");

const createRequisitionSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  arazCityZoneID: idValidation,
  departmentID: idValidation,
  purpose: stringValidation.optional().allow(""),
  khidmatGuzaarCount: numberValidation,
});

const updateRequisitionSchema = Joi.object({
  id: idValidation,
  miqaatID: idValidation,
  arazCityID: idValidation,
  arazCityZoneID: idValidation,
  departmentID: idValidation,
  purpose: stringValidation.optional().allow(""),
  khidmatGuzaarCount: numberValidation,
});

const getSingleRequisitionSchema = Joi.object({
  id: idValidation,
});
const deleteRequisitionSchema = Joi.object({
  id: idValidation,
});

const assignRequisitionSchema = Joi.object({
  requisitionID: idValidation,
  applicantID: idValidation
});
const getListRequisitionSchema = Joi.object({
  arazCityID: idValidation,
  miqaatID: idValidation,     
});

module.exports = {
  createRequisitionSchema,
  updateRequisitionSchema,
  getSingleRequisitionSchema,
  getListRequisitionSchema,
  deleteRequisitionSchema,
  assignRequisitionSchema
};