const { Schema, model } = require("mongoose");

const KGArrivalInfoSchema = new Schema(
  {
    userID: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
      unique: true,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    recievedArrivalInfo: {
      type: Date,
      required: false,
    },
    modeOfTravel: {
      type: String,
      required: false,
      trim: true,
    },
    arrivalDate: {
      type: Date,
      required: false,
    },
    arrivalTime: {
      type: String,
      required: false,
      trim: true,
    },
    flightOrTrainNumber: {
      type: String,
      required: false,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

const KGArrivalInfo = model("KGArrivalInfo", KGArrivalInfoSchema);

module.exports = { KGArrivalInfo };
