const express = require("express");
const router = express.Router();
const { getList, markAsPrinted } = require("../controllers/print.controller");
const { validate } = require("../../../middlewares/validation.middleware");
const { getListSchema } = require("../validations/print.validation");

router.post("/get", validate(getListSchema, "body"), getList);

router.post("/get/markPrinted", validate(getListSchema, "body"), markAsPrinted);

module.exports = router;
