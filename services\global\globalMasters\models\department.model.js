const { Schema, model } = require("mongoose");

const departmentSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    LDName: {
      type: String,
      required: false,
      trim: true,
    },
    isZonal: {
      type: Boolean,
      default: true,
    },
    allowDeactivate: {
      type: Boolean,
      default: false,
    },
    priority: {
      type: Number,
      required: false,
    },
    status: {
      type: String,
      required: true,
      trim: true,
      enum: ["active", "inactive"],
      default: "active",
    },
    hierarchyPositions: [
      {
        type: Schema.Types.ObjectId,
        ref: "HierarchyPosition",
        required: false,
      },
    ],
    functions: [
      {
        type: Schema.Types.ObjectId,
        ref: "Function",
        required: false,
      },
    ],
    cityRoleID: {
      type: Schema.Types.ObjectId,
      ref: "SystemRole",
    },
    departmentQuota: {
      type: Number,
      default: 0,
    },
    smeUsers: [
      {
        type: Schema.Types.ObjectId,
        ref: "KGUser",
        required: false,
      },
    ],
    isSystem: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const Department = model("Department", departmentSchema);

module.exports = { Department };
