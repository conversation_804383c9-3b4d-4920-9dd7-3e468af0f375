const { Schema, model } = require("mongoose");

const statusTypes = {
  DRAFT: "DRAFT",
  APPROVED: "APPROVED",
  UNLOCKED: "UNLOCKED",
};

const venueTypes = {
  ZONES: "ZONES",
  WAAZ_VENUE: "WAAZ_VENUE",
  MAWAID_VENUE: "MAWAID_VENUE",
};

const arazCityZoneFileSchema = new Schema(
  {
    venueType: {
      type: String,
      required: true,
      enum: Object.values(venueTypes),
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
      required: true,
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      required: true,
    },
    venueID: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    functionID: {
      type: Schema.Types.ObjectId,
      ref: "globalFunction",
      required: false,
    },
    fileData: {
      _id: false,
      fileName: {
        type: String,
        required: true,
      },
      fileType: {
        type: String,
        required: true,
      },
      fileSize: {
        type: Number,
        required: true,
      },
      fileKey: {
        type: String,
        required: true,
      }
    },
    userID: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(statusTypes),
      default: statusTypes.DRAFT,
    }
  },
  {
    timestamps: true,
  }
);

const ArazCityZoneFile = model("ArazCityZoneFile", arazCityZoneFileSchema);

module.exports = { ArazCityZoneFile };