const express = require("express");
const router = express.Router();

const healthcheckRoute = require("./healthcheck.route");
const dashboardRoute = require("./dashboard.route");
const inboxRoute = require("./inbox.route");
const composeRoute = require("./compose.route");
const emailReportRoute = require("./emailReport.route");
const emailLogRoute = require("./emailLog.route");
const meeting = require("./metting.route");

router.use("/healthcheck", healthcheckRoute);
router.use("/dashboard", dashboardRoute);
router.use("/inbox", inboxRoute);
router.use("/compose", composeRoute);
router.use("/email-report", emailReportRoute);
router.use("/email-logs", emailLogRoute);
router.use("/meeting", meeting);

module.exports = router;
