const {
  api<PERSON><PERSON><PERSON>,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const {
  ADD_SUCCESS,
  FETCH,
  NOT_FOUND,
  CUSTOM_ERROR,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  CUSTOM_SUCCESS,
} = require("../../../utils/message.util");
const { isEmpty } = require("../../../utils/misc.util");
const { KgRequisition, KGUser } = require("../models");
const { Interest } = require("../models/interest.model");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const createRequisition = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const newRequisition = new KgRequisition({
    ...req.body,
    createdBy: req.user._id,
  });

  await newRequisition.save();

  return apiResponse(ADD_SUCCESS, "Requisition", null, res);
});

const getRequisitionDetails = apiHandler(async (req, res) => {
  const { id } = req.params;

  const requisition = await KgRequisition.findOne({ _id: id }).populate([
    "departmentID",
    "arazCityID",
    "createdBy",
    {
      path: "applications.applicant",
      populate: [{ path: "jamiatID" }, { path: "jamaatID" }],
    },
    "applications.assignedBy",
  ]);

  if (isEmpty(requisition)) {
    return apiError(NOT_FOUND, "Requisition Not Found", null, res);
  }

  const obj = requisition.toObject();

  if (obj.arazCityID) {
    obj.arazCityID = {
      _id: obj.arazCityID._id,
      name: obj.arazCityID.name,
      LDName: obj.arazCityID.LDName || "",
    };
  }

  if (obj.departmentID) {
    obj.departmentID = {
      _id: obj.departmentID._id,
      name: obj.departmentID.name,
      LDName: obj.departmentID.LDName || "",
    };
  }

  if (obj.createdBy) {
    obj.createdBy = {
      name: obj.createdBy.name,
      LDName: obj.createdBy.LDName || "",
      logo: obj.createdBy.logo,
      email: obj.createdBy.email,
      phone: obj.gender === "M" ? obj.createdBy.phone : "",
      whatsapp: obj.gender === "M" ? obj.createdBy.whatsapp : "",
    };
  }

  if (obj.applications && obj.applications.length > 0) {
    obj.applications = await Promise.all(
      obj.applications.map(async (application) => {
        // Initialize the fields with empty objects to ensure they always exist
        application.hierarchyPosition = { _id: "", name: "" };
        application.department = { _id: "", name: "" };
        application.arazCityZone = { _id: "", name: "" };
        application.miqaat = { _id: "", name: "" };

        if (application.applicant) {
          const interest = await Interest.findOne({
            userID: application.applicant._id,
          }).select("status");

          const kgUser = await KGUser.findById(
            application.applicant._id
          ).populate([
            {
              path: "miqaats.miqaatID",
            },
            {
              path: "miqaats.hierarchyPositionID",
            },
            {
              path: "miqaats.departmentID",
            },
            {
              path: "miqaats.arazCityZoneID",
            },
          ]);

          if (kgUser && kgUser.miqaats && kgUser.miqaats.length > 0) {
            const activeMiqaat = kgUser.miqaats.find(
              (m) =>
                m.miqaatID &&
                m.miqaatID._id.toString() === requisition.miqaatID.toString()
            );

            if (activeMiqaat) {
              // Update fields if they exist, keeping the empty objects as fallback
              if (activeMiqaat.miqaatID) {
                application.miqaat = {
                  _id: activeMiqaat.miqaatID._id || "",
                  name: activeMiqaat.miqaatID.name || "",
                };
              }

              if (activeMiqaat.hierarchyPositionID) {
                application.hierarchyPosition = {
                  _id: activeMiqaat.hierarchyPositionID._id || "",
                  name: activeMiqaat.hierarchyPositionID.name || "",
                };
              }

              if (activeMiqaat.departmentID) {
                application.department = {
                  _id: activeMiqaat.departmentID._id || "",
                  name: activeMiqaat.departmentID.name || "",
                };
              }

              if (activeMiqaat.arazCityZoneID) {
                application.arazCityZone = {
                  _id: activeMiqaat.arazCityZoneID._id || "",
                  name: activeMiqaat.arazCityZoneID.name || "",
                };
              }
            }
          }

          return {
            ...application,
            status: interest?.status || "",
            applicant: {
              _id: application.applicant._id,
              name: application.applicant.name,
              LDName: application.applicant.LDName,
              ITSID: application.applicant.ITSID || "",
              logo: application.applicant.logo || "",
              jamiatID: application.applicant.jamiatID,
              jamaatID: application.applicant.jamaatID,
              jamiat: application.applicant.jamiatID
                ? {
                    _id: application.applicant.jamiatID._id,
                    name: application.applicant.jamiatID.name,
                    LDName: application.applicant.jamiatID.LDName || "",
                  }
                : null,
              jamaat: application.applicant.jamaatID
                ? {
                    _id: application.applicant.jamaatID._id,
                    name: application.applicant.jamaatID.name,
                    LDName: application.applicant.jamaatID.LDName || "",
                  }
                : null,
              email: application.applicant.email,
              phone:
                application.applicant.gender === "M"
                  ? application.applicant.phone
                  : "",
              whatsapp:
                application.applicant.gender === "M"
                  ? application.applicant.whatsapp
                  : "",
            },
          };
        }
        return application;
      })
    );
  }

  return apiResponse(FETCH, "Requisition", obj, res);
});

const listRequisitions = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.query;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const requisition = await KgRequisition.find({ miqaatID, arazCityID })
    .populate("departmentID")
    .populate("arazCityID")
    .populate("arazCityZoneID")
    .populate("createdBy");

  const filteredReqs = requisition.map((req) => {
    const obj = req.toObject();

    if (obj.arazCityID) {
      obj.arazCityID = {
        _id: obj.arazCityID._id,
        name: obj.arazCityID.name,
        LDName: obj.arazCityID.LDName || "",
      };
    }

    if (obj.arazCityZoneID) {
      obj.arazCityZoneID = {
        _id: obj.arazCityZoneID._id,
        name: obj.arazCityZoneID.name,
        LDName: obj.arazCityZoneID.LDName || "",
      };
    }

    if (obj.departmentID) {
      obj.departmentID = {
        _id: obj.departmentID._id,
        name: obj.departmentID.name,
        LDName: obj.departmentID.LDName || "",
      };
    }

    if (obj.createdBy) {
      obj.createdBy = {
        name: obj.createdBy.name,
        LDName: obj.createdBy.LDName || "",
        logo: obj.createdBy.logo,
        email: obj.createdBy.email,
        phone: obj.gender === "M" ? obj.createdBy.phone : "",
        whatsapp: obj.gender === "M" ? obj.createdBy.whatsapp : "",
      };
    }

    // if (obj.applications && obj.applications.length > 0) {
    //   obj.applications = obj.applications.map(application => {
    //     if (application.applicant) {
    //       return {
    //         ...application,
    //         applicant: {
    //           _id: application.applicant._id,
    //           name: application.applicant.name,
    //           LDName: application.applicant.LDName,
    //           ITSID: application.applicant.ITSID || "",
    //           logo: application.applicant.logo || "",
    //           jamiat: application.applicant.jamiatID.name,
    //           jamaat: application.applicant.jamaatID.name,
    //           email: application.applicant.email,
    //           phone: application.applicant.gender === 'M' ? application.applicant.phone : '',
    //           whatsapp: application.applicant.gender === 'M' ? application.applicant.whatsapp : '',
    //         }
    //       };
    //     }
    //     return application;
    //   });
    // }
    if (obj.applications) {
      obj.applications = obj.applications?.length || 0;
    }

    return obj;
  });

  return apiResponse(FETCH, "Requisitions", filteredReqs, res);
});

const updateRequisition = apiHandler(async (req, res) => {
  const { id } = req.body;

  const reqData = await KgRequisition.findOneAndUpdate(
    { _id: id },
    { $set: { ...req.body } },
    { new: true }
  );

  if (isEmpty(reqData)) {
    return apiError(NOT_FOUND, "Requisition Not Found", null, res);
  }

  return apiResponse(UPDATE_SUCCESS, "Requisition", null, res);
});

const deleteRequisition = apiHandler(async (req, res) => {
  const { id } = req.params;

  const reqData = await KgRequisition.findOne({ _id: id });

  if (isEmpty(reqData)) {
    return apiError(NOT_FOUND, "Requisition Not Found", null, res);
  }

  const requisition = await KgRequisition.findByIdAndDelete(id);

  if (isEmpty(requisition)) {
    return apiError(NOT_FOUND, "Requisition Not Found", null, res);
  }

  return apiResponse(DELETE_SUCCESS, "Requisition", null, res);
});
const assignRequisition = apiHandler(async (req, res) => {
  const { applicantID, requisitionID } = req.body;
  const currentUser = req.user._id;

  const requisition = await KgRequisition.findById(requisitionID);

  if (!requisition) {
    return apiError(NOT_FOUND, "Requisition", null, res);
  }

  if (requisition.status === "Closed") {
    return apiError(CUSTOM_ERROR, "Requisition is already closed", null, res);
  }
  if (requisition.status === "Filled") {
    return apiError(CUSTOM_ERROR, "Requisition is already Filled", null, res);
  }

  const applicationIndex = requisition.applications.findIndex(
    (app) => app.applicant.toString() === applicantID
  );

  if (applicationIndex === -1) {
    return apiError(NOT_FOUND, "Applicant", null, res);
  }

  const updateResult = await KgRequisition.findOneAndUpdate(
    {
      _id: requisitionID,
      "applications.applicant": applicantID,
    },
    {
      $set: {
        "applications.$.status": "Assigned",
        "applications.$.assignedAt": new Date(),
        "applications.$.assignedBy": currentUser._id,
      },
      $inc: { assignedCount: 1 },
    },
    { new: true }
  );

  if (updateResult.assignedCount >= updateResult.khidmatGuzaarCount) {
    await KgRequisition.findByIdAndUpdate(requisitionID, { status: "Filled" });
  }

  return apiResponse(
    CUSTOM_SUCCESS,
    "Applicant successfully assigned",
    updateResult,
    res
  );
});
module.exports = {
  createRequisition,
  listRequisitions,
  getRequisitionDetails,
  updateRequisition,
  assignRequisition,
  deleteRequisition,
};
