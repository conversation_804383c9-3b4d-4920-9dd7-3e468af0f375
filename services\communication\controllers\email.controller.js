const {
  apiH<PERSON>ler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const { FETCH, NOT_FOUND } = require("../../../utils/message.util");
const { toObjectId, isEmpty } = require("../../../utils/misc.util");
const { Email } = require("../models");
const { EmailLog } = require("../models/emailLog.model");
const { statusTypes } = require("../models/message.model");
const { getUserData } = require("./message.controller");

const getEmails = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, page: pageNo, limit: limitNo } = req.body;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    null,
    req
  ) 
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat is not active", null, res);
  }

  const userID = req.user._id;
  const page = !pageNo || pageNo === 0 ? 1 : pageNo;
  const limit = !limitNo || limitNo === 0 ? 10 : limitNo;
  const skip = (page - 1) * limit;
  const isSystemUser = !isEmpty(req.user.systemRoleID) ? true : false;
  let findQuery = {
    miqaatID: toObjectId(miqaatID),
    // arazCityID: toObjectId(arazCityID),
    status: { $ne: statusTypes.DELETED },
  };
  if (!isSystemUser) {
    // findQuery["$or"] = [
    //   { sender: userID },
    //   { recipients: userID },
    //   { CCrecipients: userID },
    // ];
  }

  // Select and populate fields for user details
  const kgUserSelectFields =
    "name ITSID logo systemRoleID miqaats.miqaatID miqaats.arazCityID miqaats.arazCityZoneID miqaats.hierarchyPositionID miqaats.departmentID";
  const kgUserPopulateFields = [
    { path: "systemRoleID", select: "name" },
    { path: "miqaats.arazCityID", select: "_id name showPositionAlias" },
    { path: "miqaats.hierarchyPositionID", select: "name alias" },
    { path: "miqaats.arazCityZoneID", select: "name" },
    { path: "miqaats.departmentID", select: "name" },
  ];

  // Fetch emails and populate the necessary fields
  const existingEmailsCount = await Email.countDocuments(findQuery);
  const existingEmails = await Email.find(findQuery)
    .populate([
      {
        path: "sender",
        select: kgUserSelectFields,
        populate: kgUserPopulateFields,
      },
    ])
    .skip(skip)
    .limit(limit)
    .sort({ createdAt: -1 })
    .lean();
  if (!existingEmails.length) {
    return apiError(NOT_FOUND, "Emails", null, res);
  }

  // Transform emails into userData format
  let emailsData = existingEmails.map((email) => {
    const { sender } = email;

    const userData = getUserData(userID, sender, email);
    userData["_id"] = email._id;

    return {
      _id: email._id,
      subject: email.subject,
      status: email.status,
      from: userData.sender,
      position: userData.position.split("/")[0].trim(),
      to: "",
      toCount: email.recipients.length,
      ccCount: email.CCrecipients.length,
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
    };
  });

  const response = {
    count: existingEmailsCount,
    emails: emailsData,
  };

  return apiResponse(FETCH, "Email Logs", response, res);
});

const getEmail = apiHandler(async (req, res) => {
  const { id: messageID } = req.params;
  const userID = req.user._id; // logged-in userID
  const findQuery = {
    _id: toObjectId(messageID),
  };

  // Select and populate fields for user details
  const kgUserSelectFields =
    "name ITSID logo systemRoleID miqaats.miqaatID miqaats.arazCityID miqaats.arazCityZoneID miqaats.hierarchyPositionID miqaats.departmentID";
  const kgUserPopulateFields = [
    { path: "systemRoleID", select: "name" },
    { path: "miqaats.arazCityID", select: "name showPositionAlias" },
    { path: "miqaats.hierarchyPositionID", select: "name alias" },
    { path: "miqaats.arazCityZoneID", select: "name" },
    { path: "miqaats.departmentID", select: "name" },
  ];

  const email = await Email.findOne(findQuery)
    .populate([
      {
        path: "sender",
        select: kgUserSelectFields,
        populate: kgUserPopulateFields,
      },
      {
        path: "messageType",
        select: "_id name",
      },
    ])
    .lean();
  if (!email) {
    return apiError(NOT_FOUND, "Email", null, res);
  }

  // Prepare email details
  const { sender } = email;
  let userData = getUserData(userID, sender, email);
  userData = { ...email, ...userData };

  return apiResponse(FETCH, "Email", userData, res);
});

const getEmailRecipients = apiHandler(async (req, res) => {
  const { id } = req.params;

  const pipeline = [
    {
      $match: {
        emailID: toObjectId(id),
      },
    },
    {
      $lookup: {
        from: "emails",
        localField: "emailID",
        foreignField: "_id",
        as: "emailData",
      },
    },
    {
      $lookup: {
        from: "kgusers",
        localField: "recipientUserID",
        foreignField: "_id",
        as: "user",
      },
    },
    {
      $addFields: {
        user: { $arrayElemAt: ["$user", 0] },
        arazCityID: { $arrayElemAt: ["$emailData.arazCityID", 0] },
        miqaatID: { $arrayElemAt: ["$emailData.miqaatID", 0] },
        createdAt: { $arrayElemAt: ["$emailData.createdAt", 0] },
      },
    },
    {
      $unwind: "$user.miqaats"
    },
    {
      $match: {
        $expr: {
          $and: [
            { $eq: ["$user.miqaats.miqaatID", "$miqaatID"] },
            { $eq: ["$user.miqaats.arazCityID", "$arazCityID"] }
          ]
        }
      }
    },
    {
      $lookup: {
        from: "departments",
        localField: "user.miqaats.departmentID",
        foreignField: "_id",
        as: "departmentData"
      },
    },
    {
      $lookup: {
        from: "systemroles",
        localField: "user.miqaats.cityRoleID",
        foreignField: "_id",
        as: "systemPermissionData"
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "user.miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPositionData"
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "user.miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "zoneData"
      },
    },
    {
      $addFields: {
        "user.miqaats.department": { $arrayElemAt: ["$departmentData", 0] },
        "user.miqaats.systemPermission": { $arrayElemAt: ["$systemPermissionData", 0] },
        "user.miqaats.hierarchyPosition": { $arrayElemAt: ["$hierarchyPositionData", 0] },
        "user.miqaats.zone": { $arrayElemAt: ["$zoneData", 0] }
      }
    },
    {
      $group: {
        _id: "$_id",
        emailID: { $first: "$emailID" },
        recipientUserID: { $first: "$recipientUserID" },
        status: { $first: "$status" },
        receivedOn: { $first: "$receivedOn" },
        recipientType: { $first: "$recipientType" },
        isReplied: { $first: "$isReplied" },
        user: { $first: "$user" },
        arazCityID: { $first: "$arazCityID" },
        miqaatID: { $first: "$miqaatID" },
        createdAt: { $first: "$createdAt" },
        miqaats: { $push: "$user.miqaats" }
      }
    },
    {
      $addFields: {
        "user.miqaats": "$miqaats"
      }
    }
  ];
  const emailLogs = await EmailLog.aggregate(pipeline);
  const recipients = [];

  emailLogs.map((emailLog) => {
    const foundMiqaat = emailLog.user.miqaats[0]
    const recipient = {
      _id: emailLog.user._id,
      ITSID: emailLog.user.ITSID,
      name: emailLog.user.name,
      logo: emailLog.user.logo,
      department: foundMiqaat?.department?.name,
      systemPermission: foundMiqaat?.systemPermission?.name,
      zone: foundMiqaat?.zone?.name,
      hierarchyPosition: foundMiqaat?.hierarchyPosition?.name,
      status: emailLog.status,
      // receivedOn: emailLog.receivedOn,
      // recipientType: emailLog.recipientType,
      // isReplied: emailLog.isReplied,
    };

    recipients.push(recipient);
  });

  const response = {
    messageID: id,
    toRecipients: recipients,
    ccRecipients: [],
    sentAt: emailLogs[0]?.createdAt,
  }

  return apiResponse(FETCH, "Email Recipients", response, res);
});

module.exports = {
  getEmails,
  getEmail,
  getEmailRecipients,
};
