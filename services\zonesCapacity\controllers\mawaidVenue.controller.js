const {
  apiError,
  apiResponse,
  apiHandler,
} = require("../../../utils/api.util");
const {
  generatePreSignedURLs,
  uploadFileToS3,
  generateGetPreSignedURL,
} = require("../../../utils/aws.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const {
  SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_ERROR,
  NOT_FOUND,
  DELETE_SUCCESS,
  ADD_SUCCESS,
  CUSTOM_SUCCESS,
  FETCH,
} = require("../../../utils/message.util");
const { ArazCityZoneFile, ArazCityZone } = require("../../hierarchy/models");
const { MawaidVenue } = require("../models/mawaidVenue.model");

const generateUniqueName = (name) => {
  return name
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "_")
    .replace(/_+/g, "_");
};

const getAllMawaidVenues = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const mawaidVenues = await MawaidVenue.find({
    miqaatID: miqaatID,
    arazCityID: arazCityID,
  })
    .populate("waazVenueID", "name")
    .populate("miqaatID", "name")
    .populate("arazCityID", "name")
    .populate("arazCityZoneID", "name")
    .populate("mawaidVenueTypeID", "name")
    .populate("mawaidVenueSuitabilityID", "name")
    .lean();

  const formattedMawaidVenues = await Promise.all(
    mawaidVenues.map(async (venue) => {
      const venueDrawing = await ArazCityZoneFile.findOne({
        arazCityZoneID: venue.arazCityZoneID?._id,
        arazCityID: arazCityID,
        miqaatID: miqaatID,
        venueType: "ZONES",
        status: "APPROVED",
      })
        .sort({ createdAt: -1 })
        .limit(1);
      console.log(venueDrawing)
      return {
        _id: venue._id,
        mawaidVenueName: venue.name,
        zone: venue.arazCityZoneID?.name || "N/A",
        venueType: venue.mawaidVenueTypeID?.name || "N/A",
        venueSuitability: venue.mawaidVenueSuitabilityID?.name || "N/A",
        noOfThok: venue.numberOfThoks || 0,
        thaalPerThok: venue.plannedThoks || 0,
        estimatedCapacity: venue.estimatedCapacity || 0,
        finalCapacity: venue.finalCapacity || 0,
        isDrawing: !!venueDrawing,
        miqaat: venue.miqaatID?.name || "N/A",
        arazCity: venue.arazCityID?.name || "N/A",
        arazCityZoneID: venue.arazCityZoneID?._id || null,
        waazVenue: venue.waazVenueID?.name || "N/A",
      };
    })
  );

  return apiResponse(
    SUCCESS,
    "Mawaid venues retrieved successfully",
    formattedMawaidVenues,
    res
  );
});

const getSingleMawaidVenue = apiHandler(async (req, res) => {
  const { id } = req.params;

  const mawaidVenue = await MawaidVenue.findById(id)
    .populate("waazVenueID", "name")
    .populate("miqaatID", "name")
    .populate("arazCityID", "name")
    .populate("arazCityZoneID", "name")
    .populate("mawaidVenueTypeID", "name")
    .populate("mawaidVenueSuitabilityID", "name")
    .populate("createdBy", "name")
    .populate("updatedBy", "name")
    .lean();

  if (!mawaidVenue) {
    return apiError(NOT_FOUND, "Mawaid venue not found", null, res);
  }

  return apiResponse(
    SUCCESS,
    "Mawaid venue retrieved successfully",
    mawaidVenue,
    res
  );
});

const addEditMawaidVenue = apiHandler(async (req, res) => {
  const { _id } = req.body;
  const userId = req.user?._id;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    req.body.miqaatID,
    req.body.arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  if (req.body.name && !req.body.uniqueName) {
    req.body.uniqueName = generateUniqueName(req.body.name);
  }

  if (_id) {
    const existingVenue = await MawaidVenue.findById(_id);

    if (!existingVenue) {
      return apiError(NOT_FOUND, "Mawaid venue not found", null, res);
    }

    if (
      req.body.uniqueName &&
      req.body.uniqueName !== existingVenue.uniqueName
    ) {
      const duplicateVenue = await MawaidVenue.findOne({
        uniqueName: req.body.uniqueName,
        arazCityID: req.body.arazCityID,
        arazCityZoneID:req.body.arazCityZoneID,
        _id: { $ne: _id },
      });

      if (duplicateVenue) {
        return apiError(
          CUSTOM_ERROR,
          "Venue with this unique name already exists",
          null,
          res
        );
      }
    }

    if (req.body.name && req.body.name !== existingVenue.name) {
      const duplicateVenue = await MawaidVenue.findOne({
        name: req.body.name,
        arazCityID: req.body.arazCityID,
        arazCityZoneID:req.body.arazCityZoneID,
        _id: { $ne: _id },
      });

      if (duplicateVenue) {
        return apiError(
          CUSTOM_ERROR,
          "Venue with this name already exists",
          null,
          res
        );
      }
    }

    const updatedVenue = await MawaidVenue.findByIdAndUpdate(
      _id,
      {
        ...req.body,
        updatedBy: userId,
      },
      { new: true }
    );

    return apiResponse(UPDATE_SUCCESS, "Mawaid Venue", updatedVenue, res);
  } else {
    if (req.body.uniqueName) {
      const duplicateVenue = await MawaidVenue.findOne({
        uniqueName: req.body.uniqueName,
        arazCityID: req.body.arazCityID,
        arazCityZoneID:req.body.arazCityZoneID
      });

      if (duplicateVenue) {
        return apiError(
          CUSTOM_ERROR,
          "Venue with this name already exists",
          null,
          res
        );
      }
    }


    const newVenue = new MawaidVenue({
      ...req.body,
      createdBy: userId,
      updatedBy: userId,
    });

    const savedVenue = await newVenue.save();

    return apiResponse(ADD_SUCCESS, "Mawaid venue", savedVenue, res);
  }
});

const deleteMawaidVenue = apiHandler(async (req, res) => {
  const { id } = req.params;

  const mawaidVenue = await MawaidVenue.findById(id);

  if (!mawaidVenue) {
    return apiError(NOT_FOUND, "Mawaid venue", null, res);
  }

  await MawaidVenue.findByIdAndDelete(id);

  return apiResponse(
    DELETE_SUCCESS,
    "Mawaid venue deleted successfully",
    null,
    res
  );
});

const activateMawaidVenue = apiHandler(async (req, res) => {
  const { id, status } = req.body;

  const updateData = {};
  if (status !== undefined) updateData.status = status;
  const mawaidVenue = await MawaidVenue.findOneAndUpdate(
    { _id: id },
    updateData
  );

  if (!mawaidVenue) {
    return apiError(NOT_FOUND, "Mawaid venue", null, res);
  }

  return apiResponse(UPDATE_SUCCESS, "Mawaid venue", null, res);
});

const finalCapacityMawaidVenue = apiHandler(async (req, res) => {
  const { id, finalCapacity } = req.body;
  const mawaidVenue = await MawaidVenue.findOneAndUpdate(
    { _id: id },
    { finalCapacity }
  );

  if (!mawaidVenue) {
    return apiError(NOT_FOUND, "Mawaid venue", null, res);
  }

  return apiResponse(UPDATE_SUCCESS, "Mawaid venue", null, res);
});

const statusTypes = {
  DRAFT: "DRAFT",
  APPROVED: "APPROVED",
  UNLOCKED: "UNLOCKED",
};

const venueTypes = {
  MAWAID_VENUE: "MAWAID_VENUE",
};

const handleS3Upload = async (file, arazCityZoneID) => {
  const fileDetails = {
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  };

  const preSignedURLs = await generatePreSignedURLs(
    "araz_city_mawaid_venue_files",
    arazCityZoneID,
    [fileDetails]
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return null;
  }

  const { fileKey, preSignedURL } = preSignedURLs[0];
  const uploadResult = await uploadFileToS3(file, preSignedURL, fileKey);

  if (!uploadResult) {
    return null;
  }

  return { fileKey, fileDetails };
};

const validateZoneExists = async (arazCityZoneID) => {
  if (!arazCityZoneID) {
    return false;
  }

  const arazCityData = await ArazCityZone.findById(arazCityZoneID);
  return !!arazCityData;
};

const createFileDocument = (data, userId) => {
  return {
    arazCityZoneID: data.arazCityZoneID,
    arazCityID: data.arazCityID,
    fileData: {
      fileName: data.fileDetails.fileName,
      fileType: data.fileDetails.fileType,
      fileSize: data.fileDetails.fileSize,
      fileKey: data.fileKey,
    },
    venueType: venueTypes.MAWAID_VENUE,
    functionID: data.functionID,
    venueID: data.venueID,
    miqaatID: data.miqaatID,
    userID: userId,
    status: data.status || statusTypes.DRAFT,
  };
};

const getRecentFile = async (query) => {
  return await ArazCityZoneFile.findOne({
    ...query,
  }).sort({ createdAt: -1 });
};

const uploadMawaidVenueFile = apiHandler(async (req, res) => {
  if (!req.file) {
    return apiError(CUSTOM_ERROR, "No file uploaded", null, res);
  }

  const { arazCityZoneID, functionID, miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const venueID = req.body.mawaidVenueID;

  if (!(await validateZoneExists(arazCityZoneID))) {
    return apiResponse(CUSTOM_ERROR, "Invalid arazCityZoneID", null, res);
  }

  const recentFile = await getRecentFile({
    arazCityZoneID,
    arazCityID,
    functionID,
    venueID,
    venueType: venueTypes.MAWAID_VENUE,
    miqaatID,
  });

  if (recentFile?.status === statusTypes.APPROVED) {
    return apiResponse(
      CUSTOM_ERROR,
      "An approved file already exists. Please unlock it before uploading a new file.",
      { fileId: recentFile._id },
      res
    );
  }

  const uploadResult = await handleS3Upload(req.file, arazCityZoneID);
  if (!uploadResult) {
    return apiResponse(CUSTOM_ERROR, "Failed to upload file", null, res);
  }

  const { fileKey, fileDetails } = uploadResult;

  let fileDocument;
  if (recentFile?.status === statusTypes.DRAFT) {
    recentFile.fileData = {
      fileName: fileDetails.fileName,
      fileType: fileDetails.fileType,
      fileSize: fileDetails.fileSize,
      fileKey: fileKey,
    };
    recentFile.userID = req.user._id;

    fileDocument = await recentFile.save();
  } else {
    const newFileData = createFileDocument(
      {
        arazCityZoneID,
        arazCityID,
        fileDetails,
        fileKey,
        functionID,
        venueID,
        miqaatID,
      },
      req.user._id
    );

    fileDocument = await ArazCityZoneFile.create(newFileData);
  }

  return apiResponse(
    CUSTOM_SUCCESS,
    "MawaidVenue File Uploaded Successfully",
    fileDocument,
    res
  );
});

const updateMawaidVenueFileStatus = apiHandler(async (req, res) => {
  const { fileId, status } = req.body;

  if (!fileId || !status) {
    return apiResponse(
      CUSTOM_ERROR,
      "FileId and status are required",
      null,
      res
    );
  }

  if (!Object.values(statusTypes).includes(status)) {
    return apiResponse(CUSTOM_ERROR, "Invalid status value", null, res);
  }

  const existingFile = await ArazCityZoneFile.findById(fileId);
  if (!existingFile) {
    return apiResponse(CUSTOM_ERROR, "File not found", null, res);
  }

  // Ensure we're only handling MAWAID_VENUE type files
  if (existingFile.venueType !== venueTypes.MAWAID_VENUE) {
    return apiResponse(
      CUSTOM_ERROR,
      "Only MAWAID_VENUE type files can be handled",
      null,
      res
    );
  }

  if (existingFile.status === status) {
    return apiResponse(
      CUSTOM_ERROR,
      `File is already in ${status} status`,
      null,
      res
    );
  }

  if (
    status === statusTypes.UNLOCKED &&
    existingFile.status !== statusTypes.APPROVED
  ) {
    return apiResponse(
      CUSTOM_ERROR,
      "Only files with 'approved' status can be changed to 'unlocked'",
      null,
      res
    );
  }

  if (status === statusTypes.APPROVED) {
    const query = {
      arazCityZoneID: existingFile.arazCityZoneID,
      arazCityID: existingFile.arazCityID,
      functionID: existingFile.functionID,
      venueID: existingFile.venueID,
      venueType: venueTypes.MAWAID_VENUE,
      miqaatID: existingFile.miqaatID,
    };

    const recentFile = await ArazCityZoneFile.findOne({
      ...query,
      _id: { $ne: fileId },
    }).sort({ createdAt: -1 });

    if (recentFile && recentFile.status === statusTypes.APPROVED) {
      return apiResponse(
        CUSTOM_ERROR,
        "An approved file already exists. Please unlock it first.",
        { fileId: recentFile._id },
        res
      );
    }
  }

  const newFileData = createFileDocument(
    {
      arazCityZoneID: existingFile.arazCityZoneID,
      arazCityID: existingFile.arazCityID,
      fileDetails: {
        fileName: existingFile.fileData.fileName,
        fileType: existingFile.fileData.fileType,
        fileSize: existingFile.fileData.fileSize,
      },
      fileKey: existingFile.fileData.fileKey,
      functionID: existingFile.functionID,
      venueID: existingFile.venueID,
      miqaatID: existingFile.miqaatID,
      status: status,
    },
    req.user._id
  );

  const newFileDocument = await ArazCityZoneFile.create(newFileData);

  return apiResponse(
    CUSTOM_SUCCESS,
    `MawaidVenue file status updated to ${status}`,
    { fileDocument: newFileDocument },
    res
  );
});

const getMawaidVenueFiles = apiHandler(async (req, res) => {
  const { arazCityZoneID, functionID, miqaatID, arazCityID } = req.body;
  const venueID = req.body.mawaidVenueID;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const query = {
    arazCityZoneID,
    arazCityID,
    venueType: venueTypes.MAWAID_VENUE,
    miqaatID,
  };

  if (venueID) query.venueID = venueID;
  if (functionID) query.functionID = functionID;

  const files = await ArazCityZoneFile.find(query)
    .sort({ createdAt: 1 })
    .populate("userID", "name email");

  return apiResponse(
    CUSTOM_SUCCESS,
    "MawaidVenue files retrieved successfully",
    { files },
    res
  );
});

const getMawaidVenueMasterFiles = apiHandler(async (req, res) => {
  const { arazCityZoneID, miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const venueID = req.body.mawaidVenueID;
  const query = {
    status: statusTypes.APPROVED,
    arazCityZoneID,
    arazCityID,
    venueType: "ZONES",
    miqaatID
  };

  if (venueID) query.venueID = venueID;

  const masterFiles = await ArazCityZoneFile.findOne(query)
    .sort({ updatedAt: -1 })
    .populate("userID", "name email");

  if (!masterFiles || masterFiles.length === 0) {
    return apiError(NOT_FOUND, "MawaidVenue master file", null, res);
  }

  return apiResponse(
    CUSTOM_SUCCESS,
    "MawaidVenue master files retrieved successfully",
    { masterFiles },
    res
  );
});

const getMawaidVenueFileDownloadURL = apiHandler(async (req, res) => {
  const { fileKey } = req.body;

  if (!fileKey) {
    return apiResponse(CUSTOM_ERROR, "File key is required", null, res);
  }

  const preSignedURL = await generateGetPreSignedURL(fileKey);

  return apiResponse(
    FETCH,
    "MawaidVenue file download URL generated",
    { preSignedURL },
    res
  );
});

const deleteMawaidVenueFile = apiHandler(async (req, res) => {
  const { id } = req.params;
  const currentUserId = req.user._id;

  const file = await ArazCityZoneFile.findById(id);

  if (!file) {
    return apiError(NOT_FOUND, "MawaidVenue file", null, res);
  }

  if (file.venueType !== venueTypes.MAWAID_VENUE) {
    return apiResponse(
      CUSTOM_ERROR,
      "Only MAWAID_VENUE type files can be deleted using this endpoint",
      null,
      res
    );
  }

  if (file.status === statusTypes.APPROVED) {
    return apiResponse(
      CUSTOM_ERROR,
      "Approved files cannot be deleted. Please unlock the file first.",
      null,
      res
    );
  }

  if (file.userID.toString() !== currentUserId.toString()) {
    return apiResponse(
      CUSTOM_ERROR,
      "You can only delete files that you have uploaded",
      null,
      res
    );
  }

  const fileData = await ArazCityZoneFile.findByIdAndDelete(id);

  return apiResponse(DELETE_SUCCESS, "MawaidVenue file", null, res);
});

module.exports = {
  uploadMawaidVenueFile,
  updateMawaidVenueFileStatus,
  getMawaidVenueFiles,
  getMawaidVenueMasterFiles,
  getMawaidVenueFileDownloadURL,
  deleteMawaidVenueFile,
  getAllMawaidVenues,
  getSingleMawaidVenue,
  addEditMawaidVenue,
  deleteMawaidVenue,
  activateMawaidVenue,
  finalCapacityMawaidVenue,
};
